<?php
/**
 * Notification Service
 * 
 * This service handles the actual sending of notifications via different channels
 * (email, SMS, push, toast) and integrates with various providers.
 */
class NotificationService {
    private $db;
    private $notificationModel;
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get NotificationModel instance (lazy loading)
     * 
     * @return NotificationModel
     */
    private function getNotificationModel() {
        if (!$this->notificationModel) {
            // Load NotificationModel if not already loaded
            if (!class_exists('NotificationModel')) {
                $appRoot = defined('APPROOT') ? APPROOT : dirname(dirname(__FILE__));
                require_once $appRoot . '/models/NotificationModel.php';
            }
            $this->notificationModel = new NotificationModel();
        }
        return $this->notificationModel;
    }
    
    /**
     * Get UserModel instance (lazy loading)
     * 
     * @return UserModel
     */
    private function getUserModel() {
        if (!$this->userModel) {
            // Load UserModel if not already loaded
            if (!class_exists('UserModel')) {
                $appRoot = defined('APPROOT') ? APPROOT : dirname(dirname(__FILE__));
                require_once $appRoot . '/models/UserModel.php';
            }
            $this->userModel = new UserModel();
        }
        return $this->userModel;
    }
    
    /**
     * Process pending notifications
     * 
     * @param int $limit Maximum number of notifications to process
     * @return array Processing results
     */
    public function processPendingNotifications($limit = 100) {
        if (DEBUG_MODE) {
            error_log("NotificationService: Processing pending notifications (limit: $limit)");
        }
        
        $notifications = $this->getNotificationModel()->getPendingNotifications($limit);
        $results = [
            'processed' => 0,
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        foreach ($notifications as $notification) {
            $results['processed']++;
            
            try {
                $success = $this->sendNotification($notification);
                
                if ($success) {
                    $this->getNotificationModel()->markNotificationSent($notification->id);
                    $results['sent']++;
                    
                    if (DEBUG_MODE) {
                        error_log("NotificationService: Sent {$notification->notification_type} notification to user {$notification->user_id}");
                    }
                } else {
                    $this->handleFailedNotification($notification, 'Failed to send notification');
                    $results['failed']++;
                }
            } catch (Exception $e) {
                $this->handleFailedNotification($notification, $e->getMessage());
                $results['failed']++;
                $results['errors'][] = "Notification {$notification->id}: " . $e->getMessage();
                
                if (DEBUG_MODE) {
                    error_log("NotificationService: Error sending notification {$notification->id}: " . $e->getMessage());
                }
            }
        }
        
        if (DEBUG_MODE) {
            error_log("NotificationService: Processing complete. Sent: {$results['sent']}, Failed: {$results['failed']}");
        }
        
        return $results;
    }
    
    /**
     * Send a single notification
     * 
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function sendNotification($notification) {
        switch ($notification->notification_type) {
            case 'email':
                return $this->sendEmailNotification($notification);
            case 'sms':
                return $this->sendSmsNotification($notification);
            case 'push':
                return $this->sendPushNotification($notification);
            case 'toast':
                return $this->sendToastNotification($notification);
            default:
                throw new Exception("Unknown notification type: {$notification->notification_type}");
        }
    }
    
    /**
     * Send email notification
     * 
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function sendEmailNotification($notification) {
        if (empty($notification->email)) {
            throw new Exception("No email address for user {$notification->user_id}");
        }
        
        $settings = $this->getNotificationModel()->getNotificationSettings();
        
        if (!$settings['email_enabled']) {
            if (DEBUG_MODE) {
                error_log("NotificationService: Email notifications are disabled globally");
            }
            return false;
        }
        
        $fromEmail = $settings['email_from_address'] ?? '<EMAIL>';
        $fromName = $settings['email_from_name'] ?? 'Rowan Elite Rides Events';
        
        // Use PHP's mail function or a more sophisticated email service
        $headers = [
            'From' => "$fromName <$fromEmail>",
            'Reply-To' => $fromEmail,
            'Content-Type' => 'text/plain; charset=UTF-8',
            'X-Mailer' => 'Events System v' . APP_VERSION
        ];
        
        $headerString = '';
        foreach ($headers as $key => $value) {
            $headerString .= "$key: $value\r\n";
        }
        
        $success = mail(
            $notification->email,
            $notification->subject,
            $notification->message,
            $headerString
        );
        
        if (DEBUG_MODE) {
            error_log("NotificationService: Email sent to {$notification->email} - " . ($success ? 'Success' : 'Failed'));
        }
        
        return $success;
    }
    
    /**
     * Send SMS notification
     * 
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function sendSmsNotification($notification) {
        if (empty($notification->phone)) {
            throw new Exception("No phone number for user {$notification->user_id}");
        }
        
        $settings = $this->getNotificationModel()->getNotificationSettings();
        
        if (!$settings['sms_enabled']) {
            if (DEBUG_MODE) {
                error_log("NotificationService: SMS notifications are disabled globally");
            }
            return false;
        }
        
        $provider = $this->getNotificationModel()->getDefaultSmsProvider();
        if (!$provider) {
            throw new Exception("No active SMS provider configured");
        }
        
        return $this->sendSmsViaProvider($notification, $provider);
    }
    
    /**
     * Send SMS via specific provider
     * 
     * @param object $notification Notification data
     * @param object $provider SMS provider configuration
     * @return bool Success status
     */
    private function sendSmsViaProvider($notification, $provider) {
        $config = json_decode($provider->configuration, true);
        $phone = $this->formatPhoneNumber($notification->phone);
        $message = $notification->subject . "\n\n" . $notification->message;
        
        switch ($provider->provider_key) {
            case 'twilio':
                return $this->sendTwilioSms($phone, $message, $config);
            case 'textmagic':
                return $this->sendTextMagicSms($phone, $message, $config);
            case 'nexmo':
                return $this->sendNexmoSms($phone, $message, $config);
            case 'clicksend':
                return $this->sendClickSendSms($phone, $message, $config);
            case 'plivo':
                return $this->sendPlivoSms($phone, $message, $config);
            default:
                throw new Exception("Unsupported SMS provider: {$provider->provider_key}");
        }
    }
    
    /**
     * Send SMS via Twilio
     * 
     * @param string $phone Phone number
     * @param string $message Message content
     * @param array $config Provider configuration
     * @return bool Success status
     */
    private function sendTwilioSms($phone, $message, $config) {
        if (empty($config['account_sid']) || empty($config['auth_token']) || empty($config['from_number'])) {
            throw new Exception("Twilio configuration incomplete");
        }
        
        $url = "https://api.twilio.com/2010-04-01/Accounts/{$config['account_sid']}/Messages.json";
        
        $data = [
            'From' => $config['from_number'],
            'To' => $phone,
            'Body' => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $config['account_sid'] . ':' . $config['auth_token']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if (DEBUG_MODE) {
            error_log("NotificationService: Twilio SMS response (HTTP $httpCode): $response");
        }
        
        return $httpCode >= 200 && $httpCode < 300;
    }
    
    /**
     * Send SMS via TextMagic
     * 
     * @param string $phone Phone number
     * @param string $message Message content
     * @param array $config Provider configuration
     * @return bool Success status
     */
    private function sendTextMagicSms($phone, $message, $config) {
        if (empty($config['username']) || empty($config['api_key'])) {
            throw new Exception("TextMagic configuration incomplete");
        }
        
        $url = "https://rest.textmagic.com/api/v2/messages";
        
        $data = [
            'text' => $message,
            'phones' => $phone
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $config['username'] . ':' . $config['api_key']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if (DEBUG_MODE) {
            error_log("NotificationService: TextMagic SMS response (HTTP $httpCode): $response");
        }
        
        return $httpCode >= 200 && $httpCode < 300;
    }
    
    /**
     * Send SMS via Nexmo/Vonage
     * 
     * @param string $phone Phone number
     * @param string $message Message content
     * @param array $config Provider configuration
     * @return bool Success status
     */
    private function sendNexmoSms($phone, $message, $config) {
        if (empty($config['api_key']) || empty($config['api_secret']) || empty($config['from'])) {
            throw new Exception("Nexmo configuration incomplete");
        }
        
        $url = "https://rest.nexmo.com/sms/json";
        
        $data = [
            'api_key' => $config['api_key'],
            'api_secret' => $config['api_secret'],
            'from' => $config['from'],
            'to' => $phone,
            'text' => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if (DEBUG_MODE) {
            error_log("NotificationService: Nexmo SMS response (HTTP $httpCode): $response");
        }
        
        $responseData = json_decode($response, true);
        return $httpCode >= 200 && $httpCode < 300 && isset($responseData['messages'][0]['status']) && $responseData['messages'][0]['status'] == '0';
    }
    
    /**
     * Send SMS via ClickSend
     * 
     * @param string $phone Phone number
     * @param string $message Message content
     * @param array $config Provider configuration
     * @return bool Success status
     */
    private function sendClickSendSms($phone, $message, $config) {
        if (empty($config['username']) || empty($config['api_key'])) {
            throw new Exception("ClickSend configuration incomplete");
        }
        
        $url = "https://rest.clicksend.com/v3/sms/send";
        
        $data = [
            'messages' => [
                [
                    'to' => $phone,
                    'body' => $message,
                    'from' => 'Events'
                ]
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $config['username'] . ':' . $config['api_key']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if (DEBUG_MODE) {
            error_log("NotificationService: ClickSend SMS response (HTTP $httpCode): $response");
        }
        
        return $httpCode >= 200 && $httpCode < 300;
    }
    
    /**
     * Send SMS via Plivo
     * 
     * @param string $phone Phone number
     * @param string $message Message content
     * @param array $config Provider configuration
     * @return bool Success status
     */
    private function sendPlivoSms($phone, $message, $config) {
        if (empty($config['auth_id']) || empty($config['auth_token']) || empty($config['src'])) {
            throw new Exception("Plivo configuration incomplete");
        }
        
        $url = "https://api.plivo.com/v1/Account/{$config['auth_id']}/Message/";
        
        $data = [
            'src' => $config['src'],
            'dst' => $phone,
            'text' => $message
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $config['auth_id'] . ':' . $config['auth_token']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if (DEBUG_MODE) {
            error_log("NotificationService: Plivo SMS response (HTTP $httpCode): $response");
        }
        
        return $httpCode >= 200 && $httpCode < 300;
    }
    
    /**
     * Send push notification
     *
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function sendPushNotification($notification) {
        $settings = $this->getNotificationModel()->getNotificationSettings();

        if (!$settings['push_enabled']) {
            if (DEBUG_MODE) {
                error_log("NotificationService: Push notifications are disabled globally");
            }
            return false;
        }

        // Store push notification in database for fallback
        $stored = $this->storePushNotification($notification);

        // Actually send the push notification to browsers
        $pushSent = $this->sendActualPushNotification($notification);

        if (DEBUG_MODE) {
            error_log("NotificationService: Push notification stored: " . ($stored ? 'yes' : 'no') . ", browser push: " . ($pushSent ? 'sent' : 'failed'));
        }

        // Return true if either stored successfully OR sent to browser
        // This ensures we don't lose notifications but also mark as successful if delivered
        return $stored || $pushSent;
    }
    
    /**
     * Send toast notification
     * 
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function sendToastNotification($notification) {
        $settings = $this->getNotificationModel()->getNotificationSettings();
        
        if (!$settings['toast_enabled']) {
            if (DEBUG_MODE) {
                error_log("NotificationService: Toast notifications are disabled globally");
            }
            return false;
        }
        
        // Store toast notification for display when user next visits the site
        $this->storeToastNotification($notification);
        
        if (DEBUG_MODE) {
            error_log("NotificationService: Toast notification stored for user {$notification->user_id}");
        }
        
        return true;
    }
    
    /**
     * Store push notification for browser display
     *
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function storePushNotification($notification) {
        try {
            $this->db->beginTransaction();

            // Store in user_push_notifications table
            $this->db->query('INSERT INTO user_push_notifications
                              (user_id, title, message, event_id, event_type, is_read, created_at)
                              VALUES (:user_id, :title, :message, :event_id, :event_type, 0, NOW())');

            $this->db->bind(':user_id', $notification->user_id);
            $this->db->bind(':title', $notification->subject);
            $this->db->bind(':message', $notification->message);
            $this->db->bind(':event_id', $notification->event_id);
            $this->db->bind(':event_type', $notification->event_type);

            if (!$this->db->execute()) {
                throw new Exception("Failed to store push notification");
            }

            $pushNotificationId = $this->db->lastInsertId();

            // Also create notification center item
            require_once APPROOT . '/models/UnifiedMessageModel.php';
            $unifiedMessageModel = new UnifiedMessageModel();

            $notificationCenterId = $unifiedMessageModel->createNotificationItem(
                $notification->user_id,
                'push',
                $notification->subject,
                $notification->message,
                'user_push_notifications',
                $pushNotificationId,
                '/notification_center',
                'View',
                [
                    'event_id' => $notification->event_id,
                    'event_type' => $notification->event_type
                ]
            );

            if (!$notificationCenterId) {
                throw new Exception("Failed to create notification center item");
            }

            // Update push notification with notification center ID
            $this->db->query('UPDATE user_push_notifications SET notification_center_id = :nc_id WHERE id = :id');
            $this->db->bind(':nc_id', $notificationCenterId);
            $this->db->bind(':id', $pushNotificationId);
            $this->db->execute();

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("NotificationService::storePushNotification - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Store toast notification for display
     * 
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function storeToastNotification($notification) {
        $this->db->query('INSERT INTO user_toast_notifications 
                          (user_id, title, message, event_id, event_type, is_read, created_at) 
                          VALUES (:user_id, :title, :message, :event_id, :event_type, 0, NOW())');
        
        $this->db->bind(':user_id', $notification->user_id);
        $this->db->bind(':title', $notification->subject);
        $this->db->bind(':message', $notification->message);
        $this->db->bind(':event_id', $notification->event_id);
        $this->db->bind(':event_type', $notification->event_type);
        
        return $this->db->execute();
    }

    /**
     * Send actual push notification to browsers
     *
     * @param object $notification Notification data
     * @return bool Success status
     */
    private function sendActualPushNotification($notification) {
        try {
            // Get user's push subscriptions
            $subscriptions = $this->getNotificationModel()->getUserPushSubscriptions($notification->user_id);

            if (empty($subscriptions)) {
                if (DEBUG_MODE) {
                    error_log("NotificationService: No push subscriptions found for user {$notification->user_id}");
                }
                return false;
            }

            // Prepare notification payload
            $payload = json_encode([
                'title' => $notification->subject,
                'body' => $notification->message,
                'icon' => '/public/images/icons/icon-192x192.png',
                'badge' => '/public/images/icons/badge-72x72.png',
                'tag' => 'notification-' . time(),
                'requireInteraction' => false,
                'data' => [
                    'url' => '/notification_center',
                    'event_id' => $notification->event_id ?? null,
                    'event_type' => $notification->event_type ?? 'general'
                ]
            ]);

            $successCount = 0;
            $totalCount = count($subscriptions);

            foreach ($subscriptions as $subscription) {
                $sent = $this->sendPushToSubscription($subscription, $payload);
                if ($sent) {
                    $successCount++;
                }
            }

            if (DEBUG_MODE) {
                error_log("NotificationService: Push sent to {$successCount}/{$totalCount} subscriptions for user {$notification->user_id}");
            }

            return $successCount > 0;

        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationService: Push notification error: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Send push notification to a specific subscription using FCM
     *
     * @param object $subscription Push subscription data
     * @param string $payload JSON payload
     * @return bool Success status
     */
    private function sendPushToSubscription($subscription, $payload) {
        try {
            // Check if this is an FCM token or endpoint
            $fcmToken = null;
            
            if (isset($subscription->fcm_token) && !empty($subscription->fcm_token)) {
                // Direct FCM token
                $fcmToken = $subscription->fcm_token;
            } elseif (isset($subscription->endpoint) && strpos($subscription->endpoint, 'fcm.googleapis.com') !== false) {
                // Extract FCM token from endpoint
                require_once APPROOT . '/helpers/fcm_v1_helper.php';
                $fcmToken = FCMv1Helper::extractFCMToken($subscription->endpoint);
            }
            
            if (!$fcmToken) {
                if (DEBUG_MODE) {
                    error_log("NotificationService: No FCM token found in subscription");
                }
                return false;
            }
            
            // Use FCM v1 API
            $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
            if (!file_exists($serviceAccountPath)) {
                if (DEBUG_MODE) {
                    error_log("NotificationService: Firebase service account file not found");
                }
                return false;
            }
            
            require_once APPROOT . '/helpers/fcm_v1_helper.php';
            $fcm = new FCMv1Helper($serviceAccountPath);
            
            // Parse the payload to get notification data
            $payloadData = json_decode($payload, true);
            
            // Send FCM notification
            $result = $fcm->sendNotification(
                $fcmToken, 
                $payloadData['title'] ?? 'Notification',
                $payloadData['body'] ?? 'You have a new notification',
                $payloadData['data'] ?? []
            );
            
            if (DEBUG_MODE) {
                error_log("NotificationService: FCM notification result: " . ($result ? 'success' : 'failed'));
                error_log("NotificationService: FCM token: " . substr($fcmToken, 0, 20) . "...");
            }
            
            return $result;

        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationService: Error sending FCM push: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Generate VAPID JWT token for authentication
     * Simplified version that works with most push services
     *
     * @param string $endpoint Push endpoint URL
     * @param string $subject VAPID subject (email or URL)
     * @param string $publicKey VAPID public key (unused but kept for compatibility)
     * @param string $privateKey VAPID private key
     * @return string|false JWT token or false on failure
     */
    private function generateVAPIDToken($endpoint, $subject, $publicKey, $privateKey) {
        try {
            // For now, return a simple token that works with Firebase
            // This is a temporary solution - for production, implement proper VAPID JWT

            // Parse endpoint to get audience
            $parsedUrl = parse_url($endpoint);
            $audience = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];

            // Create a simple JWT-like token
            $header = $this->base64UrlEncode(json_encode(['typ' => 'JWT', 'alg' => 'ES256']));
            $payload = $this->base64UrlEncode(json_encode([
                'aud' => $audience,
                'exp' => time() + 43200, // 12 hours
                'sub' => $subject
            ]));

            // For Firebase, we can use a simplified signature
            // This is not cryptographically secure but works for testing
            $signature = $this->base64UrlEncode(hash('sha256', $header . '.' . $payload . $privateKey, true));

            return $header . '.' . $payload . '.' . $signature;

        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationService: Error generating VAPID token: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Base64url encode
     */
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Base64url decode
     */
    private function base64UrlDecode($data) {
        return base64_decode(strtr($data, '-_', '+/') . str_repeat('=', (4 - strlen($data) % 4) % 4));
    }



    /**
     * Handle failed notification
     * 
     * @param object $notification Notification data
     * @param string $errorMessage Error message
     * @return void
     */
    private function handleFailedNotification($notification, $errorMessage) {
        $maxAttempts = $this->getNotificationModel()->getNotificationSettings('max_notification_attempts') ?? 3;
        
        if ($notification->attempts + 1 >= $maxAttempts) {
            // Mark as permanently failed
            $this->getNotificationModel()->markNotificationFailed($notification->id, $errorMessage);
        } else {
            // Schedule retry
            $retryInterval = $this->getNotificationModel()->getNotificationSettings('notification_retry_interval') ?? 30;
            $retryTime = new DateTime();
            $retryTime->add(new DateInterval('PT' . $retryInterval . 'M'));
            
            $this->db->query('UPDATE notification_queue SET 
                              attempts = attempts + 1,
                              last_attempt = NOW(),
                              scheduled_for = :retry_time,
                              error_message = :error_message,
                              updated_at = NOW()
                              WHERE id = :id');
            
            $this->db->bind(':id', $notification->id);
            $this->db->bind(':retry_time', $retryTime->format('Y-m-d H:i:s'));
            $this->db->bind(':error_message', $errorMessage);
            $this->db->execute();
        }
    }
    
    /**
     * Format phone number for SMS sending
     * 
     * @param string $phone Raw phone number
     * @return string Formatted phone number
     */
    private function formatPhoneNumber($phone) {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if not present (assuming US)
        if (strlen($phone) === 10) {
            $phone = '1' . $phone;
        }
        
        // Add + prefix for international format
        if (!str_starts_with($phone, '+')) {
            $phone = '+' . $phone;
        }
        
        return $phone;
    }
    
    /**
     * Test notification sending
     * 
     * @param int $userId User ID
     * @param string $type Notification type
     * @param string $subject Test subject
     * @param string $message Test message
     * @return bool Success status
     */
    public function sendTestNotification($userId, $type, $subject, $message) {
        // Get user data
        $user = $this->getUserModel()->getUserById($userId);
        
        if (!$user) {
            throw new Exception("User not found");
        }
        
        // Create test notification object
        $notification = (object) [
            'id' => 0,
            'user_id' => $userId,
            'notification_type' => $type,
            'subject' => $subject,
            'message' => $message,
            'email' => $user->email,
            'phone' => $user->phone,
            'user_name' => $user->name,
            'event_id' => 0,
            'event_type' => 'test'
        ];
        
        return $this->sendNotification($notification);
    }
    
    /**
     * Send test email
     * 
     * @param string $email Email address
     * @param string $message Test message
     * @return bool Success status
     */
    public function sendTestEmail($email, $message) {
        // Create test notification object for email
        $notification = (object) [
            'id' => 0,
            'user_id' => 0,
            'notification_type' => 'email',
            'subject' => 'Test Email Notification',
            'message' => $message,
            'email' => $email,
            'phone' => '',
            'user_name' => 'Test User',
            'event_id' => 0,
            'event_type' => 'test'
        ];
        
        return $this->sendNotification($notification);
    }
    
    /**
     * Send test SMS
     * 
     * @param string $phone Phone number
     * @param string $message Test message
     * @return bool Success status
     */
    public function sendTestSms($phone, $message) {
        // Create test notification object for SMS
        $notification = (object) [
            'id' => 0,
            'user_id' => 0,
            'notification_type' => 'sms',
            'subject' => 'Test SMS Notification',
            'message' => $message,
            'email' => '',
            'phone' => $phone,
            'user_name' => 'Test User',
            'event_id' => 0,
            'event_type' => 'test'
        ];
        
        return $this->sendNotification($notification);
    }
    
    /**
     * Send test push notification
     * 
     * @param int $userId User ID
     * @param string $title Notification title
     * @param string $message Test message
     * @return bool Success status
     */
    public function sendTestPushNotification($userId, $title, $message) {
        // Get user data
        $user = $this->getUserModel()->getUserById($userId);
        
        if (!$user) {
            throw new Exception("User not found");
        }
        
        // Create test notification object for push
        $notification = (object) [
            'id' => 0,
            'user_id' => $userId,
            'notification_type' => 'push',
            'subject' => $title,
            'message' => $message,
            'email' => $user->email,
            'phone' => $user->phone,
            'user_name' => $user->name,
            'event_id' => 0,
            'event_type' => 'test'
        ];
        
        return $this->sendNotification($notification);
    }
    
    /**
     * Send test toast notification
     * 
     * @param int $userId User ID
     * @param string $title Notification title
     * @param string $message Test message
     * @return bool Success status
     */
    public function sendTestToastNotification($userId, $title, $message) {
        // Get user data
        $user = $this->getUserModel()->getUserById($userId);
        
        if (!$user) {
            throw new Exception("User not found");
        }
        
        // Create test notification object for toast
        $notification = (object) [
            'id' => 0,
            'user_id' => $userId,
            'notification_type' => 'toast',
            'subject' => $title,
            'message' => $message,
            'email' => $user->email,
            'phone' => $user->phone,
            'user_name' => $user->name,
            'event_id' => 0,
            'event_type' => 'test'
        ];
        
        return $this->sendNotification($notification);
    }
    
    /**
     * Send push notification via FCM
     * 
     * @param object $notification Push notification data
     * @return bool Success status
     */
    public function sendPushNotificationViaFCM($notification) {
        try {
            // Get user's FCM subscriptions
            $subscriptions = $this->getNotificationModel()->getUserPushSubscriptions($notification->user_id);
            
            if (empty($subscriptions)) {
                if (DEBUG_MODE) {
                    error_log("NotificationService::sendPushNotificationViaFCM - No FCM subscriptions found for user {$notification->user_id}");
                }
                return false;
            }
            
            $successCount = 0;
            $totalSubscriptions = count($subscriptions);
            
            // Prepare notification payload
            $payload = json_encode([
                'title' => $notification->title ?? $notification->subject ?? 'Notification',
                'body' => $notification->message ?? 'You have a new notification',
                'icon' => '/public/images/icon-192x192.png',
                'badge' => '/public/images/icon-72x72.png',
                'data' => [
                    'notificationId' => $notification->id,
                    'eventId' => $notification->event_id ?? null,
                    'eventType' => $notification->event_type ?? null,
                    'url' => $notification->url ?? '/'
                ]
            ]);
            
            // Send to each subscription
            foreach ($subscriptions as $subscription) {
                $result = $this->sendPushToSubscription($subscription, $payload);
                if ($result) {
                    $successCount++;
                }
            }
            
            if (DEBUG_MODE) {
                error_log("NotificationService::sendPushNotificationViaFCM - Sent to $successCount/$totalSubscriptions subscriptions for notification {$notification->id}");
            }
            
            // Consider it successful if at least one subscription worked
            return $successCount > 0;
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationService::sendPushNotificationViaFCM - Error: " . $e->getMessage());
            }
            return false;
        }
    }
}
