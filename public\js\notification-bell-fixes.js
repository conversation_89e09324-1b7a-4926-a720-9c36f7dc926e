/**
 * Notification Bell Icon Fixes
 * 
 * This script fixes the notification bell icon issues:
 * 1. Desktop bell count flashing and disappearing
 * 2. Mobile bell not showing count and not turning yellow
 * 
 * Runs after all other scripts to ensure it overrides any conflicts
 */

(function() {
    'use strict';
    
    console.log('[BellFixes] Loading notification bell fixes...');
    
    // Configuration
    const CONFIG = {
        MOBILE_BELL_COLOR: '#FFD700', // Bright yellow
        MOBILE_BELL_GLOW: '0 0 8px rgba(255, 215, 0, 0.6)',
        UPDATE_INTERVAL: 30000, // 30 seconds
        INITIAL_DELAY: 2000 // 2 seconds to let other scripts load
    };
    
    // State tracking
    let isInitialized = false;
    let updateInterval = null;
    let lastKnownCount = 0;
    
    /**
     * Enhanced badge update function that overrides all other systems
     */
    function updateNotificationBadges(count) {
        console.log(`[BellFixes] Updating badges with count: ${count}`);
        
        // Update desktop badge
        updateDesktopBadge(count);
        
        // Update mobile badge and bell color
        updateMobileBadge(count);
        
        // Store last known count
        lastKnownCount = count;
    }
    
    /**
     * Update desktop notification badge with aggressive protection
     */
    function updateDesktopBadge(count) {
        const badge = document.getElementById('notification-count');
        if (!badge) {
            console.warn('[BellFixes] Desktop badge element not found');
            return;
        }

        if (count > 0) {
            // Set all possible display properties to ensure visibility
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.style.display = 'inline-block !important';
            badge.style.visibility = 'visible !important';
            badge.style.opacity = '1 !important';
            badge.style.position = 'absolute';
            badge.style.zIndex = '9999';
            badge.setAttribute('data-count', count);

            // Force the element to stay visible by setting up a protection observer
            protectBadgeVisibility(badge, count);

            // Add pulse animation
            badge.classList.add('badge-pulse');
            setTimeout(() => {
                badge.classList.remove('badge-pulse');
            }, 1000);

            console.log(`[BellFixes] Desktop badge updated: ${count} (protected)`);
        } else {
            badge.style.display = 'none';
            badge.setAttribute('data-count', '0');
            console.log('[BellFixes] Desktop badge hidden');
        }
    }

    /**
     * Protect badge visibility from being overridden
     */
    function protectBadgeVisibility(badge, count) {
        // Create a MutationObserver to watch for style changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const currentDisplay = badge.style.display;
                    const currentVisibility = badge.style.visibility;
                    const currentOpacity = badge.style.opacity;

                    // If something tries to hide the badge, force it back to visible
                    if (currentDisplay === 'none' || currentVisibility === 'hidden' || currentOpacity === '0') {
                        console.warn('[BellFixes] Badge visibility attacked! Restoring...', {
                            display: currentDisplay,
                            visibility: currentVisibility,
                            opacity: currentOpacity
                        });

                        badge.style.display = 'inline-block !important';
                        badge.style.visibility = 'visible !important';
                        badge.style.opacity = '1 !important';
                        badge.textContent = count > 99 ? '99+' : count.toString();
                    }
                }
            });
        });

        // Start observing
        observer.observe(badge, {
            attributes: true,
            attributeFilter: ['style', 'class']
        });

        // Keep observing for much longer since something is hiding it later
        setTimeout(() => {
            observer.disconnect();
            console.log('[BellFixes] Badge protection observer disconnected after 60 seconds');
        }, 60000); // Extended to 60 seconds
    }
    
    /**
     * Update mobile bell color (no count display)
     */
    function updateMobileBadge(count) {
        const mobileBadge = document.getElementById('mobile-notification-badge');
        const mobileBellIcon = document.querySelector('#mobile-messages-link .fas.fa-bell');

        // Always hide mobile badge - we only want bell color change
        if (mobileBadge) {
            mobileBadge.style.display = 'none';
            mobileBadge.style.visibility = 'hidden';
            console.log('[BellFixes] Mobile badge permanently hidden (count disabled)');
        }

        if (mobileBellIcon) {
            if (count > 0) {
                // Turn bell bright yellow with glow
                mobileBellIcon.style.color = CONFIG.MOBILE_BELL_COLOR;
                mobileBellIcon.style.textShadow = CONFIG.MOBILE_BELL_GLOW;
                mobileBellIcon.classList.add('has-unread');
                console.log('[BellFixes] Mobile bell turned yellow (no count)');
            } else {
                // Reset to normal color
                mobileBellIcon.style.color = '';
                mobileBellIcon.style.textShadow = '';
                mobileBellIcon.classList.remove('has-unread');
                console.log('[BellFixes] Mobile bell reset to normal');
            }
        }
    }
    
    /**
     * Get current unread count from server
     */
    async function fetchUnreadCount() {
        try {
            const baseUrl = window.BASE_URL || '';
            const response = await fetch(`${baseUrl}/notification_center/getUnreadCount`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                return data.total_unread || 0;
            } else {
                console.warn('[BellFixes] Server returned error:', data.message);
                return 0;
            }
            
        } catch (error) {
            console.warn('[BellFixes] Failed to fetch unread count:', error);
            return 0;
        }
    }
    
    /**
     * Preserve initial PHP count and update badges
     */
    function preserveInitialCount() {
        const initialBadge = document.getElementById('notification-count');
        let initialCount = 0;
        
        if (initialBadge) {
            // Try to get count from data attribute first
            initialCount = parseInt(initialBadge.getAttribute('data-count') || '0');
            
            // If no data attribute, try to parse text content
            if (initialCount === 0 && initialBadge.textContent) {
                const textCount = parseInt(initialBadge.textContent.replace(/\D/g, ''));
                if (!isNaN(textCount)) {
                    initialCount = textCount;
                }
            }
        }
        
        console.log(`[BellFixes] Initial count detected: ${initialCount}`);
        
        if (initialCount > 0) {
            // Use the initial count immediately
            updateNotificationBadges(initialCount);
            
            // Then verify with server after a delay
            setTimeout(async () => {
                const serverCount = await fetchUnreadCount();
                if (serverCount !== initialCount) {
                    console.log(`[BellFixes] Server count differs: ${serverCount} vs ${initialCount}`);
                    updateNotificationBadges(serverCount);
                }
            }, 3000);
        } else {
            // No initial count, fetch from server
            fetchUnreadCount().then(count => {
                updateNotificationBadges(count);
            });
        }
    }
    
    /**
     * Start periodic updates and badge visibility checks
     */
    function startPeriodicUpdates() {
        if (updateInterval) {
            clearInterval(updateInterval);
        }

        updateInterval = setInterval(async () => {
            const count = await fetchUnreadCount();
            if (count !== lastKnownCount) {
                updateNotificationBadges(count);
            }

            // Also check if badge is still visible when it should be
            checkBadgeVisibility(count);
        }, CONFIG.UPDATE_INTERVAL);

        console.log('[BellFixes] Periodic updates started');
    }

    /**
     * Check if badge is visible when it should be
     */
    function checkBadgeVisibility(count) {
        const badge = document.getElementById('notification-count');
        if (!badge || count === 0) return;

        const computedStyle = window.getComputedStyle(badge);
        const isHidden = badge.style.display === 'none' ||
                        computedStyle.display === 'none' ||
                        badge.style.visibility === 'hidden' ||
                        computedStyle.visibility === 'hidden' ||
                        badge.style.opacity === '0' ||
                        computedStyle.opacity === '0';

        if (isHidden) {
            console.warn('[BellFixes] Badge is hidden when it should be visible! Forcing visibility...', {
                styleDisplay: badge.style.display,
                computedDisplay: computedStyle.display,
                styleVisibility: badge.style.visibility,
                computedVisibility: computedStyle.visibility,
                styleOpacity: badge.style.opacity,
                computedOpacity: computedStyle.opacity,
                count: count
            });

            // Force it back to visible
            updateDesktopBadge(count);
        }
    }
    
    /**
     * Override other notification systems aggressively
     */
    function overrideOtherSystems() {
        // Override NotificationCenter updateBadge method if it exists
        if (window.notificationCenter && window.notificationCenter.updateBadge) {
            const originalUpdateBadge = window.notificationCenter.updateBadge;
            window.notificationCenter.updateBadge = function(count) {
                console.log('[BellFixes] Intercepted NotificationCenter.updateBadge');
                updateNotificationBadges(count);
            };
        }

        // Override any global updateNotificationBadge function
        if (window.updateNotificationBadge) {
            const originalGlobalUpdate = window.updateNotificationBadge;
            window.updateNotificationBadge = function(count) {
                console.log('[BellFixes] Intercepted global updateNotificationBadge');
                updateNotificationBadges(count);
            };
        }

        // Aggressively override common jQuery/Bootstrap methods that might hide elements
        const badge = document.getElementById('notification-count');
        if (badge) {
            // Override jQuery hide() if it exists
            if (window.$ && badge.$ && badge.$.hide) {
                const originalHide = badge.$.hide;
                badge.$.hide = function() {
                    console.warn('[BellFixes] Blocked jQuery hide() on badge');
                    return this;
                };
            }

            // Override style.display setter
            const originalDisplayDescriptor = Object.getOwnPropertyDescriptor(badge.style, 'display') ||
                                            Object.getOwnPropertyDescriptor(CSSStyleDeclaration.prototype, 'display');

            if (originalDisplayDescriptor && originalDisplayDescriptor.set) {
                Object.defineProperty(badge.style, 'display', {
                    get: originalDisplayDescriptor.get,
                    set: function(value) {
                        if (value === 'none' && badge.getAttribute('data-count') !== '0') {
                            console.warn('[BellFixes] Blocked attempt to hide badge with display:none');
                            return;
                        }
                        originalDisplayDescriptor.set.call(this, value);
                    }
                });
            }
        }

        console.log('[BellFixes] Aggressive override systems installed');
    }
    
    /**
     * Initialize the bell fixes
     */
    function initialize() {
        if (isInitialized) return;
        
        console.log('[BellFixes] Initializing...');
        
        // Check if user is logged in
        const isLoggedIn = document.getElementById('notification-count') || 
                          document.querySelector('.racing-notification') ||
                          document.getElementById('mobile-notification-badge');
        
        if (!isLoggedIn) {
            console.log('[BellFixes] User not logged in, skipping initialization');
            return;
        }
        
        // Override other systems
        overrideOtherSystems();
        
        // Preserve initial count and update badges
        preserveInitialCount();
        
        // Start periodic updates
        startPeriodicUpdates();
        
        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                fetchUnreadCount().then(count => {
                    updateNotificationBadges(count);
                });
            }
        });
        
        // Listen for focus events
        window.addEventListener('focus', () => {
            fetchUnreadCount().then(count => {
                updateNotificationBadges(count);
            });
        });
        
        isInitialized = true;
        console.log('[BellFixes] Initialization complete');
    }
    
    /**
     * Cleanup function
     */
    function cleanup() {
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = null;
        }
        isInitialized = false;
        console.log('[BellFixes] Cleanup complete');
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initialize, CONFIG.INITIAL_DELAY);
        });
    } else {
        setTimeout(initialize, CONFIG.INITIAL_DELAY);
    }
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanup);
    
    // Export for debugging
    window.notificationBellFixes = {
        updateBadges: updateNotificationBadges,
        fetchCount: fetchUnreadCount,
        initialize: initialize,
        cleanup: cleanup
    };
    
    console.log('[BellFixes] Script loaded');
})();
