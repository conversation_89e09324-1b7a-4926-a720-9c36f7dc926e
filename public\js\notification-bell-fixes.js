/**
 * Notification Bell Icon Fixes
 * 
 * This script fixes the notification bell icon issues:
 * 1. Desktop bell count flashing and disappearing
 * 2. Mobile bell not showing count and not turning yellow
 * 
 * Runs after all other scripts to ensure it overrides any conflicts
 */

(function() {
    'use strict';
    
    console.log('[BellFixes] Loading notification bell fixes...');
    
    // Configuration
    const CONFIG = {
        MOBILE_BELL_COLOR: '#FFD700', // Bright yellow
        MOBILE_BELL_GLOW: '0 0 8px rgba(255, 215, 0, 0.6)',
        UPDATE_INTERVAL: 30000, // 30 seconds
        INITIAL_DELAY: 2000 // 2 seconds to let other scripts load
    };
    
    // State tracking
    let isInitialized = false;
    let updateInterval = null;
    let lastKnownCount = 0;
    
    /**
     * Enhanced badge update function that overrides all other systems
     */
    function updateNotificationBadges(count) {
        console.log(`[BellFixes] Updating badges with count: ${count}`);
        
        // Update desktop badge
        updateDesktopBadge(count);
        
        // Update mobile badge and bell color
        updateMobileBadge(count);
        
        // Store last known count
        lastKnownCount = count;
    }
    
    /**
     * Update desktop notification badge
     */
    function updateDesktopBadge(count) {
        const badge = document.getElementById('notification-count');
        if (!badge) return;
        
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.style.display = 'inline-block';
            badge.style.visibility = 'visible';
            badge.style.opacity = '1';
            badge.setAttribute('data-count', count);
            
            // Add pulse animation
            badge.classList.add('badge-pulse');
            setTimeout(() => {
                badge.classList.remove('badge-pulse');
            }, 1000);
            
            console.log(`[BellFixes] Desktop badge updated: ${count}`);
        } else {
            badge.style.display = 'none';
            badge.setAttribute('data-count', '0');
            console.log('[BellFixes] Desktop badge hidden');
        }
    }
    
    /**
     * Update mobile notification badge and bell color
     */
    function updateMobileBadge(count) {
        const mobileBadge = document.getElementById('mobile-notification-badge');
        const mobileBellIcon = document.querySelector('#mobile-messages-link .fas.fa-bell');
        
        if (mobileBadge) {
            if (count > 0) {
                mobileBadge.textContent = count > 99 ? '99+' : count.toString();
                mobileBadge.style.display = 'flex';
                mobileBadge.style.visibility = 'visible';
                mobileBadge.style.opacity = '1';
                console.log(`[BellFixes] Mobile badge updated: ${count}`);
            } else {
                mobileBadge.style.display = 'none';
                console.log('[BellFixes] Mobile badge hidden');
            }
        }
        
        if (mobileBellIcon) {
            if (count > 0) {
                // Turn bell bright yellow with glow
                mobileBellIcon.style.color = CONFIG.MOBILE_BELL_COLOR;
                mobileBellIcon.style.textShadow = CONFIG.MOBILE_BELL_GLOW;
                mobileBellIcon.classList.add('has-unread');
                console.log('[BellFixes] Mobile bell turned yellow');
            } else {
                // Reset to normal color
                mobileBellIcon.style.color = '';
                mobileBellIcon.style.textShadow = '';
                mobileBellIcon.classList.remove('has-unread');
                console.log('[BellFixes] Mobile bell reset to normal');
            }
        }
    }
    
    /**
     * Get current unread count from server
     */
    async function fetchUnreadCount() {
        try {
            const baseUrl = window.BASE_URL || '';
            const response = await fetch(`${baseUrl}/notification_center/getUnreadCount`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                return data.total_unread || 0;
            } else {
                console.warn('[BellFixes] Server returned error:', data.message);
                return 0;
            }
            
        } catch (error) {
            console.warn('[BellFixes] Failed to fetch unread count:', error);
            return 0;
        }
    }
    
    /**
     * Preserve initial PHP count and update badges
     */
    function preserveInitialCount() {
        const initialBadge = document.getElementById('notification-count');
        let initialCount = 0;
        
        if (initialBadge) {
            // Try to get count from data attribute first
            initialCount = parseInt(initialBadge.getAttribute('data-count') || '0');
            
            // If no data attribute, try to parse text content
            if (initialCount === 0 && initialBadge.textContent) {
                const textCount = parseInt(initialBadge.textContent.replace(/\D/g, ''));
                if (!isNaN(textCount)) {
                    initialCount = textCount;
                }
            }
        }
        
        console.log(`[BellFixes] Initial count detected: ${initialCount}`);
        
        if (initialCount > 0) {
            // Use the initial count immediately
            updateNotificationBadges(initialCount);
            
            // Then verify with server after a delay
            setTimeout(async () => {
                const serverCount = await fetchUnreadCount();
                if (serverCount !== initialCount) {
                    console.log(`[BellFixes] Server count differs: ${serverCount} vs ${initialCount}`);
                    updateNotificationBadges(serverCount);
                }
            }, 3000);
        } else {
            // No initial count, fetch from server
            fetchUnreadCount().then(count => {
                updateNotificationBadges(count);
            });
        }
    }
    
    /**
     * Start periodic updates
     */
    function startPeriodicUpdates() {
        if (updateInterval) {
            clearInterval(updateInterval);
        }
        
        updateInterval = setInterval(async () => {
            const count = await fetchUnreadCount();
            if (count !== lastKnownCount) {
                updateNotificationBadges(count);
            }
        }, CONFIG.UPDATE_INTERVAL);
        
        console.log('[BellFixes] Periodic updates started');
    }
    
    /**
     * Override other notification systems
     */
    function overrideOtherSystems() {
        // Override NotificationCenter updateBadge method if it exists
        if (window.notificationCenter && window.notificationCenter.updateBadge) {
            const originalUpdateBadge = window.notificationCenter.updateBadge;
            window.notificationCenter.updateBadge = function(count) {
                console.log('[BellFixes] Intercepted NotificationCenter.updateBadge');
                updateNotificationBadges(count);
            };
        }
        
        // Override any global updateNotificationBadge function
        if (window.updateNotificationBadge) {
            const originalGlobalUpdate = window.updateNotificationBadge;
            window.updateNotificationBadge = function(count) {
                console.log('[BellFixes] Intercepted global updateNotificationBadge');
                updateNotificationBadges(count);
            };
        }
        
        console.log('[BellFixes] Override systems installed');
    }
    
    /**
     * Initialize the bell fixes
     */
    function initialize() {
        if (isInitialized) return;
        
        console.log('[BellFixes] Initializing...');
        
        // Check if user is logged in
        const isLoggedIn = document.getElementById('notification-count') || 
                          document.querySelector('.racing-notification') ||
                          document.getElementById('mobile-notification-badge');
        
        if (!isLoggedIn) {
            console.log('[BellFixes] User not logged in, skipping initialization');
            return;
        }
        
        // Override other systems
        overrideOtherSystems();
        
        // Preserve initial count and update badges
        preserveInitialCount();
        
        // Start periodic updates
        startPeriodicUpdates();
        
        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                fetchUnreadCount().then(count => {
                    updateNotificationBadges(count);
                });
            }
        });
        
        // Listen for focus events
        window.addEventListener('focus', () => {
            fetchUnreadCount().then(count => {
                updateNotificationBadges(count);
            });
        });
        
        isInitialized = true;
        console.log('[BellFixes] Initialization complete');
    }
    
    /**
     * Cleanup function
     */
    function cleanup() {
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = null;
        }
        isInitialized = false;
        console.log('[BellFixes] Cleanup complete');
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initialize, CONFIG.INITIAL_DELAY);
        });
    } else {
        setTimeout(initialize, CONFIG.INITIAL_DELAY);
    }
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanup);
    
    // Export for debugging
    window.notificationBellFixes = {
        updateBadges: updateNotificationBadges,
        fetchCount: fetchUnreadCount,
        initialize: initialize,
        cleanup: cleanup
    };
    
    console.log('[BellFixes] Script loaded');
})();
