/**
 * Notification System CSS v3.49.15
 * 
 * Styles for the comprehensive notification system including
 * toast notifications, subscription modals, and admin interfaces.
 * 
 * Enhanced modal layout with improved spacing and mobile responsiveness.
 * Fixed checkbox positioning to prevent them from sitting on column borders.
 * 
 * Location: /public/css/notifications.css
 * Dependencies: Bootstrap 5, Font Awesome
 */

/* ==========================================================================
   Toast Notifications
   ========================================================================== */

.toast-container {
    z-index: 99999;
    max-width: 400px;
    pointer-events: none;
}

.toast-container > * {
    pointer-events: auto;
}

.toast-notification {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #0d6efd;
    animation: slideInRight 0.3s ease-out;
    z-index: 100000;
    position: relative;
    pointer-events: auto;
}

.toast-notification.alert-success {
    border-left-color: #198754;
}

.toast-notification.alert-warning {
    border-left-color: #ffc107;
}

.toast-notification.alert-danger {
    border-left-color: #dc3545;
}

.toast-notification.alert-info {
    border-left-color: #0dcaf0;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.toast-notification.fade-out {
    animation: fadeOut 0.15s ease-out forwards;
}

.toast-notification .btn-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.5rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
    color: #0d6efd;
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230d6efd'%3e%3cpath d='m.235 1.027 1.027-.235 6.738 6.738 6.738-6.738 1.027.235-6.738 6.738 6.738 6.738-1.027.235-6.738-6.738-6.738 6.738-1.027-.235 6.738-6.738-6.738-6.738z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    border: 0;
    border-radius: 0.375rem;
    opacity: 0.7;
    cursor: pointer;
    width: 1em;
    height: 1em;
}

.toast-notification .btn-close:hover {
    color: #0d6efd;
    text-decoration: none;
    opacity: 1;
}

.toast-notification .btn-close:focus {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    opacity: 1;
}

.toast-notification {
    position: relative;
    padding-right: 3rem;
}

/* ==========================================================================
   Notification Buttons
   ========================================================================== */

[data-notification-btn] {
    position: relative;
    transition: all 0.2s ease;
    min-height: 44px; /* Better touch target for mobile */
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

[data-notification-btn]:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

[data-notification-btn]:active {
    transform: scale(0.95);
}

[data-notification-btn].btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0b5ed7);
}

[data-notification-btn].btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #0a58ca);
}

/* Mobile-specific button enhancements */
@media (max-width: 768px) {
    [data-notification-btn] {
        min-height: 48px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

/* Badge pulse animation for new notifications */
.notification-badge.badge-pulse {
    animation: notification-badge-pulse 1s ease-in-out;
}

@keyframes notification-badge-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); box-shadow: 0 0 15px rgba(220, 53, 69, 0.6); }
    100% { transform: scale(1); }
}

/* ==========================================================================
   Subscription Modal
   ========================================================================== */

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    padding: 1.5rem;
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
    padding: 1.5rem;
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 1;
}

/* Enhanced modal body with proper spacing */
.notification-modal-body {
    padding: 1.5rem;
    min-height: 300px;
    max-height: calc(90vh - 200px);
    overflow-y: auto;
}

.notification-modal-body .container-fluid {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    max-width: 100%;
}

/* Notification times columns with proper spacing */
.notification-times-column {
    padding: 0.75rem 1rem 0.75rem 1.25rem;
    background-color: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    width: 100%;
    box-sizing: border-box;
}

.notification-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    width: 100%;
    box-sizing: border-box;
}

/* Enhanced form check styling */
.form-check {
    padding: 0.75rem 0.5rem 0.75rem 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    margin-left: 0.25rem;
    position: relative;
}

.form-check:hover {
    background-color: rgba(13, 110, 253, 0.08);
    transform: translateX(2px);
}

.form-check-input {
    margin-top: 0.25rem;
    margin-right: 0.75rem;
    margin-left: 0.25rem;
}

.form-check-label {
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    line-height: 1.4;
    padding-left: 0.25rem;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:checked + .form-check-label {
    color: #0d6efd;
    font-weight: 600;
}

.form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Form switch enhancements */
.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    margin-right: 1rem;
}

.form-switch .form-check-label {
    padding-left: 0.5rem;
}

/* Registration deadline notification positioning fix */
#notify_registration_end_container {
    /* Match alert styling for consistent width and padding */
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    background-color: rgba(13, 202, 240, 0.1);
    border-color: rgba(13, 202, 240, 0.2);
    color: #055160;
    
    /* Remove previous positioning */
    margin-left: 0;
    
    /* Use flexbox to position toggle at the end */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Keep the toggle switch in its original position */
#notify_registration_end_container .form-check-input {
    margin-left: auto;
    margin-right: 0;
}

/* Adjust label to take available space */
#notify_registration_end_container .form-check-label {
    flex: 1;
    margin-right: 1rem;
}

/* ==========================================================================
   Admin Interface
   ========================================================================== */

.notification-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.notification-stats-card .card-body {
    padding: 2rem;
}

.notification-stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
}

.notification-provider-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.notification-provider-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.notification-provider-card.active {
    border-color: #198754;
    background-color: rgba(25, 135, 84, 0.05);
}

.notification-provider-card.default {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.provider-status-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* ==========================================================================
   Queue Management
   ========================================================================== */

.notification-queue-table {
    font-size: 0.9rem;
}

.notification-queue-table .table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.notification-queue-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.notification-queue-table .badge {
    font-size: 0.7rem;
    padding: 0.35em 0.65em;
}

.notification-status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.notification-status-sent {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #b8daff;
}

.notification-status-failed {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.notification-status-cancelled {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* ==========================================================================
   User Preferences
   ========================================================================== */



.notification-preferences-card {
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
}

.notification-preferences-card .card-header {
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    border-radius: 3rem;
}

.form-switch .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

/* User preference toggle styling */
.user-preference-toggle {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    min-height: 80px;
    overflow: hidden;
    position: relative;
}

.user-preference-toggle .form-check.form-switch {
    margin: 0;
    width: auto;
    max-width: 60px;
}

.user-preference-toggle:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-preference-toggle .form-check-input {
    margin: 0 !important;
    flex-shrink: 0;
    position: relative;
    float: none;
}

.user-preference-toggle .form-check-label {
    margin-bottom: 0;
    flex: 1;
    cursor: pointer;
    padding-left: 0 !important;
}

/* Override form-switch specific styles for user preference toggles - Ultra specific selectors */
div.user-preference-toggle.form-switch.d-flex .form-check-input {
    margin-right: 0 !important;
    margin-left: 0 !important;
    margin-top: 0 !important;
    position: relative !important;
}

div.user-preference-toggle.form-switch.d-flex .form-check-label {
    padding-left: 0 !important;
    margin-bottom: 0 !important;
}

/* Even more specific override for Bootstrap form-switch */
.col-md-6 .user-preference-toggle.form-switch.mb-3.d-flex .form-check-input {
    margin-right: 0 !important;
    margin-left: 0 !important;
}

.notification-subscription-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.notification-subscription-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.1);
}

.notification-subscription-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.notification-times-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.notification-time-badge {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* ==========================================================================
   Test Interface
   ========================================================================== */

.test-notification-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 2rem;
}

.test-notification-result {
    border-left: 4px solid #0d6efd;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
    margin-top: 1rem;
}

.test-notification-result.success {
    border-left-color: #198754;
    background-color: #d1edff;
}

.test-notification-result.error {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.quick-test-buttons .btn {
    margin-bottom: 0.5rem;
    width: 100%;
    text-align: left;
}

.system-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.system-status-item:last-child {
    border-bottom: none;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .toast-container {
        left: 1rem;
        right: 1rem;
        top: 1rem !important;
        max-width: none;
    }
    
    .toast-notification {
        margin-bottom: 0.5rem;
    }
    
    .notification-time-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .notification-stats-number {
        font-size: 2rem;
    }
    
    .notification-queue-table {
        font-size: 0.8rem;
    }
    
    .notification-subscription-item {
        padding: 0.75rem;
    }
    
    .notification-times-list {
        justify-content: center;
    }
    
    /* Mobile modal improvements */
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
        width: calc(100vw - 1rem);
    }
    
    .modal-content {
        max-height: 95vh;
        border-radius: 8px;
    }
    
    .notification-modal-body {
        padding: 1rem;
        min-height: auto;
        max-height: calc(95vh - 160px);
    }
    
    .notification-modal-body .container-fluid {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
    
    .notification-times-column {
        padding: 0.75rem 0.5rem;
        margin-bottom: 1rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .modal-header,
    .modal-footer {
        padding: 1rem;
    }
    
    .modal-header {
        border-radius: 8px 8px 0 0;
    }
    
    .modal-footer {
        border-radius: 0 0 8px 8px;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .modal-footer .btn {
        flex: 1;
        min-width: 120px;
    }
    
    .form-check {
        padding: 0.5rem 0.25rem 0.5rem 0.5rem;
        margin-bottom: 0.25rem;
        margin-left: 0;
        width: 100%;
        box-sizing: border-box;
    }
    
    .form-check-label {
        font-size: 0.9rem;
        word-wrap: break-word;
    }
    
    .form-check-input {
        margin-top: 0.3rem;
        flex-shrink: 0;
    }
    
    /* Better touch targets for mobile */
    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
    }
    
    .form-switch .form-check-input {
        width: 2.5rem;
        height: 1.25rem;
    }

    /* Mobile responsive styles for user preference toggles */
    .user-preference-toggle {
        padding: 0.75rem;
        min-height: 70px;
    }
}

@media (max-width: 576px) {
    /* Vertical layout for user preference toggles on very small screens */
    .user-preference-toggle {
        flex-direction: column;
        align-items: flex-start !important;
        padding: 1rem;
        min-height: auto;
    }

    .user-preference-toggle .form-check-input {
        align-self: flex-end;
        margin-top: 0.5rem;
    }

    .modal-dialog {
        margin: 0.25rem;
        max-width: calc(100vw - 0.5rem);
        width: calc(100vw - 0.5rem);
    }
    
    .modal-content {
        max-height: 98vh;
        border-radius: 6px;
    }
    
    .toast-notification .alert-heading {
        font-size: 0.9rem;
    }
    
    .toast-notification .small {
        font-size: 0.75rem;
    }
    
    [data-notification-btn] {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
        min-height: 44px; /* Better touch target */
    }
    
    .notification-badge {
        top: -6px;
        right: -6px;
        min-width: 16px;
        height: 16px;
        font-size: 9px;
    }
    
    /* Extra small screen modal improvements */
    .notification-modal-body {
        padding: 0.75rem;
        max-height: calc(98vh - 140px);
    }
    
    .notification-modal-body .container-fluid {
        padding-left: 0;
        padding-right: 0;
    }
    
    .notification-times-column {
        padding: 0.5rem;
        background-color: rgba(248, 249, 250, 0.3);
        border: 1px solid rgba(0, 0, 0, 0.1);
        margin-bottom: 0.75rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .modal-header {
        padding: 0.75rem;
        border-radius: 6px 6px 0 0;
    }
    
    .modal-footer {
        padding: 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
        border-radius: 0 0 6px 6px;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin-bottom: 0;
        min-height: 44px; /* Better touch target */
        font-size: 0.9rem;
    }
    
    .form-check {
        padding: 0.75rem 0.5rem;
        margin-bottom: 0.25rem;
        margin-left: 0;
        width: 100%;
        box-sizing: border-box;
        min-height: 44px; /* Better touch target */
        display: flex;
        align-items: center;
    }
    
    .form-check-input {
        margin-right: 0.75rem;
        margin-left: 0;
        width: 1.5rem;
        height: 1.5rem;
        flex-shrink: 0;
    }
    
    .form-check-label {
        font-size: 0.9rem;
        line-height: 1.4;
        flex: 1;
        cursor: pointer;
        word-wrap: break-word;
    }
    
    .form-switch .form-check-input {
        width: 3rem;
        height: 1.5rem;
    }
    
    .modal-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }
    
    .alert {
        font-size: 0.85rem;
        padding: 0.75rem;
        line-height: 1.4;
    }
    
    /* Registration deadline container mobile optimization */
    #notify_registration_end_container {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.75rem;
    }
    
    #notify_registration_end_container .form-check-input {
        margin-left: 0;
        margin-right: 0.75rem;
    }
    
    #notify_registration_end_container .form-check-label {
        margin-right: 0;
        flex: none;
    }
    
    /* Better spacing for notification time sections */
    .row.g-3 {
        --bs-gutter-x: 0.5rem;
        --bs-gutter-y: 0.5rem;
    }
    
    .col-12.col-md-6 {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
    
    /* Mobile modal button enhancements */
    .modal-footer .btn {
        min-height: 48px;
        font-size: 1rem;
        font-weight: 500;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* Mobile form check spacing improvements */
    .modal-body .form-check {
        margin: 0.5rem 0.75rem;
        padding: 0.75rem;
        background-color: rgba(248, 249, 250, 0.3);
        border-radius: 8px;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .modal-body .notification-times-column {
        margin: 0.5rem 0.75rem 1rem 0.75rem;
        width: calc(100% - 1.5rem);
        box-sizing: border-box;
    }
    
    .modal-body .notification-times-column .form-check {
        margin: 0.25rem 0;
        background-color: transparent;
        border: none;
    }
    
    .modal-body .alert {
        margin: 0.5rem 0.75rem 1rem 0.75rem;
        width: calc(100% - 1.5rem);
        box-sizing: border-box;
    }
    
    #notify_registration_end_container {
        margin: 0.5rem 0.75rem 1rem 0.75rem;
        width: calc(100% - 1.5rem);
        box-sizing: border-box;
    }
    
    .modal-footer .btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
    
    /* Ensure buttons are easily tappable */
    .modal-footer .btn-primary,
    .modal-footer .btn-danger,
    .modal-footer .btn-secondary {
        position: relative;
        overflow: hidden;
    }
    
    .modal-footer .btn-primary::after,
    .modal-footer .btn-danger::after,
    .modal-footer .btn-secondary::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }
    
    .modal-footer .btn:active::after {
        width: 300px;
        height: 300px;
    }
}

/* ==========================================================================
   Dark Mode Support (Optional)
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    /* Only apply dark mode to toast notifications, not cards */
    .toast-notification {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .notification-time-badge {
        background-color: #4a5568;
        color: #e2e8f0;
    }
    
    .test-notification-form {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: #e2e8f0;
    }
    
    /* Force white backgrounds for notification preferences cards even in dark mode */
    .notification-preferences-page .notification-preferences-card,
    .notification-preferences-page .user-preference-toggle,
    .notification-preferences-page .notification-subscription-item,
    .notification-preferences-page .card {
        background-color: #ffffff !important;
        color: #212529 !important;
        border-color: #dee2e6 !important;
    }
    
    .notification-preferences-page .card-header {
        background-color: inherit !important;
        color: inherit !important;
        border-bottom-color: #dee2e6 !important;
    }
    
    .notification-preferences-page .card-body {
        background-color: #ffffff !important;
        color: #212529 !important;
    }
    
    .notification-preferences-page .user-preference-toggle:hover {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
    }
    
    .notification-preferences-page .user-preference-toggle .form-check-label {
        color: #212529 !important;
    }
    
    /* Ensure table elements stay readable on notification preferences page */
    .notification-preferences-page .table {
        background-color: #ffffff !important;
        color: #212529 !important;
    }
    
    .notification-preferences-page .table th,
    .notification-preferences-page .table td {
        background-color: #ffffff !important;
        color: #212529 !important;
        border-color: #dee2e6 !important;
    }
    
    .notification-preferences-page .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.075) !important;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .toast-container,
    [data-notification-btn],
    .notification-badge {
        display: none !important;
    }
    
    .notification-queue-table {
        font-size: 0.7rem;
    }
    
    .notification-subscription-item {
        border: 1px solid #000;
        break-inside: avoid;
    }
}