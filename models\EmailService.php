<?php
/**
 * Email Service
 * 
 * This class handles sending emails using SMTP
 */
class EmailService {
    private $db;
    private $settingsModel;
    private $smtpHost;
    private $smtpPort;
    private $smtpUsername;
    private $smtpPassword;
    private $smtpEncryption;
    private $fromAddress;
    private $fromName;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        $this->settingsModel = new SettingsModel();
        $this->loadSettings();
    }
    
    /**
     * Load email settings from database
     */
    private function loadSettings() {
        $this->smtpHost = $this->settingsModel->getSetting('email_smtp_host', '');
        $this->smtpPort = $this->settingsModel->getSetting('email_smtp_port', '587');
        $this->smtpUsername = $this->settingsModel->getSetting('email_smtp_username', '');
        $this->smtpPassword = $this->settingsModel->getSetting('email_smtp_password', '');
        $this->smtpEncryption = $this->settingsModel->getSetting('email_smtp_encryption', 'tls');
        $this->fromAddress = $this->settingsModel->getSetting('email_from_address', '');
        $this->fromName = $this->settingsModel->getSetting('email_from_name', 'Events and Shows');
    }
    
    /**
     * Check if email is configured
     *
     * @return bool
     */
    public function isConfigured() {
        return !empty($this->smtpHost) && !empty($this->smtpUsername) && !empty($this->smtpPassword) && !empty($this->fromAddress);
    }

    /**
     * Send email immediately with fallback to queue
     *
     * Attempts to send email immediately. If it fails due to connection issues
     * or SMTP problems, it queues the email for later processing by cron job.
     *
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @param string $plainText Plain text version
     * @param int $userId User ID for queue fallback
     * @param array $attachments Array of attachments
     * @return array Result with status and details
     */
    public function sendWithFallback($to, $subject, $body, $plainText = '', $userId = null, $attachments = []) {
        $result = [
            'success' => false,
            'method' => 'immediate',
            'queued' => false,
            'error' => null,
            'details' => ''
        ];

        // First, try immediate sending
        try {
            if (DEBUG_MODE) {
                error_log("EmailService::sendWithFallback - Attempting immediate send to: {$to}");
            }

            $sent = $this->send($to, $subject, $body, $plainText, $attachments);

            if ($sent) {
                $result['success'] = true;
                $result['details'] = 'Email sent immediately via SMTP';

                if (DEBUG_MODE) {
                    error_log("EmailService::sendWithFallback - Immediate send successful to: {$to}");
                }

                return $result;
            } else {
                throw new Exception('SMTP send returned false');
            }

        } catch (Exception $e) {
            $error = $e->getMessage();
            $result['error'] = $error;

            if (DEBUG_MODE) {
                error_log("EmailService::sendWithFallback - Immediate send failed to {$to}: {$error}");
            }

            // Check if this is a connection/SMTP issue that warrants queuing
            $shouldQueue = $this->shouldQueueOnError($error);

            if ($shouldQueue && $userId) {
                // Fallback to queue
                try {
                    $queued = $this->queueEmailForLater($to, $subject, $plainText, $userId);

                    if ($queued) {
                        $result['queued'] = true;
                        $result['method'] = 'queued';
                        $result['details'] = "Immediate send failed, email queued for cron processing. Error: {$error}";

                        if (DEBUG_MODE) {
                            error_log("EmailService::sendWithFallback - Email queued for later processing: {$to}");
                        }
                    } else {
                        $result['details'] = "Both immediate send and queue failed. Error: {$error}";

                        if (DEBUG_MODE) {
                            error_log("EmailService::sendWithFallback - Both immediate and queue failed for: {$to}");
                        }
                    }

                } catch (Exception $queueError) {
                    $result['details'] = "Immediate send failed, queue also failed: {$error}. Queue error: " . $queueError->getMessage();

                    if (DEBUG_MODE) {
                        error_log("EmailService::sendWithFallback - Queue fallback failed for {$to}: " . $queueError->getMessage());
                    }
                }
            } else {
                $result['details'] = "Immediate send failed and error not suitable for queuing: {$error}";

                if (DEBUG_MODE) {
                    error_log("EmailService::sendWithFallback - Error not suitable for queuing: {$error}");
                }
            }
        }

        return $result;
    }

    /**
     * Determine if an error should trigger queue fallback
     *
     * @param string $error Error message
     * @return bool True if should queue, false otherwise
     */
    private function shouldQueueOnError($error) {
        $queueableErrors = [
            'connection',
            'timeout',
            'network',
            'smtp',
            'authentication failed',
            'could not connect',
            'connection refused',
            'connection timed out',
            'temporary failure',
            'server not available',
            'service unavailable'
        ];

        $errorLower = strtolower($error);

        foreach ($queueableErrors as $queueableError) {
            if (strpos($errorLower, $queueableError) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Queue email for later processing by cron job
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $message Email message (plain text)
     * @param int $userId User ID
     * @return bool Success status
     */
    private function queueEmailForLater($to, $subject, $message, $userId) {
        try {
            // Get user email if not provided
            if (empty($to) && $userId) {
                $this->db->query("SELECT email FROM users WHERE id = ?");
                $this->db->bind(1, $userId);
                $this->db->execute();
                $user = $this->db->single();
                $to = $user->email ?? '';
            }

            if (empty($to)) {
                throw new Exception("No email address available for user ID: {$userId}");
            }

            // Insert into notification queue
            $this->db->query("
                INSERT INTO notification_queue
                (user_id, notification_type, subject, message, status, created_at, updated_at)
                VALUES (?, 'email', ?, ?, 'pending', NOW(), NOW())
            ");
            $this->db->bind(1, $userId);
            $this->db->bind(2, $subject);
            $this->db->bind(3, $message);

            $success = $this->db->execute();

            if (DEBUG_MODE) {
                error_log("EmailService::queueEmailForLater - Queued email for user {$userId}: " . ($success ? 'Success' : 'Failed'));
            }

            return $success;

        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("EmailService::queueEmailForLater - Error: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Send email
     * 
     * @param string|array $to Recipient email address(es)
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @param string $plainText Plain text version of the email
     * @param array $attachments Array of attachments [path => filename]
     * @return bool
     */
    public function send($to, $subject, $body, $plainText = '', $attachments = []) {
        if (!$this->isConfigured()) {
            error_log('Email not configured');
            return false;
        }
        
        // Try to load PHPMailer if available
        $phpmailerAvailable = false;
        if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            // Try to include PHPMailer if it's in the vendor directory
            $phpmailerPath = APPROOT . '/vendor/autoload.php';
            if (file_exists($phpmailerPath)) {
                require_once $phpmailerPath;
                $phpmailerAvailable = class_exists('PHPMailer\\PHPMailer\\PHPMailer');
            }
        } else {
            $phpmailerAvailable = true;
        }

        if (DEBUG_MODE) {
            error_log('EmailService: PHPMailer available: ' . ($phpmailerAvailable ? 'Yes' : 'No, using fallback mail() function'));
            if ($phpmailerAvailable) {
                error_log('EmailService: Will use PHPMailer for enhanced email functionality');
            }
        }
        
        try {
            // Create a new PHPMailer instance if available
            if ($phpmailerAvailable) {
                $mail = new PHPMailer\PHPMailer\PHPMailer(true);
                
                // Server settings
                $mail->isSMTP();
                $mail->Host = $this->smtpHost;
                $mail->SMTPAuth = true;
                $mail->Username = $this->smtpUsername;
                $mail->Password = $this->smtpPassword;
                $mail->SMTPSecure = $this->smtpEncryption;
                $mail->Port = $this->smtpPort;
                
                // Recipients
                $mail->setFrom($this->fromAddress, $this->fromName);
                
                if (is_array($to)) {
                    foreach ($to as $recipient) {
                        $mail->addAddress($recipient);
                    }
                } else {
                    $mail->addAddress($to);
                }
                
                // Content
                $mail->isHTML(true);
                $mail->Subject = $subject;
                $mail->Body = $body;
                
                if (!empty($plainText)) {
                    $mail->AltBody = $plainText;
                } else {
                    // Create plain text version from HTML
                    $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />', '</p>'], "\n", $body));
                }
                
                // Attachments
                if (!empty($attachments)) {
                    foreach ($attachments as $path => $name) {
                        $mail->addAttachment($path, $name);
                    }
                }
                
                // Send the email
                return $mail->send();
            } else {
                // Fallback to mail() function if PHPMailer is not available
                if (DEBUG_MODE) {
                    error_log('EmailService: Using fallback mail() function');
                }

                $headers = "MIME-Version: 1.0\r\n";
                $headers .= "Content-type: text/html; charset=utf-8\r\n";
                $headers .= "From: {$this->fromName} <{$this->fromAddress}>\r\n";
                $headers .= "Reply-To: {$this->fromAddress}\r\n";

                // Handle multiple recipients if array is passed
                if (is_array($to)) {
                    $success = true;
                    foreach ($to as $recipient) {
                        $result = mail($recipient, $subject, $body, $headers);
                        if (!$result) {
                            $success = false;
                            if (DEBUG_MODE) {
                                error_log("EmailService: Failed to send email to {$recipient}");
                            }
                        }
                    }
                    return $success;
                } else {
                    $result = mail($to, $subject, $body, $headers);
                    if (DEBUG_MODE) {
                        error_log("EmailService: Email sent to {$to} - " . ($result ? 'Success' : 'Failed'));
                    }
                    return $result;
                }
            }
        } catch (Exception $e) {
            error_log('Email error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send notification to all admins
     * 
     * @param string $subject Email subject
     * @param string $body Email body (HTML)
     * @return bool
     */
    public function sendAdminNotification($subject, $body) {
        if (!$this->isConfigured()) {
            error_log('Email not configured');
            return false;
        }
        
        // Get all admin emails
        $this->db->query('SELECT email FROM users WHERE role = "admin" AND status = "active"');
        $admins = $this->db->resultSet();
        
        if (empty($admins)) {
            error_log('No admin users found');
            return false;
        }
        
        $adminEmails = [];
        foreach ($admins as $admin) {
            $adminEmails[] = $admin->email;
        }
        
        return $this->send($adminEmails, $subject, $body);
    }
    
    /**
     * Send new show notification to admins
     * 
     * @param object $show Show object
     * @param object $coordinator Coordinator user object
     * @return bool
     */
    public function sendNewShowNotification($show, $coordinator) {
        // Check if notifications are enabled
        $notificationsEnabled = $this->settingsModel->getSetting('email_notification_new_show', '1');
        if ($notificationsEnabled != '1') {
            return true; // Skip sending if disabled
        }
        
        $subject = "New Show Created: {$show->name}";
        
        $body = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #2c3e50; margin-bottom: 20px; }
                .info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .label { font-weight: bold; }
                .button { display: inline-block; background-color: #3498db; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>New Show Created</h1>
                <p>A new show has been created and requires your attention.</p>
                
                <div class='info'>
                    <p><span class='label'>Show Name:</span> {$show->name}</p>
                    <p><span class='label'>Venue:</span> {$show->location}</p>
                    <p><span class='label'>Dates:</span> " . gmdate('M j, Y', strtotime($show->start_date)) . " - " . gmdate('M j, Y', strtotime($show->end_date)) . "</p>
                    <p><span class='label'>Created By:</span> {$coordinator->name} ({$coordinator->email})</p>
                    <p><span class='label'>Status:</span> " . ucfirst($show->status) . "</p>";
        
        if ($show->status == 'payment_pending') {
            $body .= "<p><span class='label'>Payment Status:</span> Pending payment for listing fee</p>";
        }
        
        $body .= "
                </div>
                
                <p>Please review this show and take appropriate action.</p>
                
                <p>
                    <a href='" . BASE_URL . "/admin/editShow/{$show->id}' class='button'>View Show Details</a>
                </p>
            </div>
        </body>
        </html>
        ";
        
        return $this->sendAdminNotification($subject, $body);
    }
}