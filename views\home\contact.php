<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <h1 class="mb-4">Contact Us</h1>
    
    <?php if (hasFlashMessage('contact_success')) : ?>
        <div class="alert alert-success">
            <?php echo getFlashMessage('contact_success')['message']; ?>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="card-title">Send Us a Message</h2>
                    <form action="<?php echo BASE_URL; ?>/home/<USER>" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" name="name" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $name; ?>">
                            <div class="invalid-feedback"><?php echo $name_err; ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" name="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $email; ?>">
                            <div class="invalid-feedback"><?php echo $email_err; ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject</label>
                            <input type="text" name="subject" class="form-control <?php echo (!empty($subject_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $subject; ?>">
                            <div class="invalid-feedback"><?php echo $subject_err; ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea name="message" class="form-control <?php echo (!empty($message_err)) ? 'is-invalid' : ''; ?>" rows="5"><?php echo $message; ?></textarea>
                            <div class="invalid-feedback"><?php echo $message_err; ?></div>
                        </div>

                        <!-- CAPTCHA Verification -->
                        <div class="mb-3">
                            <label for="captcha" class="form-label">Security Verification</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="captcha-container p-3 bg-light border rounded text-center">
                                        <span class="captcha-text fw-bold fs-4" style="font-family: monospace; letter-spacing: 3px; color: #333; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                                            <?php echo $captcha_text; ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <input type="text" name="captcha" class="form-control <?php echo (!empty($captcha_err)) ? 'is-invalid' : ''; ?>"
                                           placeholder="Enter the text shown" required>
                                    <div class="invalid-feedback"><?php echo $captcha_err; ?></div>
                                    <small class="form-text text-muted">Please enter the text shown in the box above</small>
                                </div>
                            </div>
                            <input type="hidden" name="captcha_hash" value="<?php echo $captcha_hash; ?>">
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="card-title">Contact Information</h2>
                    <p class="card-text">
                        We'd love to hear from you! Whether you have a question about our platform, need help with your account, or want to suggest a new feature, our team is here to help.
                    </p>
                    
                    <ul class="list-group list-group-flush mb-4">
                        <li class="list-group-item">
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <strong>Email:</strong> <EMAIL>
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-phone text-primary me-2"></i>
                            <strong>Phone:</strong> (*************
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            <strong>Address:</strong> 123 Main Street, Suite 100, Anytown, USA 12345
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-clock text-primary me-2"></i>
                            <strong>Hours:</strong> Monday - Friday, 9:00 AM - 5:00 PM EST
                        </li>
                    </ul>
                    
                    <h3>Connect With Us</h3>
                    <div class="social-icons mb-3">
                        <a href="#" class="btn btn-outline-primary me-2"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="btn btn-outline-primary me-2"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="btn btn-outline-primary me-2"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="btn btn-outline-primary me-2"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">Frequently Asked Questions</h2>
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="headingOne">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                                    How do I register for a show?
                                </button>
                            </h3>
                            <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    To register for a show, you need to create an account, add your vehicle(s), and then navigate to the show page. Click the "Register" button and follow the instructions to complete your registration.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    How do I become a judge?
                                </button>
                            </h3>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    To become a judge, please contact the show coordinator or administrator. They will review your experience and qualifications before assigning you as a judge.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h3 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    How are winners determined?
                                </button>
                            </h3>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Winners are determined based on the judging criteria established for each show. Judges score vehicles in various categories, and the scores are weighted and tallied to determine the winners in each class.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>