    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo APP_NAME; ?></h5>
                    <p>A comprehensive application for managing car shows, judging, registrations, and vehicle displays.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo BASE_URL; ?>" class="text-white">Home</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/show" class="text-white">Shows</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/home/<USER>" class="text-white">About</a></li>
                        <li><a href="<?php echo BASE_URL; ?>/home/<USER>" class="text-white">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Connect With Us</h5>
                    <div class="social-icons">
                        <?php 
                        // Get social media links from settings
                        $facebook_url = $settingsModel->getSetting('facebook_url', '#');
                        $twitter_url = $settingsModel->getSetting('twitter_url', '#');
                        $instagram_url = $settingsModel->getSetting('instagram_url', '#');
                        $linkedin_url = $settingsModel->getSetting('linkedin_url', '#');
                        ?>
                        <a href="<?php echo $facebook_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?php echo $twitter_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-twitter"></i></a>
                        <a href="<?php echo $instagram_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?php echo $linkedin_url; ?>" class="text-white me-2" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <?php 
                // Get footer text from settings
                require_once APPROOT . '/models/SettingsModel.php';
                $settingsModel = new SettingsModel();
                $footer_text = $settingsModel->getSetting('footer_text', APP_NAME);
                $enable_white_labeling = $settingsModel->getSetting('enable_white_labeling', '0');
                
                if ($enable_white_labeling == '1' && !empty($footer_text)) {
                    echo '<p>&copy; ' . gmdate('Y') . ' ' . $footer_text . '. All rights reserved.</p>';
                } else {
                    echo '<p>&copy; ' . gmdate('Y') . ' ' . APP_NAME . '. All rights reserved.</p>';
                }
                ?>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN" crossorigin="anonymous"></script>

    <!-- Bootstrap initialization check -->
    <script>
    (function() {
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap failed to load properly. Attempting to reload...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js';
            script.integrity = 'sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN';
            script.crossOrigin = 'anonymous';
            document.body.appendChild(script);
        } else {
            console.log('Bootstrap loaded successfully in footer');
        }
    })();
    </script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>/public/js/main.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/image-viewer.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/notifications.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/mobile-notifications-fix.js"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/desktop-notifications-fix.js"></script>
    
    <!-- PWA Features - Force fresh loading -->
    <script src="<?php echo BASE_URL; ?>/public/js/pwa-features.js?v=<?php echo time(); ?>"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/camera-banner.js?v=<?php echo time(); ?>"></script>
    
    <!-- PWA Initialization -->
    <script>
        // PWA Configuration
        window.PWA_CONFIG = {
            baseUrl: '<?php echo BASE_URL; ?>',
            userId: <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'null'; ?>,
            userRole: '<?php echo isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'guest'; ?>',
            debugMode: <?php echo DEBUG_MODE ? 'true' : 'false'; ?>,
            vapidPublicKey: null // Will be fetched from server
        };

        // Debug logging for PWA config
        if (window.PWA_CONFIG.debugMode) {
            console.log('[PWA] Configuration:', window.PWA_CONFIG);
        }
        


        // Initialize PWA features when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePWA);
        } else {
            initializePWA();
        }
        
        function initializePWA() {
            if (typeof PWAFeatures !== 'undefined' || typeof window.PWAFeatures !== 'undefined') {
                if (!window.pwaFeatures) {
                    const PWAClass = window.PWAFeatures || PWAFeatures;
                    window.pwaFeatures = new PWAClass();
                }

                // Update usage data
                if (window.PWA_CONFIG.userId) {
                    updatePWAUsageData();
                }

                // Add mobile navigation if on mobile
                if (window.innerWidth <= 768) {
                    addMobileNavigation();
                }
                
                // Add quick actions FAB
                addQuickActionsFAB();
                
                console.log('[PWA] PWA features initialized successfully');
            } else {
                console.error('[PWA] PWA features not loaded - PWAFeatures class not found');
                console.log('[PWA] Available globals:', {
                    PWAFeatures: typeof PWAFeatures,
                    'window.PWAFeatures': typeof window.PWAFeatures,
                    'window.pwaFeatures': typeof window.pwaFeatures
                });

                // Try to load PWA features with a delay
                setTimeout(() => {
                    if (typeof PWAFeatures !== 'undefined' || typeof window.PWAFeatures !== 'undefined') {
                        console.log('[PWA] Retrying PWA initialization...');
                        initializePWA();
                    } else {
                        console.error('[PWA] PWA features still not available after retry');
                    }
                }, 1000);
            }
        }
        
        function updatePWAUsageData() {
            const usageData = {
                isInstalled: window.matchMedia('(display-mode: standalone)').matches,
                isStandalone: window.navigator.standalone === true,
                supportsPush: 'serviceWorker' in navigator && 'PushManager' in window,
                timestamp: new Date().toISOString()
            };
            
            fetch('<?php echo BASE_URL; ?>/api/pwa/usage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(usageData)
            }).catch(error => {
                if (window.PWA_CONFIG.debugMode) {
                    console.log('[PWA] Usage data update failed:', error);
                }
            });
        }
        

        
        function forceAppRefresh() {
            console.log('[PWA] Force refresh triggered by user');

            // Show loading indicator
            const refreshBtn = event.target.closest('.mobile-nav-item');
            const originalHTML = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Clearing...</span>';

            // Set a maximum timeout to prevent hanging
            const maxTimeout = setTimeout(() => {
                console.log('[PWA] Refresh timeout, forcing reload...');
                window.location.reload(true);
            }, 5000); // 5 second maximum

            // Simple and fast cache clearing
            const clearCaches = async () => {
                try {
                    // Quick storage clear
                    localStorage.clear();
                    sessionStorage.clear();

                    // Quick cache clear with timeout
                    if ('caches' in window) {
                        const cacheNames = await Promise.race([
                            caches.keys(),
                            new Promise(resolve => setTimeout(() => resolve([]), 2000))
                        ]);

                        if (cacheNames.length > 0) {
                            await Promise.race([
                                Promise.all(cacheNames.map(name => caches.delete(name))),
                                new Promise(resolve => setTimeout(resolve, 2000))
                            ]);
                        }
                    }

                    // Quick service worker unregister
                    if ('serviceWorker' in navigator) {
                        const registrations = await Promise.race([
                            navigator.serviceWorker.getRegistrations(),
                            new Promise(resolve => setTimeout(() => resolve([]), 1000))
                        ]);

                        registrations.forEach(reg => reg.unregister().catch(() => {}));
                    }

                } catch (e) {
                    console.log('[PWA] Cache clear error (continuing):', e);
                }
            };

            // Execute clearing and reload
            clearCaches().then(() => {
                clearTimeout(maxTimeout);
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Reloading...</span>';

                // Force reload with cache busting
                const timestamp = Date.now();
                const currentUrl = window.location.pathname + window.location.search;
                const separator = currentUrl.includes('?') ? '&' : '?';
                const newUrl = currentUrl + separator + 'refresh=' + timestamp + '&nocache=' + timestamp;

                window.location.replace(newUrl);

            }).catch(() => {
                clearTimeout(maxTimeout);
                console.log('[PWA] Cache clear failed, forcing reload...');
                window.location.reload(true);
            });
        }
        
        function addMobileNavigation() {
            // Create mobile bottom navigation
            const mobileNav = document.createElement('div');
            mobileNav.className = 'mobile-nav-bottom show';
            mobileNav.innerHTML = `
                <div class="mobile-nav-items">
                    <a href="<?php echo BASE_URL; ?>/" class="mobile-nav-item ${window.location.pathname === '/' ? 'active' : ''}">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>/calendar" class="mobile-nav-item ${window.location.pathname.includes('/calendar') ? 'active' : ''}">
                        <i class="fas fa-calendar"></i>
                        <span>Events</span>
                    </a>
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <a href="<?php echo BASE_URL; ?>/user/dashboard" class="mobile-nav-item ${window.location.pathname.includes('/dashboard') ? 'active' : ''}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="<?php echo BASE_URL; ?>/notification_center" class="mobile-nav-item ${window.location.pathname.includes('/notification_center') ? 'active' : ''}" id="mobile-messages-link">
                        <div class="position-relative">
                            <i class="fas fa-bell"></i>
                            <span class="mobile-notification-badge" id="mobile-notification-badge" style="display: none;"></span>
                        </div>
                        <span>Messages</span>
                    </a>
                    <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/auth/login" class="mobile-nav-item">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                    <?php endif; ?>
                    <a href="#" class="mobile-nav-item" data-qr-scanner>
                        <i class="fas fa-qrcode"></i>
                        <span>Scan</span>
                    </a>
                    <a href="#" class="mobile-nav-item" onclick="forceAppRefresh()" style="color: #1338BE;">
                        <i class="fas fa-sync-alt"></i>
                        <span>Refresh</span>
                    </a>
                </div>
            `;
            
            document.body.appendChild(mobileNav);
            
            // Adjust body padding for mobile nav
            document.body.style.paddingBottom = '80px';
        }
        
        function addQuickActionsFAB() {
            // Don't create FAB on desktop screens (992px and above)
            if (window.innerWidth >= 992) {
                return;
            }
            
            <?php if (isset($_SESSION['user_id'])): ?>
            const fab = document.createElement('div');
            fab.className = 'quick-actions-fab';
            
            let fabActions = '';
            
            // Only show Register Vehicle button if we're on a show page with open registration
            <?php 
            $showRegisterButton = false;
            if (isset($data['show']) && is_object($data['show'])) {
                // Check if registration is open for this show
                $now = new DateTime();
                $registrationStart = new DateTime($data['show']->registration_start);
                $registrationEnd = new DateTime($data['show']->registration_end);
                
                if ($now >= $registrationStart && $now <= $registrationEnd && $data['show']->status === 'published') {
                    $showRegisterButton = true;
                }
            }
            ?>
            
            <?php if ($showRegisterButton): ?>
            fabActions += `<button class="fab-action" onclick="window.location.href='<?php echo BASE_URL; ?>/registration/register/<?php echo $data['show']->id; ?>'" title="Register Vehicle">
                <i class="fas fa-car"></i>
            </button>`;
            <?php endif; ?>
            
            // Always show camera and QR scanner for logged-in users
            fabActions += `<button class="fab-action" data-camera-capture="vehicle_image" title="Take Photo">
                <i class="fas fa-camera"></i>
            </button>`;
            
            fabActions += `<button class="fab-action" data-qr-scanner title="Scan QR Code">
                <i class="fas fa-qrcode"></i>
            </button>`;
            
            // Show Create Show button based on user role
            <?php if (isset($_SESSION['user_role'])): ?>
                <?php if ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'coordinator'): ?>
                fabActions += `<button class="fab-action" onclick="window.location.href='<?php echo BASE_URL; ?>/admin/addShow'" title="Create Show">
                    <i class="fas fa-plus-circle"></i>
                </button>`;
                <?php elseif ($_SESSION['user_role'] === 'user'): ?>
                fabActions += `<button class="fab-action" onclick="window.location.href='<?php echo BASE_URL; ?>/user/createShow'" title="Create Show">
                    <i class="fas fa-plus-circle"></i>
                </button>`;
                <?php endif; ?>
            <?php endif; ?>
            
            fab.innerHTML = `
                <button class="fab-main" onclick="toggleQuickActions()">
                    <i class="fas fa-plus"></i>
                </button>
                <div class="fab-actions" id="fab-actions">
                    ${fabActions}
                </div>
            `;
            
            document.body.appendChild(fab);
            <?php endif; ?>
        }
        
        function toggleQuickActions() {
            const actions = document.getElementById('fab-actions');
            if (actions) {
                actions.classList.toggle('show');
            }
        }
        
        // Handle window resize to show/hide FAB based on screen size
        window.addEventListener('resize', function() {
            const fab = document.querySelector('.quick-actions-fab');
            
            if (window.innerWidth >= 992) {
                // Desktop - hide FAB if it exists
                if (fab) {
                    fab.style.display = 'none';
                }
            } else {
                // Mobile/Tablet - show FAB if it exists, or create it if it doesn't
                if (fab) {
                    fab.style.display = 'block';
                } else {
                    // Re-create FAB for mobile if it doesn't exist
                    addQuickActionsFAB();
                }
            }
        });
        
        // Handle online/offline status
        window.addEventListener('online', function() {
            if (window.pwaFeatures) {
                window.pwaFeatures.handleOnlineStatus();
            }
        });
        
        window.addEventListener('offline', function() {
            if (window.pwaFeatures) {
                window.pwaFeatures.handleOfflineStatus();
            }
        });
        
        // Handle app installation
        window.addEventListener('beforeinstallprompt', function(e) {
            e.preventDefault();
            if (window.pwaFeatures) {
                window.pwaFeatures.deferredPrompt = e;
                window.pwaFeatures.showInstallButton();
            }
        });
        
        // Handle app installed
        window.addEventListener('appinstalled', function() {
            if (window.pwaFeatures) {
                window.pwaFeatures.showWelcomeMessage();
            }
        });
    </script>
    
    <?php 
    // Clear cache buster from session after it's been used once
    if (isset($_SESSION['cache_buster'])) {
        unset($_SESSION['cache_buster']);
    }
    ?>
    
    <!-- Dropdown Fix Script - Revised -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Skip if we're on the image_editor/browse page (it has its own handling)
        if (window.location.pathname.includes('/image_editor/browse') || 
            document.body.classList.contains('image-browse-page')) {
            console.log('Skipping global dropdown fix for image browse page');
            return;
        }
        
        // Let Bootstrap handle the dropdown toggling
        // We'll just fix the positioning after Bootstrap shows the dropdown
        
        // This approach works with Bootstrap's native dropdown implementation
        document.addEventListener('shown.bs.dropdown', function(e) {
            const toggle = e.target;
            const dropdown = toggle.nextElementSibling;
            
            if (!dropdown || !dropdown.classList.contains('dropdown-menu')) {
                return;
            }
            
            // Get toggle button position
            const toggleRect = toggle.getBoundingClientRect();
            const isMobile = window.innerWidth < 768;
            
            // Fix dropdown position
            if (isMobile) {
                // On mobile, position with minimal gap
                dropdown.style.marginTop = '2px';
            } else {
                // On desktop, position with small gap
                dropdown.style.marginTop = '1px';
            }
            
            // Ensure dropdown doesn't go off-screen horizontally
            const dropdownRect = dropdown.getBoundingClientRect();
            if (dropdownRect.right > window.innerWidth) {
                // If dropdown goes off right edge, align it to the right of the toggle
                dropdown.style.left = 'auto';
                dropdown.style.right = '0';
            }
            
            // Fix for dropdowns inside tables or other containers with overflow hidden
            if (dropdown.closest('.table-responsive') || 
                dropdown.closest('.overflow-hidden') ||
                dropdown.closest('.card-body')) {
                
                // Move dropdown to body for proper display
                const dropdownClone = dropdown.cloneNode(true);
                
                // Note: We can't reliably copy event listeners across cloned nodes
                // So we'll just make sure the dropdown works for basic clicking
                
                // Position the cloned dropdown
                dropdownClone.style.position = 'absolute';
                dropdownClone.style.top = (toggleRect.bottom + window.scrollY) + 'px';
                dropdownClone.style.left = (toggleRect.left + window.scrollX) + 'px';
                dropdownClone.style.zIndex = '999999';
                
                // Replace the original dropdown
                dropdown.style.display = 'none';
                document.body.appendChild(dropdownClone);
                
                // Clean up when dropdown is hidden
                document.addEventListener('hidden.bs.dropdown', function handler() {
                    if (document.body.contains(dropdownClone)) {
                        document.body.removeChild(dropdownClone);
                    }
                    dropdown.style.display = '';
                    document.removeEventListener('hidden.bs.dropdown', handler);
                }, { once: true });
            }
        });
    });
    </script>
</body>
</html>