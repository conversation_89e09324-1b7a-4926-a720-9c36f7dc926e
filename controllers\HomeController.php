<?php
/**
 * Home Controller
 * 
 * This controller handles the home page and general site navigation.
 */
class HomeController extends Controller {
    private $showModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->showModel = $this->model('ShowModel');
    }
    
    /**
     * Index method - Home page
     */
    public function index() {
        // Get upcoming shows
        $upcomingShows = $this->showModel->getUpcomingShows(5);
        
        $data = [
            'title' => 'Home',
            'upcoming_shows' => $upcomingShows
        ];
        
        $this->view('home/index', $data);
    }
    
    /**
     * About page
     */
    public function about() {
        $data = [
            'title' => 'About Us',
            'content' => 'Car Show Judge is a comprehensive application for managing car shows, judging, registrations, and vehicle displays.'
        ];
        
        $this->view('home/about', $data);
    }
    
    /**
     * Contact page
     */
    public function contact() {
        // Initialize form data with all required variables
        $data = [
            'title' => 'Contact Us',
            'name' => '',
            'email' => '',
            'subject' => '',
            'message' => '',
            'name_err' => '',
            'email_err' => '',
            'subject_err' => '',
            'message_err' => '',
            'captcha_err' => '',
            'success_message' => '',
            'captcha_question' => '',
            'captcha_hash' => ''
        ];

        // Generate math CAPTCHA
        $captcha = $this->generateMathCaptcha();
        $data['captcha_question'] = $captcha['question'];
        $data['captcha_hash'] = $captcha['hash'];

        // Check for success message from session
        if (isset($_SESSION['contact_success'])) {
            $data['success_message'] = $_SESSION['contact_success'];
            unset($_SESSION['contact_success']);
        }

        // Debug: Log data types when logged in to identify conflicts
        if (isset($_SESSION['user_id'])) {
            foreach (['name', 'email', 'subject', 'message'] as $field) {
                if (isset($data[$field]) && !is_string($data[$field])) {
                    error_log("HomeController::contact - Field '{$field}' is not a string: " . gettype($data[$field]));
                    $data[$field] = ''; // Force to empty string
                }
            }
        }

        // Process form submission
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize input data
            $data['name'] = trim($_POST['name'] ?? '');
            $data['email'] = trim($_POST['email'] ?? '');
            $data['subject'] = trim($_POST['subject'] ?? '');
            $data['message'] = trim($_POST['message'] ?? '');
            $captcha_answer = trim($_POST['captcha'] ?? '');
            $captcha_hash = $_POST['captcha_hash'] ?? '';

            // Reset error messages
            $data['name_err'] = '';
            $data['email_err'] = '';
            $data['subject_err'] = '';
            $data['message_err'] = '';
            $data['captcha_err'] = '';

            // Validate form fields
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter your name';
            }

            if (empty($data['email'])) {
                $data['email_err'] = 'Please enter your email';
            } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $data['email_err'] = 'Please enter a valid email address';
            }

            if (empty($data['subject'])) {
                $data['subject_err'] = 'Please enter a subject';
            }

            if (empty($data['message'])) {
                $data['message_err'] = 'Please enter your message';
            }

            // Validate CAPTCHA
            if (empty($captcha_answer)) {
                $data['captcha_err'] = 'Please solve the math problem';
            } elseif (!$this->verifyMathCaptcha($captcha_answer, $captcha_hash)) {
                $data['captcha_err'] = 'Incorrect answer. Please try again.';
                // Generate new CAPTCHA
                $captcha = $this->generateMathCaptcha();
                $data['captcha_question'] = $captcha['question'];
                $data['captcha_hash'] = $captcha['hash'];
            }

            // If no errors, send message to all admins
            if (empty($data['name_err']) && empty($data['email_err']) &&
                empty($data['subject_err']) && empty($data['message_err']) &&
                empty($data['captcha_err'])) {

                if ($this->sendContactMessageToAdmins($data)) {
                    $_SESSION['contact_success'] = 'Your message has been sent successfully! An administrator will respond to you soon.';
                    $this->redirect('home/contact');
                    return;
                } else {
                    $data['message_err'] = 'Failed to send message. Please try again later.';
                    // Generate new CAPTCHA on error
                    $captcha = $this->generateMathCaptcha();
                    $data['captcha_question'] = $captcha['question'];
                    $data['captcha_hash'] = $captcha['hash'];
                }
            }
        }

        $this->view('home/contact', $data);
    }
    
    /**
     * Dashboard - redirects to appropriate dashboard based on user role
     */
    public function dashboard() {
        // Check if user is logged in
        $auth = new Auth();
        if (!$auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Redirect based on role
        $role = $auth->getCurrentUserRole();
        
        switch ($role) {
            case 'admin':
                $this->redirect('admin/dashboard');
                break;
            case 'coordinator':
                $this->redirect('coordinator/dashboard');
                break;
            case 'judge':
                $this->redirect('judge/dashboard');
                break;
            default:
                $this->redirect('user/dashboard');
                break;
        }
    }
    
    /**
     * Error page
     * 
     * @param string $message Error message
     */
    public function error($message = 'An error occurred') {
        $data = [
            'title' => 'Error',
            'message' => urldecode($message)
        ];
        
        $this->view('home/error', $data);
    }
    
    /**
     * Not found page
     */
    public function notFound() {
        $data = [
            'title' => 'Page Not Found',
            'message' => 'The page you are looking for could not be found.'
        ];
        
        $this->view('home/not_found', $data);
    }
    
    /**
     * Access denied page
     */
    public function accessDenied() {
        $data = [
            'title' => 'Access Denied',
            'message' => 'You do not have permission to access this page.'
        ];

        $this->view('home/access_denied', $data);
    }

    /**
     * Generate a math-based CAPTCHA that can't be copy-pasted
     */
    private function generateMathCaptcha() {
        // Generate two random numbers
        $num1 = rand(1, 20);
        $num2 = rand(1, 20);

        // Randomly choose operation
        $operations = ['+', '-', '*'];
        $operation = $operations[array_rand($operations)];

        // Calculate answer
        switch ($operation) {
            case '+':
                $answer = $num1 + $num2;
                break;
            case '-':
                // Ensure positive result
                if ($num1 < $num2) {
                    $temp = $num1;
                    $num1 = $num2;
                    $num2 = $temp;
                }
                $answer = $num1 - $num2;
                break;
            case '*':
                // Use smaller numbers for multiplication
                $num1 = rand(1, 10);
                $num2 = rand(1, 10);
                $answer = $num1 * $num2;
                break;
        }

        // Create question with words to make it harder to parse
        $question = "What is {$num1} {$operation} {$num2}?";

        // Store answer in session for verification with a unique key
        $sessionKey = 'captcha_answer_' . time();
        $_SESSION[$sessionKey] = $answer;

        // Create a hash that includes the session key
        $hash = base64_encode($sessionKey);

        return [
            'question' => $question,
            'answer' => $answer,
            'hash' => $hash
        ];
    }

    /**
     * Verify math CAPTCHA answer
     */
    private function verifyMathCaptcha($userAnswer, $hash) {
        try {
            // Decode the session key from the hash
            $sessionKey = base64_decode($hash);

            if (isset($_SESSION[$sessionKey])) {
                $correctAnswer = $_SESSION[$sessionKey];
                unset($_SESSION[$sessionKey]); // Use once

                // Compare answers (convert to int to handle string/number differences)
                $result = (int)$userAnswer === (int)$correctAnswer;

                // Debug logging
                error_log("CAPTCHA Verification: User={$userAnswer}, Correct={$correctAnswer}, Result=" . ($result ? 'PASS' : 'FAIL'));

                return $result;
            }

            error_log("CAPTCHA Verification: Session key not found: {$sessionKey}");
            return false;

        } catch (Exception $e) {
            error_log("CAPTCHA Verification Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send contact message to all administrators using unified messaging system
     */
    private function sendContactMessageToAdmins($data) {
        try {
            // Load unified message model
            $messageModel = $this->model('UnifiedMessageModel');

            // Get all users with admin role
            $db = new Database();
            $db->query("SELECT id FROM users WHERE role = 'admin' AND status = 'active'");
            $admins = $db->resultSet();

            if (empty($admins)) {
                error_log('HomeController::sendContactMessageToAdmins - No active admins found');
                return false;
            }

            // Prepare message content with [reply] to enable replies
            $subject = 'Contact Form: ' . $data['subject'];
            $message = "New contact form submission:\n\n";
            $message .= "Name: " . $data['name'] . "\n";
            $message .= "Email: " . $data['email'] . "\n";
            $message .= "Subject: " . $data['subject'] . "\n\n";
            $message .= "Message:\n" . $data['message'] . "\n\n";
            $message .= "---\n";
            $message .= "This message was sent via the contact form on " . date('Y-m-d H:i:s') . "\n";
            $message .= "Reply directly to: " . $data['email'] . "\n\n";
            $message .= "[reply]\n"; // Enable reply functionality
            $message .= "CONTACT_EMAIL:" . $data['email']; // Special marker for email-only replies

            $sentCount = 0;

            // Send message to each admin (from system user ID 1)
            foreach ($admins as $admin) {
                $messageId = $messageModel->sendMessage(
                    1, // From system user
                    $admin->id, // To admin
                    $subject,
                    $message,
                    null, // No show ID
                    'system', // System message type
                    true, // Requires reply (because of [reply] tag)
                    null // Not a reply
                );

                if ($messageId) {
                    $sentCount++;
                }
            }

            error_log("HomeController::sendContactMessageToAdmins - Sent to {$sentCount} of " . count($admins) . " admins");

            return $sentCount > 0;

        } catch (Exception $e) {
            error_log('HomeController::sendContactMessageToAdmins - Error: ' . $e->getMessage());
            return false;
        }
    }
}