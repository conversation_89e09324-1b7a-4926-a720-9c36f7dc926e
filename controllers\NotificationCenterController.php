<?php
/**
 * Notification Center Controller
 * 
 * This controller handles the notification center functionality including
 * viewing notifications, messages, and handling replies.
 */
class NotificationCenterController extends Controller {
    private $auth;
    private $db;
    private $notificationCenterModel;
    private $userModel;
    private $showModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Call parent constructor first
        parent::__construct();

        // Initialize core dependencies
        $this->auth = new Auth();
        $this->db = new Database();
        
        // Require login
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Initialize models - use unified message system
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        $this->notificationCenterModel = new UnifiedMessageModel();
        $this->userModel = $this->model('UserModel');
        $this->showModel = $this->model('ShowModel');
    }
    
    /**
     * Default index method - shows notification center
     */
    public function index() {
        $this->center();
    }
    
    /**
     * Main notification center page - unified messages view
     */
    public function center() {
        $userId = $this->auth->getCurrentUserId();

        // Check if user can use notification system
        $canSendNotifications = $this->notificationCenterModel->canUserSendNotifications($userId);

        // Get filter parameters (simplified - no more artificial type distinctions)
        $status = $_GET['status'] ?? 'all';
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        // Get all messages (unified view)
        $messages = $this->notificationCenterModel->getUserMessages(
            $userId, 
            $status, 
            $limit, 
            $offset
        );
        
        // Get counts for badges - use the reliable database counts
        $counts = $this->notificationCenterModel->getMessageCounts($userId);
        
        // Get conversation counts for tabs
        $conversationCounts = $this->notificationCenterModel->getConversationCounts($userId);
        
        // Calculate pagination
        $totalCount = $counts['total_count'];
        if ($status === 'unread') {
            $totalCount = $counts['total_unread'];
        } elseif ($status === 'archived') {
            $totalCount = $counts['archived_count'];
        }
        
        $totalPages = (int)ceil($totalCount / $limit);
        


        $data = [
            'title' => 'Messages',
            'messages' => $messages,
            'counts' => $counts,
            'conversationCounts' => $conversationCounts,
            'current_status' => $status,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_count' => $totalCount,
            'can_send_notifications' => $canSendNotifications,
            'user_id' => $userId
        ];
        
        parent::view('notification_center/index', $data);
    }
    
    /**
     * View a specific notification
     */
    public function viewNotification($notificationId) {
        $userId = $this->auth->getCurrentUserId();
        $notificationId = (int)$notificationId;

        // Debug logging
        error_log("NotificationCenterController::viewNotification - User: {$userId}, Notification: {$notificationId}");

        if ($notificationId <= 0) {
            error_log("NotificationCenterController::viewNotification - Invalid notification ID: {$notificationId}");
            $this->setFlashMessage('error', 'Invalid notification ID', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Get message and its thread
        $message = $this->notificationCenterModel->getMessageById($notificationId, $userId);

        if (!$message) {
            error_log("NotificationCenterController::viewNotification - Message not found: ID {$notificationId} for user {$userId}");
            $this->setFlashMessage('error', 'Message not found or access denied', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Get the full conversation thread
        $messageThread = $this->notificationCenterModel->getMessageThread($notificationId, $userId);
        
        error_log("NotificationCenterController::viewNotification - Successfully loaded message: " . $message->subject);
        error_log("NotificationCenterController::viewNotification - Thread contains " . count($messageThread) . " messages");
        
        // Mark as read
        $this->notificationCenterModel->markAsRead($notificationId, $userId);
        
        $data = [
            'title' => 'View Message',
            'message' => $message,
            'messageThread' => $messageThread,
            'currentUserId' => $userId
        ];
        
        parent::view('notification_center/view', $data);
    }
    
    /**
     * View a specific message (unified system)
     */
    public function viewMessage($messageId) {
        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)$messageId;

        if ($messageId <= 0) {
            $this->setFlashMessage('error', 'Invalid message ID', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Get message using unified system
        $message = $this->notificationCenterModel->getMessageById($messageId, $userId);

        if (!$message) {
            $this->setFlashMessage('error', 'Message not found or access denied', 'danger');
            $this->redirect('notification_center');
            return;
        }
        
        // Mark as read
        $this->notificationCenterModel->markAsRead($messageId, $userId);
        
        // Get the full conversation thread
        $messageThread = $this->notificationCenterModel->getMessageThread($messageId, $userId);
        
        // Check if user can send notifications
        $canSendNotifications = $this->notificationCenterModel->canUserSendNotifications($userId);
        
        $data = [
            'title' => 'View Message',
            'message' => $message,
            'messageThread' => $messageThread,
            'currentUserId' => $userId,
            'can_send_notifications' => $canSendNotifications
        ];
        
        parent::view('notification_center/view', $data);
    }
    
    /**
     * Send a reply to a message
     */
    public function reply() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('notification_center');
            return;
        }

        $userId = $this->auth->getCurrentUserId();

        // Check if user can send notifications
        if (!$this->notificationCenterModel->canUserSendNotifications($userId)) {
            $this->setFlashMessage('error', 'You cannot send messages because all your notification types are disabled. Please enable at least one notification type in your settings to send messages.', 'warning');
            $this->redirect('notification_center');
            return;
        }

        $parentMessageId = (int)($_POST['parent_message_id'] ?? 0);
        $message = trim($_POST['message'] ?? '');

        if ($parentMessageId <= 0 || empty($message)) {
            $this->setFlashMessage('error', 'Please provide a valid message', 'danger');
            $this->redirect('notification_center');
            return;
        }

        // Check if user can reply to this specific message
        if (!$this->notificationCenterModel->canUserReplyToMessage($userId, $parentMessageId)) {
            $this->setFlashMessage('error', 'You cannot reply to this message. Either it does not allow replies, you have already replied, or you do not have permission.', 'warning');
            $this->redirect('notification_center');
            return;
        }
        
        try {
            // Get the original message
            $originalMessage = $this->notificationCenterModel->getMessageById($parentMessageId);
            
            if (!$originalMessage || $originalMessage->to_user_id != $userId) {
                $this->setFlashMessage('error', 'Message not found or access denied', 'danger');
                $this->redirect('notification_center');
                return;
            }
            
            // Send the reply
            $replyId = $this->notificationCenterModel->sendReply(
                $userId,
                $originalMessage->from_user_id,
                $originalMessage->subject,
                $message,
                $parentMessageId,
                $originalMessage->show_id
            );
            
            if ($replyId) {
                $this->setFlashMessage('success', 'Reply sent successfully', 'success');
            } else {
                $this->setFlashMessage('error', 'Failed to send reply', 'danger');
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::reply - Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to send reply', 'danger');
        }
        
        $this->redirect('notification_center');
    }
    
    /**
     * Mark notification as read (AJAX)
     */
    public function markRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);
        
        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }
        
        try {
            $success = $this->notificationCenterModel->markAsRead($messageId, $userId);
            
            if ($success) {
                // Get updated counts
                $counts = $this->notificationCenterModel->getMessageCounts($userId);
                $this->jsonResponse([
                    'success' => true,
                    'counts' => $counts
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to mark as read']);
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::markRead - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
    
    /**
     * Mark all notifications as read (AJAX)
     */
    public function markAllRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $type = $_POST['type'] ?? 'all';
        
        try {
            $success = $this->notificationCenterModel->markAllAsRead($userId, $type);
            
            if ($success) {
                // Get updated counts from database
                $counts = $this->notificationCenterModel->getMessageCounts($userId);
                
                $this->jsonResponse([
                    'success' => true,
                    'counts' => $counts
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to mark all as read']);
            }
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::markAllRead - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
    
    /**
     * Get unread notification count for header badge (AJAX)
     */
    public function getUnreadCount() {
        $userId = $this->auth->getCurrentUserId();
        
        try {
            // Use the reliable database counts
            $counts = $this->notificationCenterModel->getMessageCounts($userId);
            $conversationCounts = $this->notificationCenterModel->getConversationCounts($userId);
            
            $this->jsonResponse([
                'success' => true,
                'total_unread' => $counts['total_unread'],
                'counts' => $counts,
                'conversationCounts' => $conversationCounts
            ]);
            
        } catch (Exception $e) {
            error_log("NotificationCenterController::getUnreadCount - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'total_unread' => 0]);
        }
    }
    
    /**
     * Get thread info for confirmation dialogs
     */
    public function getThreadInfo() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $threadInfo = $this->notificationCenterModel->getThreadInfo($messageId, $userId);
            
            if ($threadInfo) {
                $this->jsonResponse([
                    'success' => true, 
                    'total_messages' => $threadInfo->total_messages,
                    'self_replies' => $threadInfo->self_replies
                ]);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Thread not found']);
            }
        } catch (Exception $e) {
            error_log("NotificationCenterController::getThreadInfo - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }

    /**
     * Archive a notification
     */
    public function archive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $success = $this->notificationCenterModel->archiveThread($messageId, $userId);

            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Thread archived successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to archive thread']);
            }

        } catch (Exception $e) {
            error_log("NotificationCenterController::archive - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }

    /**
     * Unarchive a notification
     */
    public function unarchive() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $success = $this->notificationCenterModel->unarchiveThread($messageId, $userId);

            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Thread restored successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to restore thread']);
            }

        } catch (Exception $e) {
            error_log("NotificationCenterController::unarchive - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }

    /**
     * Delete a notification permanently
     */
    public function delete() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $messageId = (int)($_POST['message_id'] ?? 0);

        if ($messageId <= 0) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid message ID']);
            return;
        }

        try {
            $success = $this->notificationCenterModel->deleteThread($messageId, $userId);

            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Thread deleted successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to delete thread']);
            }

        } catch (Exception $e) {
            error_log("NotificationCenterController::delete - Error: " . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => 'Server error']);
        }
    }
}
