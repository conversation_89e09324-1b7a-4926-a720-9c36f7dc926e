<?php
/**
 * DEPRECATED: This file has been moved to PwaController::fcmSubscribe()
 * 
 * Use: /api/pwa/fcm-subscribe instead
 */

header('Content-Type: application/json');
http_response_code(301);
echo json_encode([
    'success' => false,
    'error' => 'This endpoint has been moved',
    'new_endpoint' => '/api/pwa/fcm-subscribe',
    'message' => 'Please update your client to use the new endpoint'
]);
?>

try {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Authentication required'
        ]);
        exit;
    }
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['fcm_token'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'FCM token is required'
        ]);
        exit;
    }
    
    $userId = $_SESSION['user_id'];
    $fcmToken = trim($input['fcm_token']);
    $userAgent = $input['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? '';
    $isSubscriptionChange = $input['subscription_change'] ?? false;
    
    // Validate FCM token format
    if (empty($fcmToken) || strlen($fcmToken) < 50) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid FCM token format'
        ]);
        exit;
    }
    
    // Initialize database
    $db = new Database();
    
    // Check if FCM tokens table exists, create if not
    $db->query("SHOW TABLES LIKE 'fcm_tokens'");
    if (!$db->single()) {
        createFCMTokensTable($db);
    }
    
    // Check if token already exists for this user
    $db->query('SELECT id FROM fcm_tokens WHERE user_id = :user_id AND token = :token');
    $db->bind(':user_id', $userId);
    $db->bind(':token', $fcmToken);
    $existing = $db->single();
    
    if ($existing) {
        // Update existing token
        $db->query('UPDATE fcm_tokens SET 
                    user_agent = :user_agent,
                    active = 1,
                    last_used = NOW(),
                    updated_at = NOW()
                    WHERE id = :id');
        $db->bind(':id', $existing->id);
        $db->bind(':user_agent', $userAgent);
        
        if ($db->execute()) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[FCM] Updated existing token for user {$userId}");
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'FCM token updated successfully',
                'token_id' => $existing->id
            ]);
        } else {
            throw new Exception('Failed to update FCM token');
        }
    } else {
        // Allow multiple active tokens per user (one per device)
        // Only deactivate tokens from the SAME device/browser, not all user tokens

        // Extract device identifier from user agent for better device detection
        function extractDeviceIdentifier($userAgent) {
            if (empty($userAgent)) {
                return 'unknown';
            }

            $patterns = [
                'iPhone' => '/iPhone/',
                'iPad' => '/iPad/',
                'Android' => '/Android/',
                'Chrome' => '/Chrome\/[\d.]+/',
                'Firefox' => '/Firefox\/[\d.]+/',
                'Safari' => '/Safari\/[\d.]+/',
                'Edge' => '/Edge\/[\d.]+/',
                'Windows' => '/Windows NT/',
                'Mac' => '/Mac OS X/',
                'Linux' => '/Linux/'
            ];

            $deviceParts = [];
            foreach ($patterns as $name => $pattern) {
                if (preg_match($pattern, $userAgent)) {
                    $deviceParts[] = $name;
                }
            }

            return !empty($deviceParts) ? implode('-', $deviceParts) : 'unknown';
        }

        $deviceId = extractDeviceIdentifier($userAgent);

        // Deactivate old tokens from the SAME device only
        $db->query('UPDATE fcm_tokens SET active = 0
                    WHERE user_id = :user_id
                    AND (user_agent = :user_agent OR user_agent LIKE :device_pattern)');
        $db->bind(':user_id', $userId);
        $db->bind(':user_agent', $userAgent);
        $db->bind(':device_pattern', '%' . $deviceId . '%');
        $db->execute();

        // Insert new token (allows multiple active tokens per user, one per device)
        $db->query('INSERT INTO fcm_tokens
                    (user_id, token, user_agent, active, created_at, updated_at, last_used)
                    VALUES (:user_id, :token, :user_agent, 1, NOW(), NOW(), NOW())');
        $db->bind(':user_id', $userId);
        $db->bind(':token', $fcmToken);
        $db->bind(':user_agent', $userAgent);
        
        if ($db->execute()) {
            $tokenId = $db->lastInsertId();
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[FCM] Saved new token for user {$userId}, token ID: {$tokenId}");
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'FCM token saved successfully',
                'token_id' => $tokenId
            ]);
        } else {
            throw new Exception('Failed to save FCM token');
        }
    }
    
} catch (Exception $e) {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log('[FCM] Subscription error: ' . $e->getMessage());
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Create FCM tokens table
 */
function createFCMTokensTable($db) {
    $sql = "CREATE TABLE fcm_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token TEXT NOT NULL,
        user_agent TEXT,
        active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_active (active),
        INDEX idx_user_active (user_id, active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    if ($db->execute()) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[FCM] Created fcm_tokens table');
        }
    } else {
        throw new Exception('Failed to create fcm_tokens table');
    }
}
?>