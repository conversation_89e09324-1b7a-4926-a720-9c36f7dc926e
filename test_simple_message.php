<?php
/**
 * Simple Message Test
 * Tests the basic sendMessage functionality without complex features
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/UnifiedMessageModel.php';

echo "<h1>🧪 Simple Message Test</h1>";

try {
    $messageModel = new UnifiedMessageModel();
    
    // Simple test parameters
    $fromUserId = 1;
    $toUserId = 1;
    $subject = "Test Message";
    $message = "This is a simple test message.";
    
    echo "<h2>📋 Test Parameters</h2>";
    echo "<p><strong>From:</strong> User {$fromUserId}</p>";
    echo "<p><strong>To:</strong> User {$toUserId}</p>";
    echo "<p><strong>Subject:</strong> {$subject}</p>";
    echo "<p><strong>Message:</strong> {$message}</p>";
    
    echo "<h2>📤 Sending Message</h2>";
    echo "<p>Calling sendMessage()...</p>";
    
    $result = $messageModel->sendMessage($fromUserId, $toUserId, $subject, $message);
    
    if ($result) {
        echo "<p style='color: green;'>✅ SUCCESS! Message sent with ID: {$result}</p>";
        
        // Verify the message was stored
        echo "<h3>🔍 Verification</h3>";
        $storedMessage = $messageModel->getMessageById($result);
        
        if ($storedMessage) {
            echo "<p style='color: green;'>✅ Message found in database</p>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Field</th><th>Value</th></tr>";
            echo "<tr><td>ID</td><td>{$storedMessage->id}</td></tr>";
            echo "<tr><td>From User</td><td>{$storedMessage->from_user_id}</td></tr>";
            echo "<tr><td>To User</td><td>{$storedMessage->to_user_id}</td></tr>";
            echo "<tr><td>Subject</td><td>" . htmlspecialchars($storedMessage->subject) . "</td></tr>";
            echo "<tr><td>Message</td><td>" . htmlspecialchars($storedMessage->message) . "</td></tr>";
            echo "<tr><td>Is Read</td><td>" . ($storedMessage->is_read ? 'Yes' : 'No') . "</td></tr>";
            echo "<tr><td>Created</td><td>{$storedMessage->created_at}</td></tr>";
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ Message not found in database</p>";
        }
        
        // Check delivery attempts
        echo "<h3>📊 Delivery Status</h3>";
        $deliveries = $messageModel->getMessageDeliveries($result);
        
        if ($deliveries && count($deliveries) > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>Method</th><th>Status</th><th>Error</th></tr>";
            foreach ($deliveries as $delivery) {
                $statusColor = $delivery->status === 'sent' ? 'green' : ($delivery->status === 'failed' ? 'red' : 'orange');
                echo "<tr>";
                echo "<td>{$delivery->delivery_method}</td>";
                echo "<td style='color: {$statusColor};'>{$delivery->status}</td>";
                echo "<td>" . htmlspecialchars($delivery->error_message ?? 'None') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ No delivery records found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ FAILED! sendMessage() returned false</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h3>Stack Trace:</h3>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
