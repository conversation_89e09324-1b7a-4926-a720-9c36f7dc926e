<?php
/**
 * Database Class
 * 
 * This class handles database connections and provides methods for executing queries.
 */
class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;
    
    private $dbh;
    private $stmt;
    private $error;
    
    /**
     * Constructor - Creates a PDO connection
     */
    public function __construct() {
        // Include timezone helper functions for site-wide availability
        if (!function_exists('formatDateTimeForUser')) {
            require_once APPROOT . '/helpers/timezone_helper.php';
        }
        
        // Set DSN
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname;
        
        // Set options
        $options = [
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_OBJ,
            PDO::ATTR_EMULATE_PREPARES => false
        ];
        
        // Create PDO instance
        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
            // Set charset to ensure proper encoding
            $this->dbh->exec("SET NAMES utf8mb4");
        } catch (PDOException $e) {
            $this->error = $e->getMessage();
            // Log the error with more details for debugging
            error_log('Database Connection Error: ' . $this->error);
            error_log('Connection attempted to: ' . $this->host . ' with user: ' . $this->user);
            
            // Show a user-friendly error message without exposing sensitive details
            die('Database connection failed. Please check your configuration or contact the administrator.');
        }
    }
    
    /**
     * Prepare statement with query
     * 
     * @param string $sql SQL query
     * @return void
     */
    public function query($sql) {
        $this->stmt = $this->dbh->prepare($sql);
    }
    
    /**
     * Bind values to prepared statement
     * 
     * @param string $param Parameter name
     * @param mixed $value Parameter value
     * @param mixed $type Parameter type
     * @return void
     */
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        
        $this->stmt->bindValue($param, $value, $type);
    }
    
    /**
     * Execute the prepared statement
     * 
     * @return bool
     */
    public function execute() {
        try {
            // Execute the statement
            $result = $this->stmt->execute();
            
            // Only log failures or important updates
            if (!$result) {
                // Get the SQL query with bound parameters for logging
                ob_start();
                $this->stmt->debugDumpParams();
                $debugInfo = ob_get_clean();
                error_log('SQL FAILED: ' . $debugInfo);
            }
            
            return $result;
        } catch (PDOException $e) {
            // Get the SQL query with bound parameters for logging
            ob_start();
            $this->stmt->debugDumpParams();
            $debugInfo = ob_get_clean();
            
            error_log('SQL ERROR: ' . $e->getMessage());
            error_log('Failed SQL: ' . $debugInfo);
            
            throw $e; // Re-throw the exception to be caught by the calling code
        }
    }
    
    /**
     * Get result set as array of objects
     * 
     * @return array
     */
    public function resultSet() {
        try {
            // Log the query if in debug mode
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                ob_start();
                $this->stmt->debugDumpParams();
                $debugInfo = ob_get_clean();
                error_log('Database::resultSet - Executing query: ' . $debugInfo);
            }
            
            $this->execute();
            $result = $this->stmt->fetchAll();
            
            // Log the result count if in debug mode
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $count = is_array($result) ? count($result) : 0;
                error_log('Database::resultSet - Result count: ' . $count);
                
                if ($count > 0) {
                    error_log('Database::resultSet - First result: ' . json_encode($result[0]));
                } else {
                    error_log('Database::resultSet - No results found');
                }
            }
            
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log('Database error in resultSet: ' . $e->getMessage());
            error_log('Database error trace: ' . $e->getTraceAsString());
            return [];
        }
    }
    
    /**
     * Get single record as object
     * 
     * @return object
     */
    public function single() {
        $this->execute();
        return $this->stmt->fetch();
    }
    
    /**
     * Get row count
     * 
     * @return int
     */
    public function rowCount() {
        return $this->stmt->rowCount();
    }
    
    /**
     * Get last inserted ID
     * 
     * @return int
     */
    public function lastInsertId() {
        return $this->dbh->lastInsertId();
    }
    
    /**
     * Begin a transaction
     * 
     * @return bool
     */
    public function beginTransaction() {
        return $this->dbh->beginTransaction();
    }
    
    /**
     * Commit a transaction
     * 
     * @return bool
     */
    public function commit() {
        return $this->dbh->commit();
    }
    
    /**
     * Rollback a transaction
     *
     * @return bool
     */
    public function rollBack() {
        return $this->dbh->rollBack();
    }

    /**
     * Check if we're in a transaction
     *
     * @return bool
     */
    public function inTransaction() {
        return $this->dbh->inTransaction();
    }
    
    /**
     * Get the PDO connection instance
     * 
     * @return PDO
     */
    public function getConnection() {
        return $this->dbh;
    }
}