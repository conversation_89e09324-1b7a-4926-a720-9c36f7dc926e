<?php
/**
 * Test Contact Form Messaging
 * 
 * Tests the enhanced contact form with CAPTCHA and unified messaging to admins
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>📧 Test Contact Form Messaging</h1>";

try {
    $db = new Database();
    
    echo "<h2>🎯 Contact Form Enhanced</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Features Added:</h3>";
    echo "<ul>";
    echo "<li><strong>CAPTCHA verification:</strong> Simple text-based CAPTCHA to prevent spam</li>";
    echo "<li><strong>Unified messaging:</strong> Messages sent to all active administrators</li>";
    echo "<li><strong>Form validation:</strong> Comprehensive validation for all fields</li>";
    echo "<li><strong>System integration:</strong> Uses existing unified messaging system</li>";
    echo "<li><strong>Admin notifications:</strong> All admins receive contact form submissions</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Current Admin Users</h2>";
    
    // Get all admin users
    $db->query("SELECT id, name, email, status FROM users WHERE role = 'admin' ORDER BY name");
    $admins = $db->resultSet();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Admin ID</th><th>Name</th><th>Email</th><th>Status</th><th>Will Receive Messages</th></tr>";
    
    if (!empty($admins)) {
        foreach ($admins as $admin) {
            $willReceive = $admin->status === 'active' ? '✅ Yes' : '❌ No (Inactive)';
            $statusColor = $admin->status === 'active' ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>{$admin->id}</td>";
            echo "<td>" . htmlspecialchars($admin->name) . "</td>";
            echo "<td>" . htmlspecialchars($admin->email) . "</td>";
            echo "<td style='color: {$statusColor};'>" . ucfirst($admin->status) . "</td>";
            echo "<td style='color: " . ($admin->status === 'active' ? 'green' : 'red') . ";'>{$willReceive}</td>";
            echo "</tr>";
        }
    } else {
        echo "<tr><td colspan='5' style='text-align: center; color: red;'>⚠️ No admin users found!</td></tr>";
    }
    echo "</table>";
    
    $activeAdminCount = 0;
    foreach ($admins as $admin) {
        if ($admin->status === 'active') {
            $activeAdminCount++;
        }
    }
    
    echo "<p><strong>Active admins who will receive contact messages: {$activeAdminCount}</strong></p>";
    
    echo "<h2>🧪 Testing Instructions</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Testing Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Navigate to contact page:</strong> /home/<USER>/li>";
    echo "<li><strong>Fill out the form:</strong>";
    echo "<ul>";
    echo "<li>Enter your name</li>";
    echo "<li>Enter a valid email address</li>";
    echo "<li>Enter a subject</li>";
    echo "<li>Enter your message</li>";
    echo "<li>Complete the CAPTCHA verification</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Submit the form:</strong> Click 'Send Message'</li>";
    echo "<li><strong>Check admin notifications:</strong> Login as admin and check notification center</li>";
    echo "<li><strong>Verify message content:</strong> Should include sender info and original message</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🎯 Form Features</h2>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Feature</th><th>Description</th><th>Purpose</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>CAPTCHA Verification</strong></td>";
    echo "<td>6-character alphanumeric code</td>";
    echo "<td>Prevent automated spam submissions</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Form Validation</strong></td>";
    echo "<td>Required fields, email format validation</td>";
    echo "<td>Ensure complete and valid submissions</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>CSRF Protection</strong></td>";
    echo "<td>CSRF token validation</td>";
    echo "<td>Prevent cross-site request forgery</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Unified Messaging</strong></td>";
    echo "<td>Messages sent via UnifiedMessageModel</td>";
    echo "<td>Consistent notification delivery</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Admin Distribution</strong></td>";
    echo "<td>Sent to all active admin users</td>";
    echo "<td>Ensure contact requests are handled</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h2>🔧 Technical Implementation</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📧 CAPTCHA System:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// Generate CAPTCHA
private function generateCaptcha() {
    \$characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    \$captcha = '';
    for (\$i = 0; \$i < 6; \$i++) {
        \$captcha .= \$characters[rand(0, strlen(\$characters) - 1)];
    }
    return \$captcha;
}

// Verify CAPTCHA
private function verifyCaptcha(\$input, \$hash) {
    return password_verify(strtoupper(\$input), \$hash);
}";
    echo "</pre>";
    
    echo "<h3>📨 Message Distribution:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "// Get all active admins
\$db->query(\"SELECT id FROM users WHERE role = 'admin' AND status = 'active'\");
\$admins = \$db->resultSet();

// Send to each admin
foreach (\$admins as \$admin) {
    \$messageId = \$messageModel->sendMessage(
        1,              // From system user
        \$admin->id,    // To admin
        \$subject,      // Contact subject
        \$message,      // Full message with sender info
        null,           // No show ID
        'system',       // System message type
        false,          // No reply needed
        null            // Not a reply
    );
}";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>🎨 Message Format</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Contact Message Template:</h3>";
    echo "<div style='background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 3px;'>";
    echo "<strong>Subject:</strong> Contact Form: [Original Subject]<br><br>";
    echo "<strong>Message Content:</strong><br>";
    echo "New contact form submission:<br><br>";
    echo "Name: [Sender Name]<br>";
    echo "Email: [Sender Email]<br>";
    echo "Subject: [Original Subject]<br><br>";
    echo "Message:<br>";
    echo "[Original Message]<br><br>";
    echo "---<br>";
    echo "This message was sent via the contact form on [Timestamp]<br>";
    echo "Reply directly to: [Sender Email]";
    echo "</div>";
    
    echo "<h3>🎯 Message Features:</h3>";
    echo "<ul>";
    echo "<li><strong>Clear identification:</strong> Subject prefixed with 'Contact Form:'</li>";
    echo "<li><strong>Sender information:</strong> Name and email clearly displayed</li>";
    echo "<li><strong>Original content:</strong> Complete original message preserved</li>";
    echo "<li><strong>Reply instructions:</strong> Admin can reply directly to sender's email</li>";
    echo "<li><strong>Timestamp:</strong> When the message was submitted</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔍 Security Features</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Security Measures:</h3>";
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Security Feature</th><th>Implementation</th><th>Protection Against</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>CAPTCHA</strong></td>";
    echo "<td>6-character alphanumeric verification</td>";
    echo "<td>Automated spam bots</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>CSRF Token</strong></td>";
    echo "<td>Hidden token validation</td>";
    echo "<td>Cross-site request forgery</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Input Sanitization</strong></td>";
    echo "<td>trim() and validation on all inputs</td>";
    echo "<td>Malicious input injection</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Email Validation</strong></td>";
    echo "<td>filter_var() with FILTER_VALIDATE_EMAIL</td>";
    echo "<td>Invalid email addresses</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Rate Limiting</strong></td>";
    echo "<td>CAPTCHA regeneration on failure</td>";
    echo "<td>Brute force attempts</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    if ($activeAdminCount > 0) {
        echo "<h2>✅ Contact Form Ready for Testing!</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<p><strong>Perfect!</strong> The contact form is ready with {$activeAdminCount} active admin(s) to receive messages.</p>";
        echo "<p><strong>What you should see:</strong></p>";
        echo "<ul>";
        echo "<li>📧 <strong>Enhanced contact form:</strong> /home/<USER>/li>";
        echo "<li>🔒 <strong>Security features:</strong> CAPTCHA, CSRF protection, input validation</li>";
        echo "<li>📨 <strong>Admin notifications:</strong> All active admins receive contact submissions</li>";
        echo "<li>🎯 <strong>Unified messaging:</strong> Messages appear in admin notification centers</li>";
        echo "<li>📱 <strong>Professional format:</strong> Clear message format with sender information</li>";
        echo "</ul>";
        echo "<p><strong>The contact form is now fully integrated with the unified messaging system!</strong></p>";
        echo "</div>";
    } else {
        echo "<h2>⚠️ Warning: No Active Admins</h2>";
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
        echo "<p><strong>Issue:</strong> No active admin users found to receive contact messages.</p>";
        echo "<p><strong>Action needed:</strong> Ensure at least one user has 'admin' role and 'active' status.</p>";
        echo "</div>";
    }
    
    echo "<h2>📋 Files Updated</h2>";
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Updated files:</strong></p>";
    echo "<ul>";
    echo "<li><code>views/home/<USER>/code> - Added CAPTCHA verification field</li>";
    echo "<li><code>controllers/HomeController.php</code> - Added form processing, CAPTCHA, and messaging</li>";
    echo "</ul>";
    echo "<p><strong>Features added:</strong></p>";
    echo "<ul>";
    echo "<li>✅ CAPTCHA generation and verification</li>";
    echo "<li>✅ Form validation and error handling</li>";
    echo "<li>✅ Unified messaging integration</li>";
    echo "<li>✅ Admin distribution system</li>";
    echo "<li>✅ Security features (CSRF, input sanitization)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Contact form messaging test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
