<?php
/**
 * Events and Shows Management System Installer
 * 
 * This script guides users through the installation process of the application.
 * It checks requirements, sets up the database, creates the admin account,
 * and configures application settings.
 */

// Define constants
define('INSTALLER_VERSION', '1.23.5');
define('MIN_PHP_VERSION', '7.4.0');
define('REQUIRED_EXTENSIONS', [
    'mysqli',
    'pdo',
    'pdo_mysql',
    'gd',
    'json',
    'session',
    'filter'
]);
define('REQUIRED_FUNCTIONS', [
    'password_hash',
    'password_verify',
    'random_bytes'
]);

// Start session
session_start();

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if already installed
if (file_exists('config/config.php') && !isset($_GET['force'])) {
    $configContent = file_get_contents('config/config.php');
    if (strpos($configContent, 'DB_HOST') !== false) {
        die('Application appears to be already installed. If you want to reinstall, add ?force to the URL.');
    }
}

// Function to check system requirements
function checkRequirements() {
    $requirements = [
        'php_version' => [
            'name' => 'PHP Version',
            'required' => '>= ' . MIN_PHP_VERSION,
            'current' => phpversion(),
            'status' => version_compare(phpversion(), MIN_PHP_VERSION, '>=')
        ],
        'extensions' => [],
        'functions' => [],
        'directories' => [
            [
                'name' => 'Config Directory',
                'path' => 'config',
                'writable' => is_writable('config') || (!file_exists('config') && is_writable('.'))
            ],
            [
                'name' => 'Uploads Directory',
                'path' => 'public/uploads',
                'writable' => is_writable('public/uploads') || (!file_exists('public/uploads') && is_writable('public'))
            ]
        ]
    ];
    
    // Check extensions
    foreach (REQUIRED_EXTENSIONS as $extension) {
        $requirements['extensions'][] = [
            'name' => $extension,
            'status' => extension_loaded($extension)
        ];
    }
    
    // Check functions
    foreach (REQUIRED_FUNCTIONS as $function) {
        $requirements['functions'][] = [
            'name' => $function,
            'status' => function_exists($function)
        ];
    }
    
    return $requirements;
}

// Function to create database tables
function createDatabaseTables($conn) {
    $tables = [
        // Users table
        "CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `password` varchar(255) NOT NULL,
            `role` enum('admin','coordinator','judge','user') NOT NULL DEFAULT 'user',
            `status` enum('active','inactive','pending') NOT NULL DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Password reset tokens table
        "CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `email` varchar(255) NOT NULL,
            `token` varchar(255) NOT NULL,
            `expires_at` timestamp NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Remember me tokens table
        "CREATE TABLE IF NOT EXISTS `remember_tokens` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `token` varchar(255) NOT NULL,
            `expires_at` timestamp NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            CONSTRAINT `remember_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Shows table
        "CREATE TABLE IF NOT EXISTS `shows` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text,
            `location` varchar(255) NOT NULL,
            `start_date` date NOT NULL,
            `end_date` date NOT NULL,
            `registration_start` date NOT NULL,
            `registration_end` date NOT NULL,
            `coordinator_id` int(11) NOT NULL,
            `status` enum('draft','published','completed','cancelled') NOT NULL DEFAULT 'draft',
            `fan_voting_enabled` tinyint(1) NOT NULL DEFAULT 1,
            `registration_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
            `listing_fee` decimal(10,2) NOT NULL DEFAULT '25.00',
            `listing_paid` tinyint(1) NOT NULL DEFAULT 0,
            `is_free` tinyint(1) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `coordinator_id` (`coordinator_id`),
            CONSTRAINT `shows_ibfk_1` FOREIGN KEY (`coordinator_id`) REFERENCES `users` (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Show categories table
        "CREATE TABLE IF NOT EXISTS `show_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `name` varchar(255) NOT NULL,
            `description` text,
            `registration_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
            `max_entries` int(11) NOT NULL DEFAULT '0',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `show_id` (`show_id`),
            CONSTRAINT `show_categories_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Judging metrics table
        "CREATE TABLE IF NOT EXISTS `judging_metrics` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `name` varchar(255) NOT NULL,
            `description` text,
            `max_score` int(11) NOT NULL DEFAULT '10',
            `weight` decimal(5,2) NOT NULL DEFAULT '1.00',
            `display_order` int(11) NOT NULL DEFAULT '0',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `show_id` (`show_id`),
            CONSTRAINT `judging_metrics_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Age weights table
        "CREATE TABLE IF NOT EXISTS `age_weights` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `min_age` int(11) NOT NULL,
            `max_age` int(11) NOT NULL,
            `multiplier` decimal(5,2) NOT NULL DEFAULT '1.00',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `show_id` (`show_id`),
            CONSTRAINT `age_weights_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Judge assignments table
        "CREATE TABLE IF NOT EXISTS `judge_assignments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `judge_id` int(11) NOT NULL,
            `category_id` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `show_id` (`show_id`),
            KEY `judge_id` (`judge_id`),
            KEY `category_id` (`category_id`),
            CONSTRAINT `judge_assignments_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
            CONSTRAINT `judge_assignments_ibfk_2` FOREIGN KEY (`judge_id`) REFERENCES `users` (`id`),
            CONSTRAINT `judge_assignments_ibfk_3` FOREIGN KEY (`category_id`) REFERENCES `show_categories` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Vehicles table
        "CREATE TABLE IF NOT EXISTS `vehicles` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `owner_id` int(11) NOT NULL,
            `make` varchar(255) NOT NULL,
            `model` varchar(255) NOT NULL,
            `year` int(11) NOT NULL,
            `color` varchar(255) DEFAULT NULL,
            `license_plate` varchar(50) DEFAULT NULL,
            `vin` varchar(50) DEFAULT NULL,
            `description` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `owner_id` (`owner_id`),
            CONSTRAINT `vehicles_ibfk_1` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Note: Vehicle images are now stored in the images table with entity_type = 'vehicle'
        
        // Registrations table
        "CREATE TABLE IF NOT EXISTS `registrations` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `vehicle_id` int(11) NOT NULL,
            `owner_id` int(11) NOT NULL,
            `category_id` int(11) NOT NULL,
            `status` enum('pending','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
            `payment_status` enum('pending','completed','rejected','refunded','free','waived') NOT NULL DEFAULT 'pending',
            `fee` decimal(10,2) NOT NULL DEFAULT '0.00',
            `payment_method_id` int(11) UNSIGNED DEFAULT NULL,
            `payment_reference` varchar(255) DEFAULT NULL,
            `registration_number` varchar(50) DEFAULT NULL COMMENT 'Unique registration number with RER- prefix',
            `display_number` varchar(50) DEFAULT NULL COMMENT 'Display number for the vehicle at the show',
            `qr_code` varchar(255) DEFAULT NULL COMMENT 'QR code filename for this registration',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `show_vehicle` (`show_id`,`vehicle_id`),
            KEY `vehicle_id` (`vehicle_id`),
            KEY `owner_id` (`owner_id`),
            KEY `category_id` (`category_id`),
            CONSTRAINT `registrations_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
            CONSTRAINT `registrations_ibfk_2` FOREIGN KEY (`vehicle_id`) REFERENCES `vehicles` (`id`) ON DELETE CASCADE,
            CONSTRAINT `registrations_ibfk_3` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
            CONSTRAINT `registrations_ibfk_4` FOREIGN KEY (`category_id`) REFERENCES `show_categories` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Judging scores table
        "CREATE TABLE IF NOT EXISTS `judging_scores` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `registration_id` int(11) NOT NULL,
            `judge_id` int(11) NOT NULL,
            `metric_id` int(11) NOT NULL,
            `score` decimal(5,2) NOT NULL DEFAULT '0.00',
            `comments` text,
            `is_draft` tinyint(1) NOT NULL DEFAULT '1',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `registration_judge_metric` (`registration_id`,`judge_id`,`metric_id`),
            KEY `judge_id` (`judge_id`),
            KEY `metric_id` (`metric_id`),
            CONSTRAINT `judging_scores_ibfk_1` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE CASCADE,
            CONSTRAINT `judging_scores_ibfk_2` FOREIGN KEY (`judge_id`) REFERENCES `users` (`id`),
            CONSTRAINT `judging_scores_ibfk_3` FOREIGN KEY (`metric_id`) REFERENCES `judging_metrics` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Results table
        "CREATE TABLE IF NOT EXISTS `results` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `category_id` int(11) NOT NULL,
            `registration_id` int(11) NOT NULL,
            `total_score` decimal(10,2) NOT NULL DEFAULT '0.00',
            `rank` int(11) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `show_category_registration` (`show_id`,`category_id`,`registration_id`),
            KEY `category_id` (`category_id`),
            KEY `registration_id` (`registration_id`),
            CONSTRAINT `results_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
            CONSTRAINT `results_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `show_categories` (`id`) ON DELETE CASCADE,
            CONSTRAINT `results_ibfk_3` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Fan votes table
        "CREATE TABLE IF NOT EXISTS `fan_votes` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `registration_id` int(11) NOT NULL,
            `voter_ip` varchar(45) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `show_ip` (`show_id`,`voter_ip`),
            KEY `registration_id` (`registration_id`),
            CONSTRAINT `fan_votes_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
            CONSTRAINT `fan_votes_ibfk_2` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // System settings table
        "CREATE TABLE IF NOT EXISTS `system_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(255) NOT NULL,
            `setting_value` text,
            `setting_group` varchar(50) NOT NULL DEFAULT 'general',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Printable templates table
        "CREATE TABLE IF NOT EXISTS `printable_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text,
            `template_data` longtext NOT NULL,
            `is_default` tinyint(1) NOT NULL DEFAULT '0',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Payment methods table
        "CREATE TABLE IF NOT EXISTS `payment_methods` (
            `id` int(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `name` varchar(50) NOT NULL,
            `description` text,
            `instructions` text,
            `is_active` tinyint(1) DEFAULT 1,
            `requires_approval` tinyint(1) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Payments table
        "CREATE TABLE IF NOT EXISTS `payments` (
            `id` int(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `user_id` int(11) UNSIGNED NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `payment_method_id` int(11) UNSIGNED NOT NULL,
            `payment_status` enum('pending','completed','rejected','refunded','free','waived') DEFAULT 'pending',
            `payment_reference` varchar(255),
            `payment_type` enum('registration','show_listing','other') DEFAULT 'registration',
            `related_id` int(11) UNSIGNED,
            `notes` text,
            `admin_notes` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            KEY `user_id` (`user_id`),
            KEY `payment_method_id` (`payment_method_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Vehicle total scores table
        "CREATE TABLE IF NOT EXISTS `vehicle_total_scores` (
            `id` int(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `show_id` int(11) UNSIGNED NOT NULL,
            `vehicle_id` int(11) UNSIGNED NOT NULL,
            `registration_id` int(11) UNSIGNED NOT NULL,
            `total_score` decimal(10,2) NOT NULL DEFAULT '0.00',
            `age_weight` decimal(5,2) DEFAULT NULL,
            `calculation_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY `unique_vehicle_show` (`show_id`,`vehicle_id`),
            KEY `idx_registration` (`registration_id`),
        )",
        
        // Category winners table
        "CREATE TABLE IF NOT EXISTS `category_winners` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `category_id` int(11) NOT NULL,
            `registration_id` int(11) NOT NULL,
            `vehicle_id` int(11) NOT NULL,
            `user_id` int(11) NOT NULL,
            `place` int(11) NOT NULL,
            `score` decimal(10,2) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_winner` (`show_id`,`category_id`,`place`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
            FOREIGN KEY (`show_id`) REFERENCES `shows`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`vehicle_id`) REFERENCES `vehicles`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`registration_id`) REFERENCES `registrations`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        // Payment settings table
        "CREATE TABLE IF NOT EXISTS `payment_settings` (
            `id` int(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `name` varchar(50) NOT NULL,
            `value` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"
    ];
    
    $success = true;
    
    foreach ($tables as $sql) {
        if (!$conn->query($sql)) {
            $success = false;
            echo "Error creating table: " . $conn->error . "<br>";
        }
    }
    
    // Insert default payment methods
    $defaultPaymentMethods = [
        "INSERT INTO `payment_methods` (`name`, `description`, `instructions`, `is_active`, `requires_approval`) VALUES
            ('PayPal', 'Pay securely with PayPal', 'You will be redirected to PayPal to complete your payment.', 1, 0),
            ('CashApp', 'Pay with CashApp', 'Send payment via CashApp and enter your confirmation code.', 1, 1),
            ('Venmo', 'Pay with Venmo', 'Send payment via Venmo and enter your transaction ID.', 1, 1),
            ('Free', 'No payment required', 'This option is for free events only.', 1, 0)"
    ];
    
    foreach ($defaultPaymentMethods as $sql) {
        $conn->query($sql);
    }
    
    // Insert default payment settings
    $defaultPaymentSettings = [
        "INSERT INTO `payment_settings` (`name`, `value`) VALUES
            ('paypal_client_id', ''),
            ('paypal_secret', ''),
            ('paypal_sandbox', 'true'),
            ('cashapp_id', ''),
            ('venmo_id', ''),
            ('default_show_listing_fee', '25.00')"
    ];
    
    foreach ($defaultPaymentSettings as $sql) {
        $conn->query($sql);
    }
    
    // Insert default system settings
    $defaultSystemSettings = [
        "INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_group`) VALUES
            ('dev_admin_bypass', '0', 'development')"
    ];
    
    foreach ($defaultSystemSettings as $sql) {
        $conn->query($sql);
    }
    
    return $success;
}

// Function to create admin user
function createAdminUser($conn, $name, $email, $password) {
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Check if user already exists
    $checkStmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    if (!$checkStmt) {
        error_log("Prepare failed: " . $conn->error);
        return false;
    }
    
    $checkStmt->bind_param("s", $email);
    $checkStmt->execute();
    $checkStmt->store_result();
    
    if ($checkStmt->num_rows > 0) {
        // User already exists, delete it first if we're forcing reinstall
        global $force;
        if ($force) {
            error_log("Force is enabled, deleting existing admin user");
            $deleteStmt = $conn->prepare("DELETE FROM users WHERE email = ?");
            $deleteStmt->bind_param("s", $email);
            $deleteStmt->execute();
            $deleteStmt->close();
        } else {
            error_log("Admin user already exists and force is not enabled");
            return false;
        }
    }
    $checkStmt->close();
    
    // Now create the admin user
    $stmt = $conn->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, 'admin')");
    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        return false;
    }
    
    $stmt->bind_param("sss", $name, $email, $hashedPassword);
    $result = $stmt->execute();
    
    if (!$result) {
        error_log("Execute failed: " . $stmt->error);
    }
    
    $stmt->close();
    return $result;
}

// Function to create config file
function createConfigFile($dbHost, $dbUser, $dbPass, $dbName, $appName, $baseUrl) {
    $configContent = <<<EOT
<?php
/**
 * Application Configuration
 */

// Database configuration
define('DB_HOST', '$dbHost');
define('DB_USER', '$dbUser');
define('DB_PASS', '$dbPass');
define('DB_NAME', '$dbName');

// URL and path configuration
define('BASE_URL', '$baseUrl');
define('URLROOT', '$baseUrl');

// Application settings
define('APP_NAME', '$appName');
define('APP_VERSION', '3.34.31');
define('INSTALL_DATE', gmdate('Y-m-d H:i:s'));

// Facebook OAuth settings
define('FB_APP_ID', '');
define('FB_APP_SECRET', '');
define('FB_REDIRECT_URI', '$baseUrl/auth/facebook-callback');

// File upload settings
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_LIFETIME', 86400); // 24 hours
define('REMEMBER_ME_LIFETIME', 2592000); // 30 days

// Payment settings
define('DEFAULT_CURRENCY', 'USD');
define('PAYMENT_ENABLED', true);

// Installation status
define('INSTALLED', true);

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Time zone
date_default_timezone_set('UTC');

// Load environment-specific configuration if exists
\$env_config = __DIR__ . '/env.config.php';
if (file_exists(\$env_config)) {
    require_once \$env_config;
}
EOT;
    
    // Create config directory if it doesn't exist
    if (!file_exists('config')) {
        mkdir('config', 0755, true);
    }
    
    return file_put_contents('config/config.php', $configContent);
}

// Function to create .htaccess file
function createHtaccessFile() {
    $htaccessContent = <<<EOT
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    
    # Redirect to public folder
    RewriteRule ^$ public/ [L]
    RewriteRule (.*) public/$1 [L]
</IfModule>
EOT;
    
    return file_put_contents('.htaccess', $htaccessContent);
}

// Function to create public .htaccess file
function createPublicHtaccessFile() {
    $htaccessContent = <<<EOT
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /public/
    
    # If the requested file exists, serve it
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    
    # Otherwise, route to index.php
    RewriteRule ^(.+)$ index.php?url=$1 [QSA,L]
</IfModule>
EOT;
    
    // Create public directory if it doesn't exist
    if (!file_exists('public')) {
        mkdir('public', 0755, true);
    }
    
    return file_put_contents('public/.htaccess', $htaccessContent);
}

// Function to create uploads directory
function createUploadsDirectory() {
    $directories = [
        'public/uploads',
        'public/uploads/vehicles',
        'public/uploads/shows'
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // Create index.html files to prevent directory listing
    foreach ($directories as $dir) {
        file_put_contents($dir . '/index.html', '<html><head><title>403 Forbidden</title></head><body><h1>Forbidden</h1><p>You don\'t have permission to access this resource.</p></body></html>');
    }
    
    return true;
}

// Process installation steps
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';
$force = isset($_GET['force']) ? true : false;

// Debug information
error_log("Current step: " . $step . ", Force: " . ($force ? 'true' : 'false'));

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug POST data
    error_log("Processing POST data for step: " . $step);
    
    switch ($step) {
        case 1:
            // Requirements check - just move to next step
            $step = 2;
            break;
            
        case 2:
            // Database configuration
            $dbHost = trim($_POST['db_host']);
            $dbUser = trim($_POST['db_user']);
            $dbPass = $_POST['db_pass']; // Don't trim password
            $dbName = trim($_POST['db_name']);
            
            // Validate inputs
            if (empty($dbHost) || empty($dbUser) || empty($dbName)) {
                $error = 'All database fields are required except password.';
            } else {
                // Test database connection
                $conn = new mysqli($dbHost, $dbUser, $dbPass);
                
                if ($conn->connect_error) {
                    $error = 'Database connection failed: ' . $conn->connect_error;
                } else {
                    // Check if database exists, create if not
                    if (!$conn->select_db($dbName)) {
                        if ($conn->query("CREATE DATABASE IF NOT EXISTS `$dbName`")) {
                            $conn->select_db($dbName);
                            $success = "Database '$dbName' created successfully.";
                        } else {
                            $error = "Failed to create database: " . $conn->error;
                        }
                    }
                    
                    if (empty($error)) {
                        // Store database config in session
                        $_SESSION['db_config'] = [
                            'host' => $dbHost,
                            'user' => $dbUser,
                            'pass' => $dbPass,
                            'name' => $dbName
                        ];
                        
                        // Create tables
                        if (createDatabaseTables($conn)) {
                            $success .= ' Database tables created successfully.';
                            $step = 3;
                        } else {
                            $error = 'Failed to create database tables.';
                        }
                    }
                    
                    $conn->close();
                }
            }
            break;
            
        case 3:
            // Admin account
            $adminName = trim($_POST['admin_name']);
            $adminEmail = trim($_POST['admin_email']);
            $adminPass = $_POST['admin_pass'];
            $adminPassConfirm = $_POST['admin_pass_confirm'];
            
            // Validate inputs
            if (empty($adminName) || empty($adminEmail) || empty($adminPass)) {
                $error = 'All fields are required.';
            } elseif (!filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
                $error = 'Please enter a valid email address.';
            } elseif ($adminPass !== $adminPassConfirm) {
                $error = 'Passwords do not match.';
            } elseif (strlen($adminPass) < 8) {
                $error = 'Password must be at least 8 characters long.';
            } else {
                // Connect to database
                $dbConfig = $_SESSION['db_config'];
                $conn = new mysqli($dbConfig['host'], $dbConfig['user'], $dbConfig['pass'], $dbConfig['name']);
                
                if ($conn->connect_error) {
                    $error = 'Database connection failed: ' . $conn->connect_error;
                } else {
                    // Create admin user
                    $adminCreated = createAdminUser($conn, $adminName, $adminEmail, $adminPass);
                    if ($adminCreated) {
                        // Store admin info in session
                        $_SESSION['admin_config'] = [
                            'name' => $adminName,
                            'email' => $adminEmail
                        ];
                        
                        $success = 'Admin account created successfully.';
                        $step = 4;
                        
                        // Debug info
                        error_log("Admin created successfully, moving to step 4");
                        
                        // Redirect to step 4 with force parameter if needed
                        $redirectUrl = "?step=4" . ($force ? "&force=1" : "");
                        header("Location: " . $redirectUrl);
                        exit;
                    } else {
                        $error = 'Failed to create admin account. ';
                        if ($conn->error) {
                            $error .= 'Database error: ' . $conn->error;
                        }
                        error_log("Admin creation failed: " . $error);
                    }
                    
                    $conn->close();
                }
            }
            break;
            
        case 4:
            // Application settings
            $appName = trim($_POST['app_name']);
            $baseUrl = trim($_POST['base_url']);
            
            // Validate inputs
            if (empty($appName) || empty($baseUrl)) {
                $error = 'All fields are required.';
            } else {
                // Store app settings in session
                $_SESSION['app_config'] = [
                    'name' => $appName,
                    'url' => $baseUrl
                ];
                
                $success = 'Application settings saved.';
                $step = 5;
            }
            break;
            
        case 5:
            // Finalize installation
            $dbConfig = $_SESSION['db_config'];
            $appConfig = $_SESSION['app_config'];
            
            // Create config file
            if (createConfigFile(
                $dbConfig['host'],
                $dbConfig['user'],
                $dbConfig['pass'],
                $dbConfig['name'],
                $appConfig['name'],
                $appConfig['url']
            )) {
                $success = 'Configuration file created successfully.';
                
                // Create .htaccess files
                createHtaccessFile();
                createPublicHtaccessFile();
                
                // Create uploads directory
                createUploadsDirectory();
                
                $step = 6;
            } else {
                $error = 'Failed to create configuration file.';
            }
            break;
    }
}

// HTML header
$pageTitle = 'Events and Shows Management System Installer';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
            background-color: #f8f9fa;
        }
        .installer-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            padding: 2rem;
        }
        .step-indicator {
            display: flex;
            margin-bottom: 2rem;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            border-bottom: 3px solid #dee2e6;
        }
        .step.active {
            border-bottom-color: #007bff;
            font-weight: bold;
        }
        .step.completed {
            border-bottom-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="installer-container">
            <h1 class="text-center mb-4"><?php echo $pageTitle; ?></h1>
            
            <div class="step-indicator">
                <div class="step <?php echo ($step >= 1) ? 'completed' : ''; ?> <?php echo ($step == 1) ? 'active' : ''; ?>">1. Requirements</div>
                <div class="step <?php echo ($step >= 2) ? 'completed' : ''; ?> <?php echo ($step == 2) ? 'active' : ''; ?>">2. Database</div>
                <div class="step <?php echo ($step >= 3) ? 'completed' : ''; ?> <?php echo ($step == 3) ? 'active' : ''; ?>">3. Admin Account</div>
                <div class="step <?php echo ($step >= 4) ? 'completed' : ''; ?> <?php echo ($step == 4) ? 'active' : ''; ?>">4. Settings</div>
                <div class="step <?php echo ($step >= 5) ? 'completed' : ''; ?> <?php echo ($step == 5) ? 'active' : ''; ?>">5. Finalize</div>
                <div class="step <?php echo ($step >= 6) ? 'completed' : ''; ?> <?php echo ($step == 6) ? 'active' : ''; ?>">6. Complete</div>
            </div>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <?php if ($step == 1): ?>
                <!-- Step 1: Requirements Check -->
                <h2>System Requirements</h2>
                <p>The installer will check if your server meets the requirements for running the Events and Shows Management System.</p>
                
                <?php $requirements = checkRequirements(); ?>
                
                <h3>PHP Version</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Requirement</th>
                            <th>Current</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?php echo $requirements['php_version']['required']; ?></td>
                            <td><?php echo $requirements['php_version']['current']; ?></td>
                            <td>
                                <?php if ($requirements['php_version']['status']): ?>
                                    <span class="badge bg-success">Pass</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Fail</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>PHP Extensions</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Extension</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($requirements['extensions'] as $extension): ?>
                            <tr>
                                <td><?php echo $extension['name']; ?></td>
                                <td>
                                    <?php if ($extension['status']): ?>
                                        <span class="badge bg-success">Installed</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Missing</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <h3>PHP Functions</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Function</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($requirements['functions'] as $function): ?>
                            <tr>
                                <td><?php echo $function['name']; ?></td>
                                <td>
                                    <?php if ($function['status']): ?>
                                        <span class="badge bg-success">Available</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Missing</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <h3>Directory Permissions</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Directory</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($requirements['directories'] as $directory): ?>
                            <tr>
                                <td><?php echo $directory['path']; ?></td>
                                <td>
                                    <?php if ($directory['writable']): ?>
                                        <span class="badge bg-success">Writable</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Not Writable</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <?php
                // Check if all requirements are met
                $phpVersionOk = $requirements['php_version']['status'];
                $extensionsOk = true;
                foreach ($requirements['extensions'] as $extension) {
                    if (!$extension['status']) {
                        $extensionsOk = false;
                        break;
                    }
                }
                
                $functionsOk = true;
                foreach ($requirements['functions'] as $function) {
                    if (!$function['status']) {
                        $functionsOk = false;
                        break;
                    }
                }
                
                $directoriesOk = true;
                foreach ($requirements['directories'] as $directory) {
                    if (!$directory['writable']) {
                        $directoriesOk = false;
                        break;
                    }
                }
                
                $allRequirementsMet = $phpVersionOk && $extensionsOk && $functionsOk && $directoriesOk;
                ?>
                
                <?php if (!$allRequirementsMet): ?>
                    <div class="alert alert-danger">
                        <h4>Requirements Not Met</h4>
                        <p>Your server does not meet all the requirements needed to install the application. Please fix the issues highlighted above and try again.</p>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="?step=1<?php echo $force ? '&force=1' : ''; ?>">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary" <?php echo !$allRequirementsMet ? 'disabled' : ''; ?>>Continue</button>
                    </div>
                </form>
                
            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Configuration -->
                <h2>Database Configuration</h2>
                <p>Please enter your database connection details below.</p>
                
                <form method="post" action="?step=2<?php echo $force ? '&force=1' : ''; ?>">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">Database Host</label>
                        <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                        <div class="form-text">Usually "localhost" or "127.0.0.1"</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_user" class="form-label">Database Username</label>
                        <input type="text" class="form-control" id="db_user" name="db_user" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_pass" class="form-label">Database Password</label>
                        <input type="password" class="form-control" id="db_pass" name="db_pass">
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_name" class="form-label">Database Name</label>
                        <input type="text" class="form-control" id="db_name" name="db_name" value="events_shows" required>
                        <div class="form-text">If the database doesn't exist, the installer will attempt to create it.</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="?step=1<?php echo $force ? '&force=1' : ''; ?>" class="btn btn-secondary me-md-2">Back</a>
                        <button type="submit" class="btn btn-primary">Continue</button>
                    </div>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- Step 3: Admin Account -->
                <h2>Admin Account</h2>
                <p>Create an administrator account for the application.</p>
                
                <form method="post" action="?step=3<?php echo $force ? '&force=1' : ''; ?>">
                    <div class="mb-3">
                        <label for="admin_name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="admin_name" name="admin_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_pass" class="form-label">Password</label>
                        <input type="password" class="form-control" id="admin_pass" name="admin_pass" required>
                        <div class="form-text">Password must be at least 8 characters long.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_pass_confirm" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="admin_pass_confirm" name="admin_pass_confirm" required>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="?step=2<?php echo $force ? '&force=1' : ''; ?>" class="btn btn-secondary me-md-2">Back</a>
                        <button type="submit" class="btn btn-primary">Continue</button>
                    </div>
                </form>
                
            <?php elseif ($step == 4): ?>
                <!-- Step 4: Application Settings -->
                <h2>Application Settings</h2>
                <p>Configure the basic settings for your application.</p>
                
                <form method="post" action="?step=4<?php echo $force ? '&force=1' : ''; ?>">
                    <div class="mb-3">
                        <label for="app_name" class="form-label">Application Name</label>
                        <input type="text" class="form-control" id="app_name" name="app_name" value="Events and Shows" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="base_url" class="form-label">Base URL</label>
                        <?php
                        // Try to detect base URL
                        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                        $host = $_SERVER['HTTP_HOST'];
                        $path = dirname($_SERVER['REQUEST_URI']);
                        $path = str_replace('\\', '/', $path);
                        $path = rtrim($path, '/');
                        $detectedUrl = $protocol . '://' . $host . $path;
                        ?>
                        <input type="text" class="form-control" id="base_url" name="base_url" value="<?php echo $detectedUrl; ?>" required>
                        <div class="form-text">The base URL of your application, without a trailing slash.</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="?step=3<?php echo $force ? '&force=1' : ''; ?>" class="btn btn-secondary me-md-2">Back</a>
                        <button type="submit" class="btn btn-primary">Continue</button>
                    </div>
                </form>
                
            <?php elseif ($step == 5): ?>
                <!-- Step 5: Finalize Installation -->
                <h2>Finalize Installation</h2>
                <p>Review your settings and complete the installation.</p>
                
                <h3>Database Settings</h3>
                <table class="table">
                    <tbody>
                        <tr>
                            <th>Host</th>
                            <td><?php echo $_SESSION['db_config']['host']; ?></td>
                        </tr>
                        <tr>
                            <th>Username</th>
                            <td><?php echo $_SESSION['db_config']['user']; ?></td>
                        </tr>
                        <tr>
                            <th>Database</th>
                            <td><?php echo $_SESSION['db_config']['name']; ?></td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>Admin Account</h3>
                <table class="table">
                    <tbody>
                        <tr>
                            <th>Name</th>
                            <td><?php echo $_SESSION['admin_config']['name']; ?></td>
                        </tr>
                        <tr>
                            <th>Email</th>
                            <td><?php echo $_SESSION['admin_config']['email']; ?></td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>Application Settings</h3>
                <table class="table">
                    <tbody>
                        <tr>
                            <th>Application Name</th>
                            <td><?php echo $_SESSION['app_config']['name']; ?></td>
                        </tr>
                        <tr>
                            <th>Base URL</th>
                            <td><?php echo $_SESSION['app_config']['url']; ?></td>
                        </tr>
                    </tbody>
                </table>
                
                <form method="post" action="?step=5<?php echo $force ? '&force=1' : ''; ?>">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="?step=4<?php echo $force ? '&force=1' : ''; ?>" class="btn btn-secondary me-md-2">Back</a>
                        <button type="submit" class="btn btn-primary">Complete Installation</button>
                    </div>
                </form>
                
            <?php elseif ($step == 6): ?>
                <!-- Step 6: Installation Complete -->
                <div class="text-center">
                    <h2>Installation Complete!</h2>
                    <p class="lead">The Events and Shows Management System has been successfully installed.</p>
                    
                    <div class="alert alert-warning">
                        <h4>Important!</h4>
                        <p>For security reasons, please delete the <code>install.php</code> file from your server.</p>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?php echo $_SESSION['app_config']['url']; ?>" class="btn btn-primary btn-lg">Go to Homepage</a>
                        <a href="<?php echo $_SESSION['app_config']['url']; ?>/auth/login" class="btn btn-outline-primary btn-lg ms-2">Login to Admin</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <p class="text-muted">Events and Shows Management System Installer v<?php echo INSTALLER_VERSION; ?></p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>