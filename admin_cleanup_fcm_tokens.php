<?php
/**
 * Admin FCM Token Cleanup Tool
 * 
 * Web-based tool for manually cleaning up FCM tokens
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'models/NotificationModel.php';

// Simple admin check (you might want to add proper authentication)
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

$notificationModel = new NotificationModel();
$db = new Database();

// Handle cleanup actions
$cleanupResults = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'cleanup_inactive':
            $days = (int)($_POST['days'] ?? 30);
            $cleaned = $notificationModel->cleanupInactiveTokens($days);
            $cleanupResults[] = "Cleaned {$cleaned} inactive tokens older than {$days} days";
            break;
            
        case 'cleanup_orphaned':
            $db->query("DELETE fcm_tokens FROM fcm_tokens 
                        LEFT JOIN users ON fcm_tokens.user_id = users.id 
                        WHERE users.id IS NULL");
            if ($db->execute()) {
                $cleaned = $db->rowCount();
                $cleanupResults[] = "Cleaned {$cleaned} orphaned tokens (users no longer exist)";
            }
            break;
            
        case 'cleanup_duplicates':
            $db->query("DELETE t1 FROM fcm_tokens t1
                        INNER JOIN fcm_tokens t2 
                        WHERE t1.id > t2.id 
                        AND t1.user_id = t2.user_id 
                        AND t1.token = t2.token");
            if ($db->execute()) {
                $cleaned = $db->rowCount();
                $cleanupResults[] = "Cleaned {$cleaned} duplicate tokens";
            }
            break;
    }
}

echo "<h1>🧹 FCM Token Cleanup Tool</h1>";

// Show cleanup results
if (!empty($cleanupResults)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>✅ Cleanup Results</h3>";
    foreach ($cleanupResults as $result) {
        echo "<p>• {$result}</p>";
    }
    echo "</div>";
}

// Get current statistics
$db->query("SELECT 
                COUNT(*) as total,
                SUM(active) as active,
                COUNT(*) - SUM(active) as inactive
            FROM fcm_tokens");
$stats = $db->single();

echo "<h2>📊 Current FCM Token Statistics</h2>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>Metric</th><th>Count</th></tr>";
echo "<tr><td>Total Tokens</td><td>{$stats->total}</td></tr>";
echo "<tr><td>Active Tokens</td><td style='color: green;'>{$stats->active}</td></tr>";
echo "<tr><td>Inactive Tokens</td><td style='color: red;'>{$stats->inactive}</td></tr>";
echo "</table>";

// Show users with multiple tokens
echo "<h2>👥 Users with Multiple Active Tokens</h2>";
$db->query("SELECT user_id, COUNT(*) as token_count, 
                   GROUP_CONCAT(SUBSTRING(token, 1, 20) SEPARATOR ', ') as token_previews
            FROM fcm_tokens 
            WHERE active = 1 
            GROUP BY user_id 
            HAVING COUNT(*) > 1 
            ORDER BY token_count DESC 
            LIMIT 10");
$multiTokenUsers = $db->resultSet();

if (!empty($multiTokenUsers)) {
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>User ID</th><th>Active Tokens</th><th>Token Previews</th></tr>";
    foreach ($multiTokenUsers as $user) {
        echo "<tr>";
        echo "<td>{$user->user_id}</td>";
        echo "<td>{$user->token_count}</td>";
        echo "<td>" . htmlspecialchars($user->token_previews) . "...</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: green;'>✅ No users have multiple active tokens</p>";
}

// Show old inactive tokens
echo "<h2>🗑️ Old Inactive Tokens</h2>";
$db->query("SELECT 
                COUNT(*) as count_7_days,
                (SELECT COUNT(*) FROM fcm_tokens WHERE active = 0 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)) as count_30_days,
                (SELECT COUNT(*) FROM fcm_tokens WHERE active = 0 AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)) as count_90_days
            FROM fcm_tokens 
            WHERE active = 0 AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)");
$oldTokens = $db->single();

echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>Age</th><th>Inactive Tokens</th></tr>";
echo "<tr><td>7+ days old</td><td>{$oldTokens->count_7_days}</td></tr>";
echo "<tr><td>30+ days old</td><td>{$oldTokens->count_30_days}</td></tr>";
echo "<tr><td>90+ days old</td><td>{$oldTokens->count_90_days}</td></tr>";
echo "</table>";

// Cleanup actions
echo "<h2>🛠️ Cleanup Actions</h2>";

echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;'>";

// Cleanup inactive tokens
echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px;'>";
echo "<h3>🗑️ Cleanup Inactive Tokens</h3>";
echo "<p>Remove inactive tokens older than specified days</p>";
echo "<form method='post'>";
echo "<input type='hidden' name='action' value='cleanup_inactive'>";
echo "<label>Days old: <input type='number' name='days' value='30' min='1' max='365' style='width: 60px;'></label><br><br>";
echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px; border: none; border-radius: 3px;'>Clean Inactive Tokens</button>";
echo "</form>";
echo "</div>";

// Cleanup orphaned tokens
echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px;'>";
echo "<h3>👻 Cleanup Orphaned Tokens</h3>";
echo "<p>Remove tokens for users that no longer exist</p>";
echo "<form method='post'>";
echo "<input type='hidden' name='action' value='cleanup_orphaned'>";
echo "<button type='submit' style='background: #fd7e14; color: white; padding: 10px; border: none; border-radius: 3px;'>Clean Orphaned Tokens</button>";
echo "</form>";
echo "</div>";

// Cleanup duplicates
echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 5px;'>";
echo "<h3>🔄 Cleanup Duplicates</h3>";
echo "<p>Remove duplicate tokens (same user + same token)</p>";
echo "<form method='post'>";
echo "<input type='hidden' name='action' value='cleanup_duplicates'>";
echo "<button type='submit' style='background: #6f42c1; color: white; padding: 10px; border: none; border-radius: 3px;'>Clean Duplicate Tokens</button>";
echo "</form>";
echo "</div>";

echo "</div>";

echo "<h2>⚙️ Automatic Cleanup</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>🤖 Built-in Cleanup</h3>";
echo "<p>The system automatically cleans up inactive tokens:</p>";
echo "<ul>";
echo "<li><strong>During token registration:</strong> 10% chance of cleanup on each new token registration</li>";
echo "<li><strong>Cron job:</strong> Daily cleanup via <code>cron/cleanup_fcm_tokens.php</code></li>";
echo "</ul>";

echo "<h3>📅 Recommended Cron Schedule</h3>";
echo "<code>0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_fcm_tokens.php</code>";
echo "<p><small>This runs daily at 2 AM</small></p>";
echo "</div>";

echo "<h2>📈 Cleanup Benefits</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<ul>";
echo "<li>🚀 <strong>Faster queries:</strong> Smaller table = faster FCM token lookups</li>";
echo "<li>💾 <strong>Less storage:</strong> Reduces database size and backup time</li>";
echo "<li>🔧 <strong>Easier debugging:</strong> Only see tokens that actually matter</li>";
echo "<li>🛡️ <strong>Security:</strong> Remove old tokens that might be compromised</li>";
echo "<li>📊 <strong>Accurate metrics:</strong> Better understanding of active users</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><em>Cleanup tool accessed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
