<?php
/**
 * Test FCM Validation Endpoint
 * 
 * Tests the new /api/pwa/fcm-validate-token endpoint
 */

require_once 'config/config.php';

echo "<h1>🔧 Test FCM Validation Endpoint</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Authentication Required</h3>";
    echo "<p>You need to be logged in to test the FCM validation endpoint.</p>";
    echo "<p><a href='/login'>Login here</a></p>";
    echo "</div>";
    exit;
}

echo "<h2>✅ Testing FCM Token Validation Endpoint</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>🎯 Endpoint Details:</h3>";
echo "<ul>";
echo "<li><strong>URL:</strong> /api/pwa/fcm-validate-token</li>";
echo "<li><strong>Method:</strong> POST</li>";
echo "<li><strong>Content-Type:</strong> application/json</li>";
echo "<li><strong>Body:</strong> {\"fcm_token\": \"test_token\"}</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🧪 Test Cases</h2>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>📱 Test 1: Invalid Token</h3>";
echo "<button onclick='testInvalidToken()' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Invalid Token</button>";
echo "<div id='invalid-token-result' style='margin-top: 10px;'></div>";

echo "<h3>📱 Test 2: Missing Token</h3>";
echo "<button onclick='testMissingToken()' style='background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Missing Token</button>";
echo "<div id='missing-token-result' style='margin-top: 10px;'></div>";

echo "<h3>📱 Test 3: Valid Token (if any exist)</h3>";
echo "<button onclick='testValidToken()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Valid Token</button>";
echo "<div id='valid-token-result' style='margin-top: 10px;'></div>";
echo "</div>";

// Get a real token from database for testing
require_once 'core/Database.php';
$db = new Database();
$db->query("SELECT fcm_token FROM fcm_tokens WHERE user_id = ? AND active = 1 LIMIT 1");
$db->bind(1, $_SESSION['user_id']);
$db->execute();
$userToken = $db->single();
$realToken = $userToken ? $userToken->fcm_token : null;

echo "<script>
function testInvalidToken() {
    const resultDiv = document.getElementById('invalid-token-result');
    resultDiv.innerHTML = '<p>Testing invalid token...</p>';
    
    fetch('/api/pwa/fcm-validate-token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            fcm_token: 'invalid_test_token_12345'
        })
    })
    .then(response => response.json())
    .then(data => {
        resultDiv.innerHTML = `
            <div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>
                <strong>Response:</strong><br>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
        `;
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    });
}

function testMissingToken() {
    const resultDiv = document.getElementById('missing-token-result');
    resultDiv.innerHTML = '<p>Testing missing token...</p>';
    
    fetch('/api/pwa/fcm-validate-token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        resultDiv.innerHTML = `
            <div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>
                <strong>Response:</strong><br>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            </div>
        `;
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    });
}

function testValidToken() {
    const resultDiv = document.getElementById('valid-token-result');
    const realToken = " . json_encode($realToken) . ";
    
    if (!realToken) {
        resultDiv.innerHTML = `
            <div style='background: #fff3cd; padding: 10px; border-radius: 3px;'>
                <strong>No valid tokens found for your user.</strong><br>
                Generate a token first by enabling push notifications.
            </div>
        `;
        return;
    }
    
    resultDiv.innerHTML = '<p>Testing valid token...</p>';
    
    fetch('/api/pwa/fcm-validate-token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            fcm_token: realToken
        })
    })
    .then(response => response.json())
    .then(data => {
        resultDiv.innerHTML = `
            <div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>
                <strong>Response:</strong><br>
                <pre>${JSON.stringify(data, null, 2)}</pre>
                <br><strong>Token tested:</strong> ${realToken.substring(0, 20)}...
            </div>
        `;
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>
                <strong>Error:</strong> ${error.message}
            </div>
        `;
    });
}
</script>";

echo "<h2>🎯 Expected Results</h2>";

echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
echo "<h3>✅ What Each Test Should Return:</h3>";

echo "<h4>Test 1: Invalid Token</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo "{\n";
echo "  \"success\": true,\n";
echo "  \"valid\": false,\n";
echo "  \"message\": \"Token not found in database\"\n";
echo "}";
echo "</pre>";

echo "<h4>Test 2: Missing Token</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"error\": \"FCM token is required\"\n";
echo "}";
echo "</pre>";

echo "<h4>Test 3: Valid Token (if exists)</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo "{\n";
echo "  \"success\": true,\n";
echo "  \"valid\": true,\n";
echo "  \"message\": \"Token is valid\"\n";
echo "}";
echo "</pre>";
echo "</div>";

echo "<h2>📊 Current FCM Tokens</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h3>🔍 Your FCM Tokens:</h3>";

$db->query("SELECT id, fcm_token, created_at, last_used FROM fcm_tokens WHERE user_id = ? AND active = 1 ORDER BY created_at DESC");
$db->bind(1, $_SESSION['user_id']);
$db->execute();
$userTokens = $db->resultSet();

if (!empty($userTokens)) {
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Token (First 20 chars)</th><th>Created</th><th>Last Used</th></tr>";
    
    foreach ($userTokens as $token) {
        echo "<tr>";
        echo "<td>{$token->id}</td>";
        echo "<td>" . substr($token->fcm_token, 0, 20) . "...</td>";
        echo "<td>{$token->created_at}</td>";
        echo "<td>" . ($token->last_used ?? 'Never') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ No active FCM tokens found for your user.</p>";
    echo "<p>To generate a token:</p>";
    echo "<ol>";
    echo "<li>Go to your main site page</li>";
    echo "<li>Allow push notifications when prompted</li>";
    echo "<li>Return here to test validation</li>";
    echo "</ol>";
}
echo "</div>";

echo "<h2>🔧 Troubleshooting</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<h3>🎯 If Tests Fail:</h3>";

echo "<h4>500 Internal Server Error:</h4>";
echo "<ul>";
echo "<li>Check that Database class is included in PwaController</li>";
echo "<li>Verify fcm_tokens table exists</li>";
echo "<li>Check PHP error logs</li>";
echo "</ul>";

echo "<h4>404 Not Found:</h4>";
echo "<ul>";
echo "<li>Verify route is added to App.php</li>";
echo "<li>Check that fcmValidateToken method exists in PwaController</li>";
echo "</ul>";

echo "<h4>Authentication Errors:</h4>";
echo "<ul>";
echo "<li>Make sure you're logged in</li>";
echo "<li>Check session is active</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><em>FCM validation endpoint test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
