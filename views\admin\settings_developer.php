<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Developer Tools</h1>
            <p class="text-muted">Advanced tools for system administration</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <!-- Development Settings Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary bg-opacity-10 py-3">
                    <h5 class="card-title mb-0 text-primary">
                        <i class="fas fa-cogs me-2"></i>Development Settings
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="<?php echo BASE_URL; ?>/admin/saveDevSettings" method="POST" class="needs-validation" novalidate>
                        <?php echo csrfTokenField(); ?>
                        
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="dev_admin_bypass" name="dev_admin_bypass" value="1" 
                                    <?php echo (isset($data['settings']['dev_admin_bypass']) && $data['settings']['dev_admin_bypass'] == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="dev_admin_bypass">
                                    <strong>Enable Admin Authentication Bypass</strong>
                                </label>
                            </div>
                            <div class="alert alert-danger mt-2">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>SECURITY WARNING:</strong> This setting bypasses all authentication checks and grants admin access to anyone visiting the site. Only enable in secure development environments and disable immediately after testing.
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h5 class="mb-3"><i class="fas fa-key me-2"></i>Session Security Settings</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="session_lifetime" class="form-label">Session Lifetime (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="session_lifetime" name="session_lifetime" 
                                            value="<?php echo isset($data['settings']['session_lifetime']) ? $data['settings']['session_lifetime'] : '86400'; ?>" 
                                            min="300" max="2592000">
                                        <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                    </div>
                                    <div class="form-text">Default: 86400 (24 hours). Min: 300 (5 minutes). Max: 2592000 (30 days).</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="facebook_session_lifetime" class="form-label">Facebook Session Lifetime (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="facebook_session_lifetime" name="facebook_session_lifetime" 
                                            value="<?php echo isset($data['settings']['facebook_session_lifetime']) ? $data['settings']['facebook_session_lifetime'] : '86400'; ?>" 
                                            min="300" max="2592000">
                                        <span class="input-group-text"><i class="fab fa-facebook"></i></span>
                                    </div>
                                    <div class="form-text">Default: 86400 (24 hours). Min: 300 (5 minutes). Max: 2592000 (30 days).</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="remember_me_lifetime" class="form-label">Remember Me Lifetime (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="remember_me_lifetime" name="remember_me_lifetime" 
                                            value="<?php echo isset($data['settings']['remember_me_lifetime']) ? $data['settings']['remember_me_lifetime'] : '2592000'; ?>" 
                                            min="3600" max="31536000">
                                        <span class="input-group-text"><i class="fas fa-user-check"></i></span>
                                    </div>
                                    <div class="form-text">Default: 2592000 (30 days). Min: 3600 (1 hour). Max: 31536000 (1 year).</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Developer Tools Dashboard -->
    <div class="row g-4 mb-5">
        <!-- Impersonate User Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-user-secret text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Impersonate User</h4>
                    </div>
                    <p class="card-text text-muted">Log in as another user to troubleshoot issues or provide support.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/impersonateUser" class="stretched-link text-decoration-none">
                        <span class="d-none">View Impersonate User</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Scheduled Tasks Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calendar-alt text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Scheduled Tasks</h4>
                    </div>
                    <p class="card-text text-muted">Manage automated tasks and event-triggered operations.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/scheduled_tasks" class="stretched-link text-decoration-none">
                        <span class="d-none">View Scheduled Tasks</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Cache Management Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-danger bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-trash-alt text-danger fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Cache Management</h4>
                    </div>
                    <p class="card-text text-muted">Clear browser cache, cookies, and storage for this site only. Useful for development troubleshooting.</p>
                    <div class="mt-3">
                        <button type="button" class="btn btn-danger btn-sm" onclick="clearSiteCache()">
                            <i class="fas fa-broom me-2"></i>Clear Site Cache
                        </button>
                    </div>
                    <div class="alert alert-warning mt-3 mb-0 small">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        This will clear all cached data for events.rowaneliterides.com only, preserving other sites' data.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cache Management Modal -->
<div class="modal fade" id="cacheClearModal" tabindex="-1" aria-labelledby="cacheClearModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="cacheClearModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Clear Site Cache
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-info-circle me-2"></i>What will be cleared:</h6>
                    <ul class="mb-0">
                        <li><strong>All Cookies</strong> - Including session cookies (PHPSESSID), authentication tokens, remember me tokens</li>
                        <li><strong>Local Storage</strong> - All locally stored application data</li>
                        <li><strong>Session Storage</strong> - All session-specific data</li>
                        <li><strong>IndexedDB</strong> - PWA databases, FCM token storage, notification data</li>
                        <li><strong>Service Worker Cache</strong> - All cached resources and background sync data</li>
                        <li><strong>Browser Cache</strong> - All cached files and resources for this domain</li>
                        <li><strong>Push Subscriptions</strong> - All push notification subscriptions</li>
                        <li><strong>PWA Cache</strong> - Forces PWA update check on next visit</li>
                    </ul>
                </div>
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important:</h6>
                    <ul class="mb-0">
                        <li><strong>You will be logged out</strong> and need to log in again</li>
                        <li><strong>All "Remember Me" settings will be lost</strong></li>
                        <li><strong>Push notification permissions will be reset</strong></li>
                        <li><strong>PWA will show update prompt on next visit</strong></li>
                        <li><strong>This action cannot be undone</strong></li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmClearCache()">
                    <i class="fas fa-trash-alt me-2"></i>Clear Cache & Reload
                </button>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Cache Management Functions
 * Clears site-specific browser data for development troubleshooting
 */

function clearSiteCache() {
    // Show confirmation modal
    const modal = new bootstrap.Modal(document.getElementById('cacheClearModal'));
    modal.show();
}

async function confirmClearCache() {
    try {
        // Hide the modal first
        const modal = bootstrap.Modal.getInstance(document.getElementById('cacheClearModal'));
        modal.hide();
        
        // Show loading indicator
        showCacheLoadingIndicator();
        
        // Clear different types of storage
        await clearAllSiteData();
        
        // Show success message briefly before reload
        showCacheSuccessMessage();
        
        // Set a simple sessionStorage flag that will survive the redirect
        sessionStorage.setItem('show_pwa_update_banner', 'true');
        console.log('[Cache Clear] PWA update banner flag set:', sessionStorage.getItem('show_pwa_update_banner'));
        
        // Redirect to login page after a short delay since user is now logged out
        setTimeout(() => {
            console.log('[Cache Clear] Redirecting to login page after logout');
            window.location.href = '/auth/login';
        }, 2000);
        
    } catch (error) {
        console.error('Error clearing cache:', error);
        showCacheErrorMessage(error.message);
    }
}

async function clearAllSiteData() {
    const domain = window.location.hostname;
    const protocol = window.location.protocol;
    const origin = `${protocol}//${domain}`;
    
    console.log(`[Cache Clear] Clearing data for domain: ${domain}`);
    
    // 1. Clear Local Storage (but preserve PWA update flags temporarily)
    try {
        // Save PWA update flags before clearing
        const forceUpdate = localStorage.getItem('force_pwa_update_check');
        const cacheCleared = localStorage.getItem('pwa_cache_cleared');
        
        localStorage.clear();
        console.log('[Cache Clear] Local Storage cleared');
        
        // Restore PWA update flags after clearing
        if (forceUpdate) localStorage.setItem('force_pwa_update_check', forceUpdate);
        if (cacheCleared) localStorage.setItem('pwa_cache_cleared', cacheCleared);
        
        // Also set additional flags for PWA update
        localStorage.setItem('pwa_force_update_after_cache_clear', 'true');
        localStorage.setItem('pwa_cache_clear_timestamp', Date.now().toString());
        
        // Set cookie as backup (won't be cleared by our cookie clearing since it's set after)
        document.cookie = 'pwa_force_update=true; path=/; max-age=300'; // 5 minutes
        
    } catch (e) {
        console.warn('[Cache Clear] Could not clear Local Storage:', e);
    }
    
    // 2. Clear Session Storage
    try {
        sessionStorage.clear();
        console.log('[Cache Clear] Session Storage cleared');
    } catch (e) {
        console.warn('[Cache Clear] Could not clear Session Storage:', e);
    }
    
    // 3. Clear IndexedDB (including FCM and PWA databases)
    try {
        await clearIndexedDB();
        console.log('[Cache Clear] IndexedDB cleared');
    } catch (e) {
        console.warn('[Cache Clear] Could not clear IndexedDB:', e);
    }
    
    // 4. Clear Service Worker Cache and unregister workers
    try {
        await clearServiceWorkerCache();
        console.log('[Cache Clear] Service Worker cache cleared');
    } catch (e) {
        console.warn('[Cache Clear] Could not clear Service Worker cache:', e);
    }
    
    // 5. Clear ALL cookies for this domain (comprehensive approach)
    try {
        await clearAllDomainCookies(domain);
        console.log('[Cache Clear] All cookies cleared');
    } catch (e) {
        console.warn('[Cache Clear] Could not clear cookies:', e);
    }
    
    // 6. Clear browser cache (Cache API)
    try {
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            await Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
            console.log('[Cache Clear] Browser caches cleared');
        }
    } catch (e) {
        console.warn('[Cache Clear] Could not clear browser caches:', e);
    }
    
    // 7. Clear Web SQL (if supported - legacy)
    try {
        if (window.openDatabase) {
            // Clear Web SQL databases (legacy support)
            console.log('[Cache Clear] Web SQL clearing attempted');
        }
    } catch (e) {
        console.warn('[Cache Clear] Could not clear Web SQL:', e);
    }
    
    // 8. Clear push notification subscriptions
    try {
        await clearPushSubscriptions();
        console.log('[Cache Clear] Push subscriptions cleared');
    } catch (e) {
        console.warn('[Cache Clear] Could not clear push subscriptions:', e);
    }
    
    // 9. Force PWA update on next visit
    try {
        await forcePWAUpdate();
        console.log('[Cache Clear] PWA update forced');
    } catch (e) {
        console.warn('[Cache Clear] Could not force PWA update:', e);
    }
    
    // 10. Force server-side logout
    try {
        await forceServerLogout();
        console.log('[Cache Clear] Server-side logout completed');
    } catch (e) {
        console.warn('[Cache Clear] Could not complete server-side logout:', e);
    }
    
    // 11. Clear any remaining storage
    try {
        // Clear any remaining storage quotas
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            console.log('[Cache Clear] Storage estimate before clear:', estimate);
        }
    } catch (e) {
        console.warn('[Cache Clear] Could not get storage estimate:', e);
    }
}

async function clearIndexedDB() {
    return new Promise((resolve, reject) => {
        // Comprehensive list of potential IndexedDB databases
        const commonDBNames = [
            'REREventsDB', // Our PWA database
            'keyval-store',
            'localforage',
            'firebase-messaging-database',
            'fcm_token_details_db',
            'fcm_vapid_details_db',
            'firebase-installations-database',
            'firebase-heartbeat-database',
            'workbox-background-sync',
            'workbox-expiration',
            'workbox-precache',
            'workbox-runtime',
            'workbox-strategies',
            'push-notifications-db',
            'notification-store',
            'pwa-cache-db',
            'events-cache-db'
        ];
        
        let clearedCount = 0;
        let totalDBs = commonDBNames.length;
        
        // Try to get all databases if supported
        if ('databases' in indexedDB) {
            indexedDB.databases().then(databases => {
                databases.forEach(db => {
                    if (!commonDBNames.includes(db.name)) {
                        commonDBNames.push(db.name);
                        totalDBs++;
                    }
                });
                deleteAllDatabases();
            }).catch(() => {
                deleteAllDatabases();
            });
        } else {
            deleteAllDatabases();
        }
        
        function deleteAllDatabases() {
            if (commonDBNames.length === 0) {
                resolve();
                return;
            }
            
            commonDBNames.forEach(dbName => {
                const deleteReq = indexedDB.deleteDatabase(dbName);
                
                deleteReq.onsuccess = () => {
                    console.log(`[Cache Clear] IndexedDB '${dbName}' deleted`);
                    clearedCount++;
                    if (clearedCount === totalDBs) resolve();
                };
                
                deleteReq.onerror = () => {
                    console.log(`[Cache Clear] IndexedDB '${dbName}' not found or could not be deleted`);
                    clearedCount++;
                    if (clearedCount === totalDBs) resolve();
                };
                
                deleteReq.onblocked = () => {
                    console.warn(`[Cache Clear] IndexedDB '${dbName}' deletion blocked`);
                    clearedCount++;
                    if (clearedCount === totalDBs) resolve();
                };
            });
        }
        
        // Fallback timeout
        setTimeout(() => {
            if (clearedCount < totalDBs) {
                console.log('[Cache Clear] IndexedDB clearing timed out, continuing...');
                resolve();
            }
        }, 5000);
    });
}

async function clearServiceWorkerCache() {
    if ('serviceWorker' in navigator) {
        try {
            // Get all service worker registrations
            const registrations = await navigator.serviceWorker.getRegistrations();
            console.log(`[Cache Clear] Found ${registrations.length} service worker registrations`);
            
            // Unregister all service workers
            await Promise.all(
                registrations.map(async (registration) => {
                    console.log(`[Cache Clear] Unregistering service worker: ${registration.scope}`);
                    return registration.unregister();
                })
            );
            
            // Clear all cache storage
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                console.log(`[Cache Clear] Found ${cacheNames.length} cache storages:`, cacheNames);
                
                await Promise.all(
                    cacheNames.map(async (cacheName) => {
                        console.log(`[Cache Clear] Deleting cache: ${cacheName}`);
                        return caches.delete(cacheName);
                    })
                );
            }
            
            // Clear any background sync registrations
            if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
                try {
                    // Send message to service worker to clear background sync
                    navigator.serviceWorker.controller.postMessage({
                        type: 'CLEAR_ALL_DATA'
                    });
                } catch (e) {
                    console.warn('[Cache Clear] Could not send clear message to service worker:', e);
                }
            }
            
        } catch (error) {
            console.warn('[Cache Clear] Error clearing service worker cache:', error);
        }
    }
}

async function clearAllDomainCookies(domain) {
    // Get all cookies for this domain
    const cookies = document.cookie.split(';');
    
    // Common paths to clear cookies from
    const paths = ['/', '/admin', '/user', '/auth', '/api', '/calendar', '/show', '/registration'];
    
    // Common cookie names used by the application
    const knownCookies = [
        'PHPSESSID',
        'remember_token',
        'user_session',
        'admin_session',
        'csrf_token',
        'facebook_token',
        'fb_access_token',
        'auth_token',
        'login_token',
        'session_id',
        'user_id',
        'admin_id',
        'coordinator_id',
        'staff_id',
        'judge_id',
        'fcm_token',
        'push_subscription',
        'notification_permission',
        'pwa_installed',
        'cache_version'
    ];
    
    // Clear all existing cookies (except PWA update flags)
    cookies.forEach(cookie => {
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
        
        // Skip PWA update cookies
        if (name && name !== 'pwa_force_update' && name !== 'pwa_update_flag') {
            // Clear for all paths and domain variations
            paths.forEach(path => {
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}`;
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=.${domain}`;
                
                // Also try with secure flag
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; secure`;
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}; secure`;
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=.${domain}; secure`;
            });
        }
    });
    
    // Also clear known application cookies that might not be visible
    knownCookies.forEach(cookieName => {
        paths.forEach(path => {
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`;
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}`;
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=.${domain}`;
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; secure`;
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=${domain}; secure`;
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}; domain=.${domain}; secure`;
        });
    });
    
    console.log(`[Cache Clear] Attempted to clear ${cookies.length} existing cookies and ${knownCookies.length} known cookies`);
    
    // Wait a moment for cookie clearing to process
    await new Promise(resolve => setTimeout(resolve, 100));
}

async function clearPushSubscriptions() {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
        try {
            // Get all service worker registrations
            const registrations = await navigator.serviceWorker.getRegistrations();
            
            for (const registration of registrations) {
                try {
                    // Get push subscription for this registration
                    const subscription = await registration.pushManager.getSubscription();
                    
                    if (subscription) {
                        console.log('[Cache Clear] Unsubscribing from push notifications');
                        await subscription.unsubscribe();
                        console.log('[Cache Clear] Push subscription unsubscribed');
                    }
                } catch (e) {
                    console.warn('[Cache Clear] Could not unsubscribe from push for registration:', e);
                }
            }
            
            // Also try to clear FCM token from localStorage/indexedDB
            try {
                localStorage.removeItem('fcm_token');
                localStorage.removeItem('fcm_registration_token');
                localStorage.removeItem('firebase_token');
                localStorage.removeItem('push_subscription');
                localStorage.removeItem('notification_permission');
                console.log('[Cache Clear] FCM tokens cleared from localStorage');
            } catch (e) {
                console.warn('[Cache Clear] Could not clear FCM tokens from localStorage:', e);
            }
            
        } catch (error) {
            console.warn('[Cache Clear] Error clearing push subscriptions:', error);
        }
    }
}

async function forcePWAUpdate() {
    if ('serviceWorker' in navigator) {
        try {
            // 1. Clear PWA version tracking in localStorage
            localStorage.removeItem('pwa_version');
            localStorage.removeItem('app_version');
            localStorage.removeItem('cache_version');
            localStorage.removeItem('sw_version');
            localStorage.removeItem('pwa_last_update');
            localStorage.removeItem('pwa_update_available');
            localStorage.removeItem('pwa_update_dismissed');
            
            // 2. Set flag to force update check on next visit
            localStorage.setItem('force_pwa_update_check', 'true');
            localStorage.setItem('pwa_cache_cleared', new Date().toISOString());
            
            // 3. Get all service worker registrations and force update
            const registrations = await navigator.serviceWorker.getRegistrations();
            
            for (const registration of registrations) {
                try {
                    // Force service worker to check for updates
                    console.log('[PWA Update] Forcing update check for service worker:', registration.scope);
                    await registration.update();
                    
                    // If there's a waiting service worker, skip waiting
                    if (registration.waiting) {
                        console.log('[PWA Update] Activating waiting service worker');
                        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                    }
                    
                    // Send message to active service worker to prepare for update
                    if (registration.active) {
                        registration.active.postMessage({ 
                            type: 'PREPARE_UPDATE',
                            timestamp: Date.now()
                        });
                    }
                    
                } catch (e) {
                    console.warn('[PWA Update] Could not update service worker registration:', e);
                }
            }
            
            // 4. Clear PWA-related caches specifically
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                const pwaCaches = cacheNames.filter(name => 
                    name.includes('pwa') || 
                    name.includes('workbox') || 
                    name.includes('precache') || 
                    name.includes('runtime') ||
                    name.includes('app-shell') ||
                    name.includes('static-resources')
                );
                
                for (const cacheName of pwaCaches) {
                    console.log('[PWA Update] Clearing PWA cache:', cacheName);
                    await caches.delete(cacheName);
                }
            }
            
            // 5. Clear manifest cache and force re-fetch
            if ('caches' in window) {
                try {
                    const manifestCache = await caches.open('manifest-cache');
                    await manifestCache.delete('/manifest.json');
                    console.log('[PWA Update] Manifest cache cleared');
                } catch (e) {
                    console.warn('[PWA Update] Could not clear manifest cache:', e);
                }
            }
            
            // 6. Set update notification flag for next visit
            sessionStorage.setItem('show_pwa_update_notification', 'true');
            
            console.log('[PWA Update] PWA update preparation complete');
            
        } catch (error) {
            console.warn('[PWA Update] Error forcing PWA update:', error);
        }
    }
}

async function forceServerLogout() {
    try {
        // Call the new API logout endpoint
        const response = await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('[Cache Clear] Server-side logout successful:', data);
        } else {
            console.warn('[Cache Clear] Server-side logout failed with status:', response.status);
            
            // Try fallback logout methods
            await tryFallbackLogout();
        }
        
    } catch (error) {
        console.warn('[Cache Clear] Error during server-side logout:', error);
        
        // Try fallback logout methods
        await tryFallbackLogout();
    }
}

async function tryFallbackLogout() {
    // Try traditional logout endpoint
    try {
        const response = await fetch('/auth/logout', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });
        console.log('[Cache Clear] Fallback logout attempted');
    } catch (e) {
        console.warn('[Cache Clear] Fallback logout failed:', e);
    }
    
    // Try to destroy session by calling a logout URL
    try {
        const img = new Image();
        img.src = '/auth/logout?cache_clear=1&t=' + Date.now();
        console.log('[Cache Clear] Image-based logout attempted');
    } catch (e) {
        console.warn('[Cache Clear] Image-based logout failed:', e);
    }
}

function showCacheLoadingIndicator() {
    showToast('Clearing site cache and data...', 'info', 'cacheLoadingToast', 0);
}

function showCacheSuccessMessage() {
    // Hide loading toast
    const loadingToast = document.getElementById('cacheLoadingToast');
    if (loadingToast) {
        const toast = bootstrap.Toast.getInstance(loadingToast);
        if (toast) toast.hide();
    }
    
    showToast('Cache cleared successfully! Reloading page...', 'success', 'cacheSuccessToast');
}

function showCacheErrorMessage(error) {
    // Hide loading toast
    const loadingToast = document.getElementById('cacheLoadingToast');
    if (loadingToast) {
        const toast = bootstrap.Toast.getInstance(loadingToast);
        if (toast) toast.hide();
    }
    
    showToast('Error clearing cache: ' + error, 'danger', 'cacheErrorToast', 5000);
}

function showToast(message, type, toastId, delay = 3000) {
    // Remove existing toast if present
    const existingToast = document.getElementById(toastId);
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Get appropriate icon
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    else if (type === 'danger') icon = 'exclamation-triangle';
    else if (type === 'info') icon = 'spinner fa-spin';
    
    // Create toast HTML
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${icon} me-2"></i>
                    ${message}
                </div>
                ${type === 'danger' ? '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>' : ''}
            </div>
        </div>
    `;
    
    // Add toast to container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toastOptions = delay === 0 ? { autohide: false } : { delay: delay };
    const toast = new bootstrap.Toast(toastElement, toastOptions);
    toast.show();
    
    // Auto-remove after hiding
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>