<?php
/**
 * Notification Model
 * 
 * This model handles all database operations related to notifications,
 * including user preferences, subscriptions, and notification queue management.
 */
class NotificationModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get user notification preferences
     * 
     * @param int $userId User ID
     * @return object|bool User preferences or false if not found
     */
    public function getUserPreferences($userId) {
        $this->db->query('SELECT * FROM user_notification_preferences WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        
        $preferences = $this->db->single();
        
        // If no preferences exist, create default ones
        if (!$preferences) {
            $this->createDefaultPreferences($userId);
            return $this->getUserPreferences($userId);
        }
        
        return $preferences;
    }
    
    /**
     * Update user notification preferences
     * 
     * @param int $userId User ID
     * @param array $preferences Preference data
     * @return bool Success status
     */
    public function updateUserPreferences($userId, $preferences) {
        // First ensure the user has a preferences record
        $this->ensureUserPreferencesExist($userId);
        
        $this->db->query('UPDATE user_notification_preferences SET 
                          email_notifications = :email_notifications,
                          sms_notifications = :sms_notifications,
                          push_notifications = :push_notifications,
                          toast_notifications = :toast_notifications,
                          event_reminders = :event_reminders,
                          registration_updates = :registration_updates,
                          judging_updates = :judging_updates,
                          award_notifications = :award_notifications,
                          system_announcements = :system_announcements,
                          reminder_times = :reminder_times,
                          updated_at = NOW()
                          WHERE user_id = :user_id');
        
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':email_notifications', isset($preferences['email_notifications']) ? ($preferences['email_notifications'] ? 1 : 0) : 1);
        $this->db->bind(':sms_notifications', isset($preferences['sms_notifications']) ? ($preferences['sms_notifications'] ? 1 : 0) : 0);
        $this->db->bind(':push_notifications', isset($preferences['push_notifications']) ? ($preferences['push_notifications'] ? 1 : 0) : 1);
        $this->db->bind(':toast_notifications', isset($preferences['toast_notifications']) ? ($preferences['toast_notifications'] ? 1 : 0) : 1);
        $this->db->bind(':event_reminders', isset($preferences['event_reminders']) ? ($preferences['event_reminders'] ? 1 : 0) : 1);
        $this->db->bind(':registration_updates', isset($preferences['registration_updates']) ? ($preferences['registration_updates'] ? 1 : 0) : 1);
        $this->db->bind(':judging_updates', isset($preferences['judging_updates']) ? ($preferences['judging_updates'] ? 1 : 0) : 1);
        $this->db->bind(':award_notifications', isset($preferences['award_notifications']) ? ($preferences['award_notifications'] ? 1 : 0) : 1);
        $this->db->bind(':system_announcements', isset($preferences['system_announcements']) ? ($preferences['system_announcements'] ? 1 : 0) : 1);
        $this->db->bind(':reminder_times', isset($preferences['reminder_times']) ? $preferences['reminder_times'] : '[1440, 60]');
        
        return $this->db->execute();
    }
    
    /**
     * Create default notification preferences for a user
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    public function createDefaultPreferences($userId) {
        $this->db->query('INSERT INTO user_notification_preferences 
                          (user_id, email_notifications, sms_notifications, push_notifications, toast_notifications,
                           event_reminders, registration_updates, judging_updates, award_notifications, 
                           system_announcements, reminder_times) 
                          VALUES (:user_id, 1, 0, 1, 1, 1, 1, 1, 1, 1, :reminder_times)');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':reminder_times', '[1440, 60]');
        
        return $this->db->execute();
    }
    
    /**
     * Ensure user preferences record exists, create if not
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    private function ensureUserPreferencesExist($userId) {
        $this->db->query('SELECT id FROM user_notification_preferences WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        
        if (!$this->db->single()) {
            return $this->createDefaultPreferences($userId);
        }
        
        return true;
    }
    
    /**
     * Subscribe user to event notifications
     * 
     * @param int $userId User ID
     * @param int $eventId Event ID
     * @param string $eventType Event type ('calendar_event' or 'car_show')
     * @param array $notificationTimes Array of minutes before event to notify
     * @param bool $notifyRegistrationEnd Whether to notify about registration deadline
     * @param array $registrationTimes Array of minutes before registration deadline
     * @return bool Success status
     */
    public function subscribeToEvent($userId, $eventId, $eventType, $notificationTimes, $notifyRegistrationEnd = false, $registrationTimes = []) {
        // Check if subscription already exists
        $this->db->query('SELECT id FROM event_notification_subscriptions 
                          WHERE user_id = :user_id AND event_id = :event_id AND event_type = :event_type');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $this->db->bind(':event_type', $eventType);
        
        $existing = $this->db->single();
        
        if ($existing) {
            // Update existing subscription
            $this->db->query('UPDATE event_notification_subscriptions SET 
                              notification_times = :notification_times,
                              notify_registration_end = :notify_registration_end,
                              registration_notification_times = :registration_notification_times,
                              is_active = 1,
                              updated_at = NOW()
                              WHERE id = :id');
            $this->db->bind(':id', $existing->id);
        } else {
            // Create new subscription
            $this->db->query('INSERT INTO event_notification_subscriptions 
                              (user_id, event_id, event_type, notification_times, notify_registration_end, registration_notification_times) 
                              VALUES (:user_id, :event_id, :event_type, :notification_times, :notify_registration_end, :registration_notification_times)');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':event_id', $eventId);
            $this->db->bind(':event_type', $eventType);
        }
        
        $this->db->bind(':notification_times', json_encode($notificationTimes));
        $this->db->bind(':notify_registration_end', $notifyRegistrationEnd ? 1 : 0);
        $this->db->bind(':registration_notification_times', json_encode($registrationTimes));
        
        $result = $this->db->execute();
        
        if ($result) {
            // Schedule notifications for this subscription
            $subscriptionId = $existing ? $existing->id : $this->db->lastInsertId();
            $this->scheduleNotifications($subscriptionId);
        }
        
        return $result;
    }
    
    /**
     * Unsubscribe user from event notifications
     * 
     * @param int $userId User ID
     * @param int $eventId Event ID
     * @param string $eventType Event type
     * @return bool Success status
     */
    public function unsubscribeFromEvent($userId, $eventId, $eventType) {
        // Mark subscription as inactive
        $this->db->query('UPDATE event_notification_subscriptions SET 
                          is_active = 0, updated_at = NOW()
                          WHERE user_id = :user_id AND event_id = :event_id AND event_type = :event_type');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $this->db->bind(':event_type', $eventType);
        
        $result = $this->db->execute();
        
        if ($result) {
            // Cancel pending notifications for this subscription
            $this->cancelPendingNotifications($userId, $eventId, $eventType);
        }
        
        return $result;
    }
    
    /**
     * Get user's event subscriptions
     * 
     * @param int $userId User ID
     * @return array Array of subscriptions
     */
    public function getUserSubscriptions($userId) {
        $this->db->query('SELECT ens.*, 
                          CASE 
                            WHEN ens.event_type = "calendar_event" THEN ce.title
                            WHEN ens.event_type = "car_show" THEN s.name
                          END as event_title,
                          CASE 
                            WHEN ens.event_type = "calendar_event" THEN ce.start_date
                            WHEN ens.event_type = "car_show" THEN s.start_date
                          END as event_date
                          FROM event_notification_subscriptions ens
                          LEFT JOIN calendar_events ce ON ens.event_id = ce.id AND ens.event_type = "calendar_event"
                          LEFT JOIN shows s ON ens.event_id = s.id AND ens.event_type = "car_show"
                          WHERE ens.user_id = :user_id AND ens.is_active = 1
                          ORDER BY event_date ASC');
        $this->db->bind(':user_id', $userId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if user is subscribed to an event
     * 
     * @param int $userId User ID
     * @param int $eventId Event ID
     * @param string $eventType Event type
     * @return bool Subscription status
     */
    public function isSubscribed($userId, $eventId, $eventType) {
        $this->db->query('SELECT id FROM event_notification_subscriptions 
                          WHERE user_id = :user_id AND event_id = :event_id AND event_type = :event_type AND is_active = 1');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $this->db->bind(':event_type', $eventType);
        
        return $this->db->single() !== false;
    }
    
    /**
     * Schedule notifications for a subscription
     * 
     * @param int $subscriptionId Subscription ID
     * @return bool Success status
     */
    public function scheduleNotifications($subscriptionId) {
        // Get subscription details
        $this->db->query('SELECT ens.*, u.email, u.phone, u.name as user_name,
                          CASE 
                            WHEN ens.event_type = "calendar_event" THEN ce.title
                            WHEN ens.event_type = "car_show" THEN s.name
                          END as event_title,
                          CASE 
                            WHEN ens.event_type = "calendar_event" THEN ce.start_date
                            WHEN ens.event_type = "car_show" THEN s.start_date
                          END as event_date,
                          CASE 
                            WHEN ens.event_type = "car_show" THEN s.registration_end
                            ELSE NULL
                          END as registration_end_date
                          FROM event_notification_subscriptions ens
                          JOIN users u ON ens.user_id = u.id
                          LEFT JOIN calendar_events ce ON ens.event_id = ce.id AND ens.event_type = "calendar_event"
                          LEFT JOIN shows s ON ens.event_id = s.id AND ens.event_type = "car_show"
                          WHERE ens.id = :subscription_id');
        $this->db->bind(':subscription_id', $subscriptionId);
        
        $subscription = $this->db->single();
        if (!$subscription) {
            return false;
        }
        
        // Get user preferences
        $preferences = $this->getUserPreferences($subscription->user_id);
        
        // Clear existing pending notifications for this subscription
        $this->db->query('DELETE FROM notification_queue 
                          WHERE subscription_id = :subscription_id AND status = "pending"');
        $this->db->bind(':subscription_id', $subscriptionId);
        $this->db->execute();
        
        $notificationTimes = json_decode($subscription->notification_times, true);
        $eventDateTime = new DateTime($subscription->event_date, new DateTimeZone('UTC'));
        
        // Schedule event reminder notifications
        foreach ($notificationTimes as $minutes) {
            $scheduledTime = clone $eventDateTime;
            $scheduledTime->sub(new DateInterval('PT' . $minutes . 'M'));
            
            // Only schedule if the time is in the future
            if ($scheduledTime > new DateTime('now', new DateTimeZone('UTC'))) {
                $this->scheduleNotification($subscription, $preferences, $scheduledTime, 'event_reminder', $minutes);
            }
        }
        
        // Schedule registration deadline notifications for car shows
        if ($subscription->event_type === 'car_show' && $subscription->notify_registration_end && $subscription->registration_end_date) {
            $registrationTimes = json_decode($subscription->registration_notification_times, true);
            $registrationDateTime = new DateTime($subscription->registration_end_date, new DateTimeZone('UTC'));
            
            foreach ($registrationTimes as $minutes) {
                $scheduledTime = clone $registrationDateTime;
                $scheduledTime->sub(new DateInterval('PT' . $minutes . 'M'));
                
                // Only schedule if the time is in the future
                if ($scheduledTime > new DateTime('now', new DateTimeZone('UTC'))) {
                    $this->scheduleNotification($subscription, $preferences, $scheduledTime, 'registration_deadline', $minutes);
                }
            }
        }
        
        return true;
    }
    
    /**
     * Schedule a single notification
     * 
     * @param object $subscription Subscription data
     * @param object $preferences User preferences
     * @param DateTime $scheduledTime When to send the notification
     * @param string $category Notification category
     * @param int $minutes Minutes before event/deadline
     * @return bool Success status
     */
    private function scheduleNotification($subscription, $preferences, $scheduledTime, $category, $minutes) {
        $notificationTypes = [];
        
        // Determine which notification types to send based on preferences
        if ($preferences->email_notifications && !empty($subscription->email)) {
            $notificationTypes[] = 'email';
        }
        if ($preferences->sms_notifications && !empty($subscription->phone)) {
            $notificationTypes[] = 'sms';
        }
        if ($preferences->push_notifications) {
            $notificationTypes[] = 'push';
        }
        if ($preferences->toast_notifications) {
            $notificationTypes[] = 'toast';
        }
        
        // Generate notification content
        $content = $this->generateNotificationContent($subscription, $category, $minutes);
        
        // Schedule each notification type
        foreach ($notificationTypes as $type) {
            $this->db->query('INSERT INTO notification_queue 
                              (user_id, subscription_id, notification_type, event_id, event_type, 
                               notification_category, scheduled_for, subject, message) 
                              VALUES (:user_id, :subscription_id, :notification_type, :event_id, :event_type, 
                                      :notification_category, :scheduled_for, :subject, :message)');
            
            $this->db->bind(':user_id', $subscription->user_id);
            $this->db->bind(':subscription_id', $subscription->id);
            $this->db->bind(':notification_type', $type);
            $this->db->bind(':event_id', $subscription->event_id);
            $this->db->bind(':event_type', $subscription->event_type);
            $this->db->bind(':notification_category', $category);
            $this->db->bind(':scheduled_for', $scheduledTime->format('Y-m-d H:i:s'));
            $this->db->bind(':subject', $content['subject']);
            $this->db->bind(':message', $content['message']);
            
            $this->db->execute();
        }
        
        return true;
    }
    
    /**
     * Generate notification content
     * 
     * @param object $subscription Subscription data
     * @param string $category Notification category
     * @param int $minutes Minutes before event/deadline
     * @return array Content with subject and message
     */
    private function generateNotificationContent($subscription, $category, $minutes) {
        $timeText = $this->formatTimeText($minutes);
        
        if ($category === 'event_reminder') {
            $subject = "Reminder: {$subscription->event_title} in {$timeText}";
            $message = "Hi {$subscription->user_name},\n\n";
            $message .= "This is a reminder that '{$subscription->event_title}' is starting in {$timeText}.\n\n";
            $message .= "Event Date: " . formatDateTimeForUser($subscription->event_date, $subscription->user_id, 'F j, Y \a\t g:i A T') . "\n\n";
            $message .= "Don't miss out!\n\n";
            $message .= "Best regards,\n";
            $message .= "Rowan Elite Rides Events Team";
        } else {
            $subject = "Registration Deadline: {$subscription->event_title} in {$timeText}";
            $message = "Hi {$subscription->user_name},\n\n";
            $message .= "The registration deadline for '{$subscription->event_title}' is in {$timeText}.\n\n";
            $message .= "Registration Deadline: " . formatDateTimeForUser($subscription->registration_end_date, $subscription->user_id, 'F j, Y \a\t g:i A T') . "\n";
            $message .= "Event Date: " . formatDateTimeForUser($subscription->event_date, $subscription->user_id, 'F j, Y \a\t g:i A T') . "\n\n";
            $message .= "Register now to secure your spot!\n\n";
            $message .= "Best regards,\n";
            $message .= "Rowan Elite Rides Events Team";
        }
        
        return [
            'subject' => $subject,
            'message' => $message
        ];
    }
    
    /**
     * Format time text for notifications
     * 
     * @param int $minutes Minutes
     * @return string Formatted time text
     */
    private function formatTimeText($minutes) {
        if ($minutes < 60) {
            return $minutes . ' minute' . ($minutes !== 1 ? 's' : '');
        } elseif ($minutes < 1440) {
            $hours = floor($minutes / 60);
            return $hours . ' hour' . ($hours !== 1 ? 's' : '');
        } else {
            $days = floor($minutes / 1440);
            return $days . ' day' . ($days !== 1 ? 's' : '');
        }
    }
    
    /**
     * Cancel pending notifications for a subscription
     * 
     * @param int $userId User ID
     * @param int $eventId Event ID
     * @param string $eventType Event type
     * @return bool Success status
     */
    private function cancelPendingNotifications($userId, $eventId, $eventType) {
        $this->db->query('UPDATE notification_queue SET status = "cancelled", updated_at = NOW()
                          WHERE user_id = :user_id AND event_id = :event_id AND event_type = :event_type AND status = "pending"');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $this->db->bind(':event_type', $eventType);
        
        return $this->db->execute();
    }
    
    /**
     * Get pending notifications that need to be sent
     * 
     * @param int $limit Maximum number of notifications to retrieve
     * @return array Array of pending notifications
     */
    public function getPendingNotifications($limit = 100) {
        if (DEBUG_MODE) {
            error_log("NotificationModel::getPendingNotifications - Getting pending notifications (limit: $limit)");
        }
        
        try {
            $this->db->query('SELECT nq.*, u.email, u.phone, u.name as user_name
                              FROM notification_queue nq
                              JOIN users u ON nq.user_id = u.id
                              WHERE nq.status = "pending" AND nq.scheduled_for <= NOW()
                              ORDER BY nq.scheduled_for ASC
                              LIMIT :limit');
            $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            
            $notifications = $this->db->resultSet();
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::getPendingNotifications - Found " . count($notifications) . " pending notifications");
            }
            
            return $notifications;
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::getPendingNotifications - Error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Mark notification as sent
     * 
     * @param int $notificationId Notification ID
     * @return bool Success status
     */
    public function markNotificationSent($notificationId) {
        if (DEBUG_MODE) {
            error_log("NotificationModel::markNotificationSent - Marking notification $notificationId as sent");
        }
        
        try {
            // First, check if the notification exists
            $this->db->query('SELECT id, status, last_attempt FROM notification_queue WHERE id = :id');
            $this->db->bind(':id', $notificationId);
            $existingNotification = $this->db->single();
            
            if (DEBUG_MODE) {
                if ($existingNotification) {
                    error_log("NotificationModel::markNotificationSent - Found notification: ID={$existingNotification->id}, Status={$existingNotification->status}, LastAttempt=" . ($existingNotification->last_attempt ?? 'NULL'));
                } else {
                    error_log("NotificationModel::markNotificationSent - Notification $notificationId not found in database");
                }
            }
            
            if (!$existingNotification) {
                if (DEBUG_MODE) {
                    error_log("NotificationModel::markNotificationSent - Cannot mark notification $notificationId as sent: notification not found");
                }
                return false;
            }
            
            // Update the notification
            $this->db->query('UPDATE notification_queue SET 
                              status = "sent", 
                              sent_at = NOW(),
                              last_attempt = NOW(), 
                              updated_at = NOW()
                              WHERE id = :id');
            $this->db->bind(':id', $notificationId);
            
            $result = $this->db->execute();
            $rowCount = $this->db->rowCount();
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::markNotificationSent - Update result: " . ($result ? 'success' : 'failed'));
                error_log("NotificationModel::markNotificationSent - Rows affected: $rowCount");
                
                if ($result && $rowCount > 0) {
                    // Verify the update worked
                    $this->db->query('SELECT id, status, sent_at, last_attempt FROM notification_queue WHERE id = :id');
                    $this->db->bind(':id', $notificationId);
                    $updatedNotification = $this->db->single();
                    
                    if ($updatedNotification) {
                        error_log("NotificationModel::markNotificationSent - After update: Status={$updatedNotification->status}, SentAt={$updatedNotification->sent_at}, LastAttempt=" . ($updatedNotification->last_attempt ?? 'NULL'));
                    }
                }
            }
            
            return $result && $rowCount > 0;
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::markNotificationSent - Error: " . $e->getMessage());
                error_log("NotificationModel::markNotificationSent - Stack trace: " . $e->getTraceAsString());
            }
            return false;
        }
    }
    
    /**
     * Mark notification as failed
     * 
     * @param int $notificationId Notification ID
     * @param string $errorMessage Error message
     * @return bool Success status
     */
    public function markNotificationFailed($notificationId, $errorMessage = '') {
        if (DEBUG_MODE) {
            error_log("NotificationModel::markNotificationFailed - Marking notification $notificationId as failed: $errorMessage");
        }
        
        try {
            $this->db->query('UPDATE notification_queue SET 
                              status = "failed", 
                              attempts = attempts + 1,
                              last_attempt = NOW(), 
                              error_message = :error_message,
                              updated_at = NOW()
                              WHERE id = :id');
            $this->db->bind(':id', $notificationId);
            $this->db->bind(':error_message', $errorMessage);
            
            $result = $this->db->execute();
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::markNotificationFailed - Result: " . ($result ? 'success' : 'failed'));
            }
            
            return $result;
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::markNotificationFailed - Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get notification settings
     * 
     * @param string $key Setting key (optional)
     * @return mixed Setting value or all settings
     */
    public function getNotificationSettings($key = null) {
        try {
            if ($key) {
                $this->db->query('SELECT setting_value, setting_type FROM notification_settings WHERE setting_key = :key');
                $this->db->bind(':key', $key);
                $setting = $this->db->single();
                
                if ($setting) {
                    return $this->parseSettingValue($setting->setting_value, $setting->setting_type);
                }
                return null;
            } else {
                $this->db->query('SELECT setting_key, setting_value, setting_type FROM notification_settings');
                $settings = $this->db->resultSet();
                
                $result = [];
                foreach ($settings as $setting) {
                    $result[$setting->setting_key] = $this->parseSettingValue($setting->setting_value, $setting->setting_type);
                }
                return $result;
            }
        } catch (Exception $e) {
            // If table doesn't exist or there's an error, return null/empty array
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("NotificationModel::getNotificationSettings - Error: " . $e->getMessage());
            }
            return $key ? null : [];
        }
    }
    
    /**
     * Update notification setting
     * 
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @param string $type Setting type
     * @return bool Success status
     */
    public function updateNotificationSetting($key, $value, $type = 'string') {
        try {
            $settingValue = $this->formatSettingValue($value, $type);
            
            // Use INSERT ... ON DUPLICATE KEY UPDATE to handle both insert and update
            $this->db->query('INSERT INTO notification_settings (setting_key, setting_value, setting_type, description) 
                              VALUES (:key, :value, :type, :description)
                              ON DUPLICATE KEY UPDATE 
                              setting_value = VALUES(setting_value),
                              setting_type = VALUES(setting_type),
                              updated_at = NOW()');
            
            $this->db->bind(':key', $key);
            $this->db->bind(':value', $settingValue);
            $this->db->bind(':type', $type);
            $this->db->bind(':description', $this->getDefaultDescription($key));
            
            $result = $this->db->execute();
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("NotificationModel::updateNotificationSetting - Updated $key = $settingValue ($type): " . ($result ? 'success' : 'failed'));
            }
            
            return $result;
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("NotificationModel::updateNotificationSetting - Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get default description for a setting key
     * 
     * @param string $key Setting key
     * @return string Default description
     */
    private function getDefaultDescription($key) {
        $descriptions = [
            'email_enabled' => 'Enable email notifications globally',
            'sms_enabled' => 'Enable SMS notifications globally',
            'push_enabled' => 'Enable push notifications globally',
            'toast_enabled' => 'Enable toast notifications globally',
            'email_from_address' => 'Default from email address for notifications',
            'email_from_name' => 'Default from name for email notifications',
            'max_notification_attempts' => 'Maximum number of attempts to send a notification',
            'notification_retry_interval' => 'Minutes to wait before retrying failed notifications'
        ];
        
        return $descriptions[$key] ?? "Setting for $key";
    }
    
    /**
     * Parse setting value based on type
     * 
     * @param string $value Raw value
     * @param string $type Value type
     * @return mixed Parsed value
     */
    private function parseSettingValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return (bool) $value;
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * Format setting value for storage
     * 
     * @param mixed $value Value to format
     * @param string $type Value type
     * @return string Formatted value
     */
    private function formatSettingValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'integer':
                return (string) (int) $value;
            case 'json':
                return json_encode($value);
            default:
                return (string) $value;
        }
    }
    
    /**
     * Get SMS providers
     * 
     * @param bool $activeOnly Whether to get only active providers
     * @return array Array of SMS providers
     */
    public function getSmsProviders($activeOnly = false) {
        $sql = 'SELECT * FROM sms_providers';
        if ($activeOnly) {
            $sql .= ' WHERE is_active = 1';
        }
        $sql .= ' ORDER BY is_default DESC, name ASC';
        
        $this->db->query($sql);
        return $this->db->resultSet();
    }
    
    /**
     * Get default SMS provider
     * 
     * @return object|bool Default SMS provider or false if none set
     */
    public function getDefaultSmsProvider() {
        $this->db->query('SELECT * FROM sms_providers WHERE is_default = 1 AND is_active = 1 LIMIT 1');
        return $this->db->single();
    }
    
    /**
     * Update SMS provider configuration
     * 
     * @param int $providerId Provider ID
     * @param array $config Configuration data
     * @return bool Success status
     */
    public function updateSmsProvider($providerId, $config) {
        // If setting as default, unset other defaults first
        if (isset($config['is_default']) && $config['is_default']) {
            $this->db->query('UPDATE sms_providers SET is_default = 0 WHERE id != :id');
            $this->db->bind(':id', $providerId);
            $this->db->execute();
        }
        
        $this->db->query('UPDATE sms_providers SET 
                          is_active = :is_active,
                          is_default = :is_default,
                          configuration = :configuration,
                          updated_at = NOW()
                          WHERE id = :id');
        
        $this->db->bind(':id', $providerId);
        $this->db->bind(':is_active', isset($config['is_active']) ? ($config['is_active'] ? 1 : 0) : 0);
        $this->db->bind(':is_default', isset($config['is_default']) ? ($config['is_default'] ? 1 : 0) : 0);
        $this->db->bind(':configuration', json_encode($config['configuration'] ?? $config));
        
        return $this->db->execute();
    }
    
    /**
     * Get notification settings (alias for getNotificationSettings)
     * 
     * @param string $key Setting key (optional)
     * @return mixed Setting value or all settings
     */
    public function getSettings($key = null) {
        return $this->getNotificationSettings($key);
    }
    
    /**
     * Check if notification system is properly installed
     * 
     * @return bool True if all required tables exist
     */
    public function isNotificationSystemInstalled() {
        try {
            $requiredTables = [
                'notification_settings',
                'user_notification_preferences',
                'event_notification_subscriptions',
                'notification_queue'
            ];
            
            foreach ($requiredTables as $table) {
                $this->db->query("SHOW TABLES LIKE '$table'");
                if ($this->db->rowCount() === 0) {
                    return false;
                }
            }
            
            return true;
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("NotificationModel::isNotificationSystemInstalled - Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Initialize default notification settings if they don't exist
     * 
     * @return bool Success status
     */
    public function initializeDefaultSettings() {
        try {
            $defaultSettings = [
                'email_enabled' => ['1', 'boolean', 'Enable email notifications globally'],
                'sms_enabled' => ['1', 'boolean', 'Enable SMS notifications globally'],
                'push_enabled' => ['1', 'boolean', 'Enable push notifications globally'],
                'toast_enabled' => ['1', 'boolean', 'Enable toast notifications globally']
            ];
            
            foreach ($defaultSettings as $key => $config) {
                // Check if setting exists
                $existing = $this->getNotificationSettings($key);
                if ($existing === null) {
                    // Insert the setting
                    $this->db->query("INSERT IGNORE INTO notification_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)");
                    $this->db->bind(1, $key);
                    $this->db->bind(2, $config[0]);
                    $this->db->bind(3, $config[1]);
                    $this->db->bind(4, $config[2]);
                    $this->db->execute();
                }
            }
            
            return true;
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("NotificationModel::initializeDefaultSettings - Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get notification queue items
     * 
     * @param string $status Optional status filter
     * @param int $limit Maximum number of items to retrieve
     * @param int $offset Offset for pagination
     * @return array Array of queue items
     */
    public function getQueueItems($status = null, $limit = 100, $offset = 0) {
        $sql = 'SELECT nq.*, u.email, u.phone, u.name as user_name
                FROM notification_queue nq
                JOIN users u ON nq.user_id = u.id';
        
        if ($status) {
            $sql .= ' WHERE nq.status = :status';
        }
        
        $sql .= ' ORDER BY nq.created_at DESC
                  LIMIT :limit OFFSET :offset';
        
        $this->db->query($sql);
        
        if ($status) {
            $this->db->bind(':status', $status);
        }
        $this->db->bind(':limit', $limit);
        $this->db->bind(':offset', $offset);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get notification queue statistics
     * 
     * @return array Queue statistics
     */
    public function getQueueStats() {
        $stats = [];
        
        // Get counts by status
        $this->db->query('SELECT status, COUNT(*) as count 
                          FROM notification_queue 
                          GROUP BY status');
        $statusCounts = $this->db->resultSet();
        
        foreach ($statusCounts as $statusCount) {
            $stats[$statusCount->status] = $statusCount->count;
        }
        
        // Ensure all statuses are represented
        $defaultStats = [
            'pending' => 0,
            'sent' => 0,
            'failed' => 0
        ];
        
        $stats = array_merge($defaultStats, $stats);
        
        // Get total count
        $stats['total'] = array_sum($stats);
        
        // Get recent activity (last 24 hours)
        $this->db->query('SELECT COUNT(*) as count 
                          FROM notification_queue 
                          WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)');
        $recentActivity = $this->db->single();
        $stats['recent_24h'] = $recentActivity ? $recentActivity->count : 0;
        
        return $stats;
    }
    
    /**
     * Clear failed notifications from the queue
     * 
     * @return bool Success status
     */
    public function clearFailedNotifications() {
        $this->db->query('DELETE FROM notification_queue WHERE status = "failed"');
        return $this->db->execute();
    }
    
    /**
     * Clear unread toast notifications for a user
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    public function clearUserToastNotifications($userId) {
        $this->db->query('UPDATE user_toast_notifications SET is_read = 1 WHERE user_id = :user_id AND is_read = 0');
        $this->db->bind(':user_id', $userId);
        return $this->db->execute();
    }
    
    /**
     * Clear unread push notifications for a user
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    public function clearUserPushNotifications($userId) {
        $this->db->query('UPDATE user_push_notifications SET is_read = 1 WHERE user_id = :user_id AND is_read = 0');
        $this->db->bind(':user_id', $userId);
        return $this->db->execute();
    }
    
    /**
     * Fix NULL last_attempt values for sent notifications
     * 
     * This method fixes the issue where sent notifications have NULL last_attempt values
     * by setting last_attempt to sent_at or updated_at as appropriate.
     * 
     * @return array Results of the fix operation
     */
    public function fixNullLastAttemptValues() {
        if (DEBUG_MODE) {
            error_log("NotificationModel::fixNullLastAttemptValues - Starting fix");
        }
        
        try {
            // Count notifications that need fixing
            $this->db->query('SELECT COUNT(*) as count FROM notification_queue WHERE status = "sent" AND last_attempt IS NULL');
            $result = $this->db->single();
            $needsFixing = $result->count ?? 0;
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::fixNullLastAttemptValues - Found $needsFixing notifications needing fix");
            }
            
            if ($needsFixing == 0) {
                return [
                    'success' => true,
                    'message' => 'No notifications need fixing',
                    'fixed_count' => 0
                ];
            }
            
            // Fix the NULL last_attempt values
            // Use sent_at if available, otherwise use updated_at
            $this->db->query('UPDATE notification_queue 
                             SET last_attempt = COALESCE(sent_at, updated_at) 
                             WHERE status = "sent" AND last_attempt IS NULL');
            
            $result = $this->db->execute();
            $affectedRows = $this->db->rowCount();
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::fixNullLastAttemptValues - Fixed $affectedRows notifications");
            }
            
            return [
                'success' => $result,
                'message' => $result ? "Fixed $affectedRows notification records" : 'Failed to fix notifications',
                'fixed_count' => $affectedRows
            ];
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::fixNullLastAttemptValues - Error: " . $e->getMessage());
            }
            
            return [
                'success' => false,
                'message' => 'Error fixing notifications: ' . $e->getMessage(),
                'fixed_count' => 0
            ];
        }
    }
    
    /**
     * Queue a test notification
     * 
     * @param int $userId User ID
     * @param string $type Notification type (email, sms, push, toast)
     * @param string $subject Subject/title
     * @param string $message Message content
     * @param string|null $scheduledTime Optional scheduled time (Y-m-d H:i:s format)
     * @return bool Success status
     */
    public function queueTestNotification($userId, $type, $subject, $message, $scheduledTime = null) {
        if (DEBUG_MODE) {
            error_log("NotificationModel::queueTestNotification - START");
            error_log("NotificationModel::queueTestNotification - User: $userId, Type: $type, Subject: $subject");
            error_log("NotificationModel::queueTestNotification - Scheduled time: " . ($scheduledTime ?? 'immediate'));
        }
        
        try {
            // Set scheduled time or immediate processing
            // Use database NOW() function to avoid timezone issues
            if ($scheduledTime) {
                $scheduleFor = $scheduledTime;
            } else {
                // For immediate processing, use database current time
                $this->db->query('SELECT NOW() as db_current_time');
                $currentTime = $this->db->single();
                $scheduleFor = $currentTime->db_current_time;
            }
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::queueTestNotification - Schedule for: $scheduleFor");
            }
            
            // Try to insert with current table structure
            // Use dummy values for required fields that don't apply to test notifications
            $sql = 'INSERT INTO notification_queue 
                    (user_id, subscription_id, notification_type, event_id, event_type, notification_category, 
                     scheduled_for, subject, message, status, created_at) 
                    VALUES (:user_id, :subscription_id, :notification_type, :event_id, :event_type, :notification_category, 
                            :scheduled_for, :subject, :message, :status, NOW())';
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::queueTestNotification - SQL: " . $sql);
            }
            
            $this->db->query($sql);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':subscription_id', 0);
            $this->db->bind(':notification_type', $type);
            $this->db->bind(':event_id', 0);
            $this->db->bind(':event_type', 'calendar_event'); // Use existing enum value
            $this->db->bind(':notification_category', 'event_reminder'); // Use existing enum value
            $this->db->bind(':scheduled_for', $scheduleFor);
            $this->db->bind(':subject', $subject . ' [TEST]'); // Mark as test in subject
            $this->db->bind(':message', $message);
            $this->db->bind(':status', 'pending');
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::queueTestNotification - About to execute...");
            }
            
            $result = $this->db->execute();
            
            if (DEBUG_MODE) {
                error_log("NotificationModel::queueTestNotification - Execute result: " . ($result ? 'TRUE' : 'FALSE'));
                if ($result) {
                    $insertId = $this->db->lastInsertId();
                    error_log("NotificationModel::queueTestNotification - Queued notification ID: " . $insertId);
                } else {
                    error_log("NotificationModel::queueTestNotification - Database error: " . $this->db->error());
                }
            }
            
            return $result;
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::queueTestNotification - EXCEPTION: " . $e->getMessage());
                error_log("NotificationModel::queueTestNotification - Exception trace: " . $e->getTraceAsString());
            }
            return false;
        }
    }
    
    /**
     * Get unread notifications for a user
     *
     * @param int $userId User ID
     * @return array Array of unread notifications with separate push and toast
     */
    public function getUnreadNotifications($userId) {
        try {
            $notifications = [
                'push' => [],
                'toast' => []
            ];

            // Get unread push notifications
            $this->db->query('SELECT *, "push" as notification_type FROM user_push_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20');
            $this->db->bind(':user_id', $userId);
            $notifications['push'] = $this->db->resultSet();

            // Get unread toast notifications
            $this->db->query('SELECT *, "toast" as notification_type FROM user_toast_notifications
                             WHERE user_id = :user_id AND is_read = 0
                             ORDER BY created_at DESC LIMIT 20');
            $this->db->bind(':user_id', $userId);
            $notifications['toast'] = $this->db->resultSet();

            if (DEBUG_MODE) {
                error_log("NotificationModel::getUnreadNotifications - Found " . count($notifications['push']) . " push, " . count($notifications['toast']) . " toast notifications for user $userId");
            }

            return $notifications;

        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::getUnreadNotifications - Error: " . $e->getMessage());
            }
            return ['push' => [], 'toast' => []];
        }
    }
    
    /**
     * Mark notifications as read
     *
     * @param int $userId User ID
     * @param array $notificationIds Array of notification IDs
     * @param string $type Notification type (toast, push, etc.)
     * @return bool Success status
     */
    public function markNotificationsAsRead($userId, $notificationIds, $type = 'toast') {
        try {
            if (empty($notificationIds)) {
                return true;
            }

            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';

            // Determine which table to update based on type
            $table = ($type === 'push') ? 'user_push_notifications' : 'user_toast_notifications';

            $this->db->query("UPDATE $table
                             SET is_read = 1
                             WHERE user_id = ?
                             AND id IN ($placeholders)");

            // Bind user ID first
            $this->db->bind(1, $userId);

            // Bind notification IDs
            foreach ($notificationIds as $index => $id) {
                $this->db->bind($index + 2, $id);
            }
            
            return $this->db->execute();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::markNotificationsAsRead - Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get push notifications by IDs for a specific user
     *
     * @param int $userId User ID
     * @param array $notificationIds Array of notification IDs
     * @return array Array of push notification objects
     */
    public function getPushNotificationsByIds($userId, $notificationIds) {
        try {
            if (empty($notificationIds)) {
                return [];
            }

            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';

            $this->db->query("SELECT * FROM user_push_notifications
                             WHERE user_id = ?
                             AND id IN ($placeholders)
                             AND is_read = 0
                             ORDER BY created_at DESC");

            // Bind user ID first
            $this->db->bind(1, $userId);

            // Bind notification IDs
            foreach ($notificationIds as $index => $id) {
                $this->db->bind($index + 2, $id);
            }

            return $this->db->resultSet();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::getPushNotificationsByIds - Error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Mark push notification as sent
     *
     * @param int $notificationId Notification ID
     * @return bool Success status
     */
    public function markPushNotificationAsSent($notificationId) {
        try {
            $this->db->query('UPDATE user_push_notifications 
                             SET is_read = 1, sent_at = NOW() 
                             WHERE id = :id');
            $this->db->bind(':id', $notificationId);
            
            return $this->db->execute();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationModel::markPushNotificationAsSent - Error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    // ========================================
    // PWA-SPECIFIC METHODS
    // ========================================
    
    /**
     * DEPRECATED: Dead Web Push API method - using FCM OAuth v1 instead
     * Use saveFCMToken() method for FCM-based notifications
     */
    /*
    public function savePushSubscription($userId, $endpoint, $p256dhKey, $authKey, $userAgent = '') {
        try {
            // Check if subscription already exists
            $this->db->query('SELECT id FROM push_subscriptions WHERE user_id = :user_id AND endpoint = :endpoint');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':endpoint', $endpoint);
            $existing = $this->db->single();
            
            if ($existing) {
                // Update existing subscription
                $this->db->query('UPDATE push_subscriptions SET 
                                  p256dh_key = :p256dh_key,
                                  auth_key = :auth_key,
                                  user_agent = :user_agent,
                                  active = 1,
                                  updated_at = NOW()
                                  WHERE id = :id');
                $this->db->bind(':id', $existing->id);
                $this->db->bind(':p256dh_key', $p256dhKey);
                $this->db->bind(':auth_key', $authKey);
                $this->db->bind(':user_agent', $userAgent);
                
                if ($this->db->execute()) {
                    return $existing->id;
                }
                return false;
            } else {
                // Create new subscription
                $this->db->query('INSERT INTO push_subscriptions 
                                  (user_id, endpoint, p256dh_key, auth_key, user_agent, active) 
                                  VALUES (:user_id, :endpoint, :p256dh_key, :auth_key, :user_agent, 1)');
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':endpoint', $endpoint);
                $this->db->bind(':p256dh_key', $p256dhKey);
                $this->db->bind(':auth_key', $authKey);
                $this->db->bind(':user_agent', $userAgent);
                
                if ($this->db->execute()) {
                    return $this->db->lastInsertId();
                }
                return false;
            }
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Save push subscription error: " . $e->getMessage());
            }
            return false;
        }
    }
    */
    
    /**
     * Get user's push subscriptions
     * 
     * @param int $userId User ID
     * @return array Array of active subscriptions
     */
    public function getUserPushSubscriptions($userId) {
        try {
            $this->db->query('SELECT * FROM push_subscriptions 
                              WHERE user_id = :user_id AND active = 1 
                              ORDER BY created_at DESC');
            $this->db->bind(':user_id', $userId);
            return $this->db->resultSet();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get user push subscriptions error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Remove push subscription
     * 
     * @param int $userId User ID
     * @param string $endpoint Endpoint to remove (optional)
     * @return bool Success status
     */
    public function removePushSubscription($userId, $endpoint = null) {
        try {
            if ($endpoint) {
                $this->db->query('UPDATE push_subscriptions SET active = 0 
                                  WHERE user_id = :user_id AND endpoint = :endpoint');
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':endpoint', $endpoint);
            } else {
                $this->db->query('UPDATE push_subscriptions SET active = 0 
                                  WHERE user_id = :user_id');
                $this->db->bind(':user_id', $userId);
            }
            
            return $this->db->execute();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Remove push subscription error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Remove invalid push subscription
     * 
     * @param int $subscriptionId Subscription ID
     * @return bool Success status
     */
    public function removeInvalidPushSubscription($subscriptionId) {
        try {
            $this->db->query('UPDATE push_subscriptions SET active = 0 
                              WHERE id = :id');
            $this->db->bind(':id', $subscriptionId);
            return $this->db->execute();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Remove invalid push subscription error: " . $e->getMessage());
            }
            return false;
        }
    }
    


    

    

    
    /**
     * Get upcoming events for reminders
     * 
     * @param int $hours Hours ahead to look for events
     * @return array Array of upcoming events
     */
    public function getUpcomingEvents($hours = 24) {
        try {
            $this->db->query('SELECT s.*, s.name as event_name, s.start_date as start_time
                              FROM shows s 
                              WHERE s.start_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL :hours HOUR)
                              AND s.status = "active"
                              ORDER BY s.start_date ASC');
            $this->db->bind(':hours', $hours);
            return $this->db->resultSet();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get upcoming events error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Get event registrations for notifications
     * 
     * @param int $eventId Event ID
     * @return array Array of registrations
     */
    public function getEventRegistrations($eventId) {
        try {
            $this->db->query('SELECT r.*, u.id as user_id, u.name, u.email 
                              FROM registrations r
                              JOIN users u ON r.user_id = u.id
                              WHERE r.show_id = :event_id AND r.status = "confirmed"');
            $this->db->bind(':event_id', $eventId);
            return $this->db->resultSet();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get event registrations error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Get pending judging assignments
     * 
     * @return array Array of pending judging assignments
     */
    public function getPendingJudging() {
        try {
            $this->db->query('SELECT sj.*, s.name as show_name, s.end_date as deadline, u.id as judge_id
                              FROM show_judges sj
                              JOIN shows s ON sj.show_id = s.id
                              JOIN users u ON sj.judge_id = u.id
                              WHERE s.status = "active" 
                              AND s.end_date > NOW()
                              AND sj.status = "assigned"
                              ORDER BY s.end_date ASC');
            return $this->db->resultSet();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get pending judging error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Get coordinator user IDs
     * 
     * @return array Array of coordinator user IDs
     */
    public function getCoordinatorIds() {
        try {
            $this->db->query('SELECT id FROM users WHERE role IN ("admin", "coordinator") AND active = 1');
            $results = $this->db->resultSet();
            return array_column($results, 'id');
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get coordinator IDs error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Get judge user IDs
     * 
     * @return array Array of judge user IDs
     */
    public function getJudgeIds() {
        try {
            $this->db->query('SELECT id FROM users WHERE role IN ("admin", "judge") AND active = 1');
            $results = $this->db->resultSet();
            return array_column($results, 'id');
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get judge IDs error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Get staff user IDs
     * 
     * @return array Array of staff user IDs
     */
    public function getStaffIds() {
        try {
            $this->db->query('SELECT id FROM users WHERE role IN ("admin", "staff") AND active = 1');
            $results = $this->db->resultSet();
            return array_column($results, 'id');
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get staff IDs error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Get all active user IDs
     * 
     * @return array Array of all active user IDs
     */
    public function getAllUserIds() {
        try {
            $this->db->query('SELECT id FROM users WHERE active = 1');
            $results = $this->db->resultSet();
            return array_column($results, 'id');
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get all user IDs error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Log notification attempt
     * 
     * @param int $userId User ID (optional)
     * @param string $type Notification type
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $status Status (sent, delivered, failed, clicked)
     * @param array $metadata Additional metadata
     * @return bool Success status
     */
    public function logNotification($userId, $type, $title, $message, $status = 'sent', $metadata = []) {
        try {
            $this->db->query('INSERT INTO notification_log 
                              (user_id, notification_type, title, message, status, metadata) 
                              VALUES (:user_id, :type, :title, :message, :status, :metadata)');
            
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':type', $type);
            $this->db->bind(':title', $title);
            $this->db->bind(':message', $message);
            $this->db->bind(':status', $status);
            $this->db->bind(':metadata', json_encode($metadata));
            
            return $this->db->execute();
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Log notification error: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Send push notification to user(s)
     *
     * @param string $title Notification title
     * @param string $message Notification message
     * @param int|null $targetUserId Target user ID (null for all users)
     * @param string $icon Icon URL
     * @param string $badge Badge URL
     * @param string $url Action URL
     * @return bool Success status
     */
    public function sendPushNotification($title, $message, $targetUserId = null, $icon = '/public/images/icons/icon-192x192.png', $badge = '/public/images/icons/badge-72x72.png', $url = '/', $data = []) {
        try {
            if (DEBUG_MODE) {
                error_log("[FCM] Sending push notification: $title to user " . ($targetUserId ?? 'all'));
            }

            // Get FCM tokens for target users
            if ($targetUserId) {
                $tokens = $this->getUserFCMTokens($targetUserId);
            } else {
                // Get all active FCM tokens
                $this->db->query('SELECT token FROM fcm_tokens WHERE active = 1');
                $results = $this->db->resultSet();
                $tokens = array_column($results, 'token');
            }

            if (empty($tokens)) {
                if (DEBUG_MODE) {
                    error_log("[FCM] No active FCM tokens found");
                }
                return false;
            }

            // Load FCM helper
            require_once APPROOT . '/helpers/fcm_v1_helper.php';
            
            $serviceAccountPath = APPROOT . '/config/firebase-service-account.json';
            if (!file_exists($serviceAccountPath)) {
                throw new Exception('Firebase service account file not found');
            }
            
            $fcm = new FCMv1Helper($serviceAccountPath);

            $successCount = 0;
            $totalCount = count($tokens);

            // Prepare notification data
            $notificationData = array_merge([
                'url' => $url,
                'timestamp' => time()
            ], $data);

            foreach ($tokens as $token) {
                try {
                    $result = $fcm->sendNotification($token, $title, $message, $notificationData);
                    
                    if ($result['success']) {
                        $successCount++;
                        
                        if (DEBUG_MODE) {
                            error_log("[FCM] Push notification sent successfully to token: " . substr($token, -10));
                        }
                    } else {
                        if (DEBUG_MODE) {
                            error_log("[FCM] Failed to send to token " . substr($token, -10) . ": " . $result['error']);
                        }
                        
                        // Check if token is invalid and remove it
                        if (strpos($result['error'], 'UNREGISTERED') !== false || 
                            strpos($result['error'], 'INVALID_ARGUMENT') !== false) {
                            $this->removeInvalidFCMToken($token);
                        }
                    }

                } catch (Exception $e) {
                    if (DEBUG_MODE) {
                        error_log("[FCM] Exception sending to token " . substr($token, -10) . ": " . $e->getMessage());
                    }
                }
            }

            if (DEBUG_MODE) {
                error_log("[FCM] Push notification sent to $successCount/$totalCount tokens");
            }

            return $successCount > 0;

        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Send push notification error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get user's FCM tokens
     * 
     * @param int $userId User ID
     * @return array Array of active FCM tokens
     */
    public function getUserFCMTokens($userId) {
        try {
            $this->db->query('SELECT token FROM fcm_tokens 
                              WHERE user_id = :user_id AND active = 1 
                              ORDER BY last_used DESC');
            $this->db->bind(':user_id', $userId);
            $results = $this->db->resultSet();
            
            return array_column($results, 'token');
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Get user FCM tokens error: " . $e->getMessage());
            }
            return [];
        }
    }
    
    /**
     * Remove invalid FCM token
     * 
     * @param string $token FCM token to remove
     * @return bool Success status
     */
    public function removeInvalidFCMToken($token) {
        try {
            $this->db->query('UPDATE fcm_tokens SET active = 0 
                              WHERE token = :token');
            $this->db->bind(':token', $token);
            
            $result = $this->db->execute();
            
            if (DEBUG_MODE && $result) {
                error_log("[FCM] Removed invalid token: " . substr($token, -10));
            }
            
            return $result;
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Remove invalid FCM token error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get FCM token count for user
     * 
     * @param int $userId User ID
     * @return int Number of active tokens
     */
    public function getUserFCMTokenCount($userId) {
        try {
            $this->db->query('SELECT COUNT(*) as count FROM fcm_tokens 
                              WHERE user_id = :user_id AND active = 1');
            $this->db->bind(':user_id', $userId);
            $result = $this->db->single();
            
            return $result ? $result->count : 0;
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Get FCM token count error: " . $e->getMessage());
            }
            return 0;
        }
    }
    
    /**
     * Clean up old FCM tokens
     * 
     * @param int $daysOld Remove tokens older than this many days
     * @return bool Success status
     */
    public function cleanupOldFCMTokens($daysOld = 30) {
        try {
            $this->db->query('DELETE FROM fcm_tokens 
                              WHERE active = 0 AND updated_at < DATE_SUB(NOW(), INTERVAL :days DAY)');
            $this->db->bind(':days', $daysOld);
            
            $result = $this->db->execute();
            
            if (DEBUG_MODE && $result) {
                error_log("[FCM] Cleaned up old FCM tokens older than {$daysOld} days");
            }
            
            return $result;
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Cleanup old FCM tokens error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Save FCM token for user
     * 
     * @param int $userId User ID
     * @param string $fcmToken FCM registration token
     * @param string $userAgent User agent string
     * @return int|bool Token ID or false on failure
     */
    public function saveFCMToken($userId, $fcmToken, $userAgent = '') {
        try {
            // Ensure FCM tokens table exists
            $this->createFCMTokensTableIfNotExists();
            
            // Check if token already exists for this user
            $this->db->query('SELECT id FROM fcm_tokens WHERE user_id = :user_id AND token = :token');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':token', $fcmToken);
            $existing = $this->db->single();
            
            if ($existing) {
                // Update existing token
                $this->db->query('UPDATE fcm_tokens SET 
                                  user_agent = :user_agent,
                                  active = 1,
                                  last_used = NOW(),
                                  updated_at = NOW()
                                  WHERE id = :id');
                $this->db->bind(':id', $existing->id);
                $this->db->bind(':user_agent', $userAgent);
                
                if ($this->db->execute()) {
                    return $existing->id;
                }
                return false;
            } else {
                // Allow multiple active tokens per user (one per device)
                // Only deactivate tokens from the SAME device/browser, not all user tokens

                // Extract device identifier from user agent for better device detection
                $deviceId = $this->extractDeviceIdentifier($userAgent);

                // Deactivate old tokens from the SAME device only
                $this->db->query('UPDATE fcm_tokens SET active = 0
                                  WHERE user_id = :user_id
                                  AND (user_agent = :user_agent OR user_agent LIKE :device_pattern)');
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':user_agent', $userAgent);
                $this->db->bind(':device_pattern', '%' . $deviceId . '%');
                $this->db->execute();

                // Insert new token (allows multiple active tokens per user, one per device)
                $this->db->query('INSERT INTO fcm_tokens
                                  (user_id, token, user_agent, active, created_at, updated_at, last_used)
                                  VALUES (:user_id, :token, :user_agent, 1, NOW(), NOW(), NOW())');
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':token', $fcmToken);
                $this->db->bind(':user_agent', $userAgent);

                if ($this->db->execute()) {
                    return $this->db->lastInsertId();
                }
                return false;
            }
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Save FCM token error: " . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Create FCM tokens table if it doesn't exist
     */
    private function createFCMTokensTableIfNotExists() {
        try {
            // Check if table exists
            $this->db->query("SHOW TABLES LIKE 'fcm_tokens'");
            if ($this->db->single()) {
                return; // Table already exists
            }
            
            // Create table
            $sql = "CREATE TABLE fcm_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token TEXT NOT NULL,
                user_agent TEXT,
                active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_active (active),
                INDEX idx_user_active (user_id, active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $this->db->query($sql);
            if ($this->db->execute()) {
                if (DEBUG_MODE) {
                    error_log('[FCM] Created fcm_tokens table');
                }
            }
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log('[FCM] Create FCM tokens table error: ' . $e->getMessage());
            }
        }
    }
    
    /**
     * Reset all push notification subscriptions for a user
     * This removes all FCM tokens and clears browser subscriptions
     * 
     * @param int $userId User ID
     * @return array Result with success status and message
     */
    public function resetUserPushSubscriptions($userId) {
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Get count of tokens before deletion for logging
            $tokenCount = $this->getUserFCMTokenCount($userId);
            
            // Remove all FCM tokens for this user
            $this->db->query('DELETE FROM fcm_tokens WHERE user_id = :user_id');
            $this->db->bind(':user_id', $userId);
            $fcmResult = $this->db->execute();
            
            // Clear any push notification preferences (but keep other preferences)
            $this->db->query('UPDATE user_notification_preferences 
                              SET push_notifications = 0, updated_at = NOW() 
                              WHERE user_id = :user_id');
            $this->db->bind(':user_id', $userId);
            $prefsResult = $this->db->execute();
            
            // Clear any pending push notifications in queue for this user
            $this->db->query('DELETE FROM notification_queue 
                              WHERE user_id = :user_id AND notification_type = "push"');
            $this->db->bind(':user_id', $userId);
            $queueResult = $this->db->execute();
            
            // Commit transaction
            $this->db->commit();
            
            if (DEBUG_MODE) {
                error_log("[FCM] Reset push subscriptions for user {$userId}: removed {$tokenCount} tokens");
            }
            
            return [
                'success' => true,
                'message' => "Successfully reset push notification subscriptions. Removed {$tokenCount} device(s).",
                'tokens_removed' => $tokenCount
            ];
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            
            if (DEBUG_MODE) {
                error_log("[FCM] Reset push subscriptions error for user {$userId}: " . $e->getMessage());
            }
            
            return [
                'success' => false,
                'message' => 'Failed to reset push notification subscriptions. Please try again.',
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Extract device identifier from user agent for device-based token management
     *
     * @param string $userAgent User agent string
     * @return string Device identifier
     */
    private function extractDeviceIdentifier($userAgent) {
        if (empty($userAgent)) {
            return 'unknown';
        }

        // Extract key device/browser identifiers
        $patterns = [
            'iPhone' => '/iPhone/',
            'iPad' => '/iPad/',
            'Android' => '/Android/',
            'Chrome' => '/Chrome\/[\d.]+/',
            'Firefox' => '/Firefox\/[\d.]+/',
            'Safari' => '/Safari\/[\d.]+/',
            'Edge' => '/Edge\/[\d.]+/',
            'Windows' => '/Windows NT/',
            'Mac' => '/Mac OS X/',
            'Linux' => '/Linux/'
        ];

        $deviceParts = [];
        foreach ($patterns as $name => $pattern) {
            if (preg_match($pattern, $userAgent)) {
                $deviceParts[] = $name;
            }
        }

        // Create a unique device identifier
        return !empty($deviceParts) ? implode('-', $deviceParts) : 'unknown';
    }

    /**
     * Get user's push subscription status and statistics
     *
     * @param int $userId User ID
     * @return array Subscription status information
     */
    public function getUserPushSubscriptionStatus($userId) {
        try {
            // Get active token count
            $activeTokens = $this->getUserFCMTokenCount($userId);
            
            // Get user preferences
            $preferences = $this->getUserPreferences($userId);
            $pushEnabled = $preferences && $preferences->push_notifications == 1;
            
            // Get last token activity
            $this->db->query('SELECT last_used, user_agent FROM fcm_tokens 
                              WHERE user_id = :user_id AND active = 1 
                              ORDER BY last_used DESC LIMIT 1');
            $this->db->bind(':user_id', $userId);
            $lastToken = $this->db->single();
            
            return [
                'active_tokens' => $activeTokens,
                'push_enabled' => $pushEnabled,
                'last_activity' => $lastToken ? $lastToken->last_used : null,
                'last_device' => $lastToken ? $lastToken->user_agent : null,
                'has_subscriptions' => $activeTokens > 0
            ];
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Get push subscription status error for user {$userId}: " . $e->getMessage());
            }
            
            return [
                'active_tokens' => 0,
                'push_enabled' => false,
                'last_activity' => null,
                'last_device' => null,
                'has_subscriptions' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Remove a specific FCM token for a user
     * 
     * @param int $userId User ID
     * @param string $fcmToken FCM token to remove
     * @return int Number of tokens removed
     */
    public function removeFCMToken($userId, $fcmToken) {
        try {
            $this->db->query('DELETE FROM fcm_tokens WHERE user_id = :user_id AND token = :token');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':token', $fcmToken);
            
            if ($this->db->execute()) {
                $rowCount = $this->db->rowCount();
                
                if (DEBUG_MODE && $rowCount > 0) {
                    error_log("[FCM] Removed {$rowCount} FCM token(s) for user {$userId}");
                }
                
                return $rowCount;
            }
            
            return 0;
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Remove FCM token error for user {$userId}: " . $e->getMessage());
            }
            return 0;
        }
    }
    
    /**
     * Remove all FCM tokens for a user
     * 
     * @param int $userId User ID
     * @return int Number of tokens removed
     */
    public function removeAllFCMTokens($userId) {
        try {
            $this->db->query('DELETE FROM fcm_tokens WHERE user_id = :user_id');
            $this->db->bind(':user_id', $userId);
            
            if ($this->db->execute()) {
                $rowCount = $this->db->rowCount();
                
                if (DEBUG_MODE && $rowCount > 0) {
                    error_log("[FCM] Removed all {$rowCount} FCM token(s) for user {$userId}");
                }
                
                return $rowCount;
            }
            
            return 0;
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[FCM] Remove all FCM tokens error for user {$userId}: " . $e->getMessage());
            }
            return 0;
        }
    }
}