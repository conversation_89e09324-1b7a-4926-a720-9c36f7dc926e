/**
 * Notification Center JavaScript
 * 
 * Handles notification center functionality including real-time updates,
 * badge counts, and user interactions.
 */

class NotificationCenter {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.updateInterval = 30000; // 30 seconds
        this.intervalId = null;
        this.isInitialized = false;
        
        this.init();
    }
    
    /**
     * Initialize the notification center
     */
    init() {
        if (this.isInitialized) {
            return;
        }
        
        // Only initialize if user is logged in
        if (!this.isUserLoggedIn()) {
            return;
        }
        
        // Preserve initial PHP-generated count for a moment, then update
        const initialBadge = document.getElementById('notification-count');
        const initialCount = initialBadge ? parseInt(initialBadge.getAttribute('data-count') || '0') : 0;

        if (initialCount > 0) {
            // If there's an initial count, wait a bit before updating to avoid flash
            setTimeout(() => {
                this.updateNotificationCount();
            }, 1000);
        } else {
            // If no initial count, update immediately
            this.updateNotificationCount();
        }

        this.startPeriodicUpdates();
        this.setupEventListeners();
        
        this.isInitialized = true;
        
        if (window.DEBUG_MODE) {
            console.log('[NotificationCenter] Initialized');
        }
    }
    
    /**
     * Check if user is logged in
     */
    isUserLoggedIn() {
        // Check for session indicators
        return document.querySelector('#notification-count') !== null ||
               document.querySelector('.racing-notification') !== null;
    }
    
    /**
     * Update notification count in header
     */
    async updateNotificationCount() {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/getUnreadCount`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.updateBadge(data.total_unread);
                
                // Update tab counts if we're on the notification center page
                if (data.conversationCounts && window.location.pathname.includes('notification_center')) {
                    const allTabCount = document.getElementById('all-tab-count');
                    const unreadTabCount = document.getElementById('unread-tab-count');
                    const archivedTabCount = document.getElementById('archived-tab-count');
                    
                    if (allTabCount) allTabCount.textContent = data.conversationCounts.all;
                    if (unreadTabCount) unreadTabCount.textContent = data.conversationCounts.unread;
                    if (archivedTabCount) archivedTabCount.textContent = data.conversationCounts.archived;
                }
                
                // Trigger custom event for other components
                window.dispatchEvent(new CustomEvent('notificationCountUpdated', {
                    detail: {
                        totalUnread: data.total_unread,
                        counts: data.counts,
                        conversationCounts: data.conversationCounts
                    }
                }));
            }
            
        } catch (error) {
            if (window.DEBUG_MODE) {
                console.error('[NotificationCenter] Failed to update count:', error);
            }
        }
    }
    
    /**
     * Update the notification badge
     */
    updateBadge(count) {
        // Update desktop header badge
        const badge = document.getElementById('notification-count');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count.toString();
                badge.style.display = 'inline-block';
                badge.setAttribute('data-count', count);

                // Add pulse animation for new notifications
                badge.classList.add('badge-pulse');
                setTimeout(() => {
                    badge.classList.remove('badge-pulse');
                }, 1000);
            } else {
                badge.style.display = 'none';
                badge.setAttribute('data-count', '0');
            }
        }

        // Update mobile navigation badge and bell icon
        const mobileBadge = document.getElementById('mobile-notification-badge');
        const mobileBellIcon = document.querySelector('#mobile-messages-link .fas.fa-bell');

        if (mobileBadge) {
            if (count > 0) {
                mobileBadge.textContent = count > 99 ? '99+' : count.toString();
                mobileBadge.style.display = 'flex';

                // Turn mobile bell icon bright yellow when there are unread messages
                if (mobileBellIcon) {
                    mobileBellIcon.style.color = '#FFD700'; // Bright yellow
                    mobileBellIcon.style.textShadow = '0 0 8px rgba(255, 215, 0, 0.6)';
                }
            } else {
                mobileBadge.style.display = 'none';

                // Reset mobile bell icon to normal color
                if (mobileBellIcon) {
                    mobileBellIcon.style.color = '';
                    mobileBellIcon.style.textShadow = '';
                }
            }
        }
    }
    
    /**
     * Start periodic updates
     */
    startPeriodicUpdates() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        
        this.intervalId = setInterval(() => {
            this.updateNotificationCount();
        }, this.updateInterval);
    }
    
    /**
     * Stop periodic updates
     */
    stopPeriodicUpdates() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPeriodicUpdates();
            } else {
                this.updateNotificationCount();
                this.startPeriodicUpdates();
            }
        });
        
        // Listen for focus/blur events
        window.addEventListener('focus', () => {
            this.updateNotificationCount();
        });
        
        // Listen for custom events from other parts of the app
        window.addEventListener('notificationSent', () => {
            // Update count when a notification is sent
            setTimeout(() => {
                this.updateNotificationCount();
            }, 1000);
        });
        
        window.addEventListener('notificationRead', () => {
            // Update count when a notification is read
            setTimeout(() => {
                this.updateNotificationCount();
            }, 500);
        });
    }
    
    /**
     * Mark a notification as read
     */
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/markRead`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `notification_id=${notificationId}`
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.updateBadge(data.counts.total_unread);
                
                // Trigger custom event
                window.dispatchEvent(new CustomEvent('notificationRead', {
                    detail: { notificationId, counts: data.counts }
                }));
                
                return true;
            } else {
                throw new Error(data.message || 'Failed to mark as read');
            }
            
        } catch (error) {
            if (window.DEBUG_MODE) {
                console.error('[NotificationCenter] Failed to mark as read:', error);
            }
            throw error;
        }
    }
    
    /**
     * Mark all notifications as read
     */
    async markAllAsRead(type = 'all') {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/markAllRead`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=${type}`
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.updateBadge(data.counts.total_unread);
                
                // Trigger custom event
                window.dispatchEvent(new CustomEvent('allNotificationsRead', {
                    detail: { type, counts: data.counts }
                }));
                
                return true;
            } else {
                throw new Error(data.message || 'Failed to mark all as read');
            }
            
        } catch (error) {
            if (window.DEBUG_MODE) {
                console.error('[NotificationCenter] Failed to mark all as read:', error);
            }
            throw error;
        }
    }
    
    /**
     * Archive a notification
     */
    async archiveNotification(notificationId) {
        try {
            const response = await fetch(`${this.baseUrl}/notification_center/archive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `notification_id=${notificationId}`
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                // Trigger custom event
                window.dispatchEvent(new CustomEvent('notificationArchived', {
                    detail: { notificationId }
                }));
                
                return true;
            } else {
                throw new Error(data.message || 'Failed to archive notification');
            }
            
        } catch (error) {
            if (window.DEBUG_MODE) {
                console.error('[NotificationCenter] Failed to archive:', error);
            }
            throw error;
        }
    }
    
    /**
     * Destroy the notification center
     */
    destroy() {
        this.stopPeriodicUpdates();
        this.isInitialized = false;
    }
}

// Global notification center instance
let notificationCenter = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    notificationCenter = new NotificationCenter();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (notificationCenter) {
        notificationCenter.destroy();
    }
});

// Export for global access
window.NotificationCenter = NotificationCenter;
window.notificationCenter = notificationCenter;
