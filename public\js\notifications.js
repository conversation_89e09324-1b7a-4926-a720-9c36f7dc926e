/**
 * Notification System JavaScript v3.49.14
 * 
 * This file handles all client-side notification functionality including
 * subscription management, toast notifications, and push notifications.
 * 
 * Location: /public/js/notifications.js
 * Dependencies: Bootstrap 5, Font Awesome
 * 
 * Fixed Issues:
 * - Fixed originalText undefined error in openSubscriptionModal
 * - Added global modal functions (subscribeToEventModal, unsubscribeFromEventModal)
 * - Improved error handling and button state management
 * - Fixed modal backdrop not being removed when modal is cancelled (v3.49.9)
 * - Added proper aria-hidden attribute management to prevent focus conflicts
 * - Added comprehensive modal cleanup functionality
 * - Added periodic cleanup check for orphaned modal backdrops
 * - Fixed DOM element null reference error by adding page reload after subscription changes (v3.49.12)
 * - Enhanced mobile support with touch events and better responsive handling (v3.49.13)
 * - Improved modal layout and button functionality on mobile devices
 */

// Prevent duplicate loading
if (typeof window.NotificationManager !== 'undefined') {
    console.warn('NotificationManager already loaded, skipping duplicate initialization');
} else {

class NotificationManager {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        this.debugMode = localStorage.getItem('notification_debug') === 'true' ||
                         document.body.dataset.debugMode === 'true' ||
                         window.location.search.includes('debug=1');
        this.isMobile = this.detectMobile();
        this.touchStartY = 0;
        this.touchEndY = 0;

        if (this.debugMode) {
            console.log('[NotificationManager] Constructor called');
            console.log('[NotificationManager] Base URL:', this.baseUrl);
            console.log('[NotificationManager] CSRF Token:', this.csrfToken ? 'Present' : 'Missing');
            console.log('[NotificationManager] Is Mobile:', this.isMobile);
        }

        this.init();
    }
    
    /**
     * Detect if device is mobile
     */
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768 ||
               ('ontouchstart' in window);
    }
    
    /**
     * Initialize the notification manager
     */
    init() {
        this.dismissedToasts = new Set(); // Track dismissed toasts to prevent re-showing

        if (this.debugMode) {
            console.log('[NotificationManager] Initializing notification system...');
        }

        this.setupEventListeners();
        this.loadUnreadNotifications();
        this.requestPushPermission();

        // Setup global modal functions immediately
        this.setupModalFunctions();

        // Check for unread notifications every 30 seconds
        const pollingInterval = setInterval(() => {
            this.loadUnreadNotifications();
        }, 30000);

        if (this.debugMode) {
            console.log('[NotificationManager] Polling started - checking every 30 seconds');
            console.log('[NotificationManager] Polling interval ID:', pollingInterval);
        }
        
        // Add a global cleanup check every 5 seconds to catch any lingering modal issues
        setInterval(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            const openModals = document.querySelectorAll('.modal.show');
            
            // If there are backdrops but no open modals, clean up
            if (backdrops.length > 0 && openModals.length === 0) {
                console.log('Cleaning up orphaned modal backdrops');
                this.cleanupModalState();
            }
        }, 5000);
        
        // Mark all visible toasts as read when page is about to unload
        window.addEventListener('beforeunload', () => {
            this.markAllVisibleToastsAsRead();
        });
        
        // Add keyboard shortcut to clear all toasts (Escape key)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const visibleToasts = document.querySelectorAll('.toast-notification[data-notification-id]');
                if (visibleToasts.length > 0) {
                    this.clearAllToasts();
                }
                
                // Also clean up any modal state issues
                setTimeout(() => {
                    this.cleanupModalState();
                }, 100);
            }
        });
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Notification subscription buttons - Enhanced for mobile
        const handleNotificationButton = (e) => {
            if (e.target.matches('[data-notification-btn]') || e.target.closest('[data-notification-btn]')) {
                e.preventDefault();
                e.stopPropagation();
                const btn = e.target.matches('[data-notification-btn]') ? e.target : e.target.closest('[data-notification-btn]');
                
                // Add visual feedback for mobile
                if (this.isMobile) {
                    btn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        btn.style.transform = '';
                    }, 150);
                }
                
                this.openSubscriptionModal(btn);
            }
        };
        
        // Add both click and touch events for better mobile support
        document.addEventListener('click', handleNotificationButton);
        if (this.isMobile) {
            document.addEventListener('touchend', handleNotificationButton);
        }
        
        // Toast notification close buttons - Enhanced for mobile
        const handleToastClose = (e) => {
            if (e.target.matches('.btn-close') || e.target.matches('.toast-close')) {
                e.preventDefault();
                e.stopPropagation();
                
                const toast = e.target.closest('.toast-notification');
                if (toast) {
                    console.log('🔴 Close button clicked for toast:', toast.getAttribute('data-notification-id'));
                    this.dismissToast(toast);
                }
            }
            
            // Check for modal backdrop clicks
            if (e.target.matches('.modal-backdrop')) {
                // Clean up modal state after backdrop click
                setTimeout(() => {
                    this.cleanupModalState();
                }, 100);
            }
        };
        
        document.addEventListener('click', handleToastClose);
        if (this.isMobile) {
            document.addEventListener('touchend', handleToastClose);
        }
        
        // Add touch event handling for mobile swipe gestures on modals
        if (this.isMobile) {
            document.addEventListener('touchstart', (e) => {
                if (e.target.closest('.modal-content')) {
                    this.touchStartY = e.touches[0].clientY;
                }
            }, { passive: true });
            
            document.addEventListener('touchend', (e) => {
                if (e.target.closest('.modal-content')) {
                    this.touchEndY = e.changedTouches[0].clientY;
                    this.handleModalSwipe();
                }
            }, { passive: true });
        }
    }
    
    /**
     * Handle modal swipe gestures on mobile
     */
    handleModalSwipe() {
        const swipeDistance = this.touchStartY - this.touchEndY;
        const minSwipeDistance = 100;
        
        // If swiped down significantly, close modal
        if (swipeDistance < -minSwipeDistance) {
            const modal = document.getElementById('notificationModal');
            if (modal && modal.classList.contains('show')) {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            }
        }
    }
    
    /**
     * Open notification subscription modal
     */
    async openSubscriptionModal(button) {
        const eventId = button.dataset.eventId;
        const eventType = button.dataset.eventType;
        
        if (!eventId || !eventType) {
            this.showAlert('error', 'Invalid event data');
            return;
        }
        
        // Store original button state before try block
        const originalText = button.innerHTML;
        const originalDisabled = button.disabled;
        
        try {
            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
            button.disabled = true;
            
            const response = await fetch(`${this.baseUrl}/notification/subscriptionModal?event_id=${eventId}&event_type=${eventType}`);
            const data = await response.json();
            
            if (data.success) {
                // Create or update modal
                let modal = document.getElementById('notificationModal');
                if (!modal) {
                    modal = document.createElement('div');
                    modal.className = 'modal fade';
                    modal.id = 'notificationModal';
                    modal.setAttribute('tabindex', '-1');
                    modal.setAttribute('aria-labelledby', 'notificationModalLabel');
                    // Don't set aria-hidden initially to prevent focus conflicts
                    document.body.appendChild(modal);
                }
                
                // Adjust modal size for mobile
                const modalSize = this.isMobile ? 'modal-fullscreen-sm-down' : 'modal-lg';
                
                modal.innerHTML = `
                    <div class="modal-dialog ${modalSize}">
                        <div class="modal-content">
                            ${data.html}
                        </div>
                    </div>
                `;
                
                // Show modal
                const bsModal = new bootstrap.Modal(modal);
                
                // Add event listeners for proper cleanup
                modal.addEventListener('hidden.bs.modal', () => {
                    // Use the centralized cleanup function
                    this.cleanupModalState();
                }, { once: true });
                
                // Handle escape key and backdrop clicks properly
                modal.addEventListener('hide.bs.modal', (e) => {
                    // Ensure proper cleanup even if modal is cancelled
                    console.log('Modal hiding...');
                });
                
                bsModal.show();
                
                // Execute any scripts in the modal content after it's shown
                modal.addEventListener('shown.bs.modal', () => {
                    // Remove aria-hidden when modal is fully shown to prevent focus conflicts
                    modal.removeAttribute('aria-hidden');
                    
                    // Trigger any initialization scripts in the modal
                    const scripts = modal.querySelectorAll('script');
                    scripts.forEach(script => {
                        if (script.innerHTML) {
                            try {
                                eval(script.innerHTML);
                            } catch (e) {
                                console.warn('Error executing modal script:', e);
                            }
                        }
                    });
                }, { once: true });
                
            } else {
                this.showAlert('error', data.message || 'Failed to load subscription modal');
            }
        } catch (error) {
            console.error('Error loading subscription modal:', error);
            this.showAlert('error', 'An error occurred while loading the subscription modal');
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = originalDisabled;
        }
    }
    
    /**
     * Clean up any lingering modal backdrops and states
     */
    cleanupModalState() {
        // Remove any lingering backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });
        
        // Remove modal-open class from body
        document.body.classList.remove('modal-open');
        document.body.style.paddingRight = '';
        document.body.style.overflow = '';
        
        // Remove any orphaned modals
        const orphanedModals = document.querySelectorAll('.modal:not(.show)');
        orphanedModals.forEach(modal => {
            if (modal.id === 'notificationModal' && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        });
    }
    
    /**
     * Setup global functions for modal functionality
     */
    setupModalFunctions() {
        const self = this;
        
        // Make subscribeToEventModal globally available
        window.subscribeToEventModal = function(e) {
            // Handle both click and touch events
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            const form = document.getElementById('notificationSubscriptionForm');
            if (!form) {
                console.error('Notification subscription form not found');
                return;
            }
            
            const formData = new FormData(form);
            
            // Validate that at least one notification time is selected
            const notificationTimes = formData.getAll('notification_times[]');
            if (notificationTimes.length === 0) {
                if (self.isMobile) {
                    // Use native mobile alert for better UX
                    alert('Please select at least one notification time.');
                } else {
                    alert('Please select at least one notification time.');
                }
                return;
            }
            
            // If registration notifications are enabled, validate those times too
            const notifyRegistration = document.getElementById('notify_registration_end');
            if (notifyRegistration && notifyRegistration.checked) {
                const registrationTimes = formData.getAll('registration_times[]');
                if (registrationTimes.length === 0) {
                    if (self.isMobile) {
                        alert('Please select at least one registration deadline notification time.');
                    } else {
                        alert('Please select at least one registration deadline notification time.');
                    }
                    return;
                }
            }
            
            // Show loading state
            const button = e ? e.target : event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Subscribing...';
            button.disabled = true;
            
            // Add visual feedback for mobile
            if (self.isMobile) {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }
            
            fetch(`${window.notificationManager.baseUrl}/notification/subscribe`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal and show success message
                    const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
                    if (modal) {
                        modal.hide();
                        // Ensure cleanup after modal is hidden
                        setTimeout(() => {
                            window.notificationManager.cleanupModalState();
                        }, 300);
                    }
                    
                    // Show success toast or alert
                    if (typeof showToast === 'function') {
                        showToast('success', data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    // Reload page to ensure consistent state and prevent DOM element issues
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500); // Give time for success message to be seen
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while subscribing to notifications.');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        };
        
        // Make unsubscribeFromEventModal globally available
        window.unsubscribeFromEventModal = function(e) {
            // Handle both click and touch events
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            if (!confirm('Are you sure you want to unsubscribe from notifications for this event?')) {
                return;
            }
            
            const form = document.getElementById('notificationSubscriptionForm');
            if (!form) {
                console.error('Notification subscription form not found');
                return;
            }
            
            const formData = new FormData(form);
            
            // Show loading state
            const button = e ? e.target : event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Unsubscribing...';
            button.disabled = true;
            
            // Add visual feedback for mobile
            if (self.isMobile) {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }
            
            fetch(`${window.notificationManager.baseUrl}/notification/unsubscribe`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal and show success message
                    const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
                    if (modal) {
                        modal.hide();
                        // Ensure cleanup after modal is hidden
                        setTimeout(() => {
                            window.notificationManager.cleanupModalState();
                        }, 300);
                    }
                    
                    // Show success toast or alert
                    if (typeof showToast === 'function') {
                        showToast('success', data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    // Reload page to ensure consistent state and prevent DOM element issues
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500); // Give time for success message to be seen
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while unsubscribing.');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        };
        
        // Make updateNotificationButtonState globally available
        window.updateNotificationButtonState = function(isSubscribed) {
            const notificationBtn = document.querySelector('[data-notification-btn]');
            if (notificationBtn) {
                if (isSubscribed) {
                    notificationBtn.classList.remove('btn-outline-primary');
                    notificationBtn.classList.add('btn-primary');
                    notificationBtn.innerHTML = '<i class="fas fa-bell me-2"></i>Subscribed';
                    notificationBtn.title = 'Manage event notifications';
                } else {
                    notificationBtn.classList.remove('btn-primary');
                    notificationBtn.classList.add('btn-outline-primary');
                    notificationBtn.innerHTML = '<i class="fas fa-bell me-2"></i>Notify Me';
                    notificationBtn.title = 'Set up event notifications';
                }
            }
        };
        
        // Add mobile-specific debugging and event handling
        if (this.isMobile) {
            this.setupMobileDebugging();
        }
    }
    
    /**
     * Load unread notifications
     */
    async loadUnreadNotifications() {
        try {
            if (this.debugMode) {
                console.log('[NotificationManager] Polling for unread notifications...');
            }

            // Get actual notifications (not just counts) - using original working endpoint
            const response = await fetch(`${this.baseUrl}/notification/getUnread`);
            const data = await response.json();

            if (this.debugMode) {
                console.log('[NotificationManager] Received notifications:', {
                    push_count: data.push ? data.push.length : 0,
                    toast_count: data.toast ? data.toast.length : 0,
                    toast_notifications: data.toast,
                    push_notifications: data.push,
                    raw_response: data
                });
            }

            // Show toast notifications if any
            if (data.toast && data.toast.length > 0) {
                this.showToastNotifications(data.toast);
            }

            // Send push notifications if any
            if (this.debugMode) {
                console.log('[NotificationManager] Checking push notifications:', {
                    has_push_property: 'push' in data,
                    push_is_array: Array.isArray(data.push),
                    push_length: data.push ? data.push.length : 'undefined',
                    push_data: data.push
                });
            }
            
            if (data.push && data.push.length > 0) {
                if (this.debugMode) {
                    console.log('[NotificationManager] About to call sendPushNotifications with', data.push.length, 'notifications');
                }
                this.sendPushNotifications(data.push);
            } else {
                if (this.debugMode) {
                    console.log('[NotificationManager] No push notifications to send');
                }
            }

            // Update notification badge with total count
            const totalCount = (data.push ? data.push.length : 0) + (data.toast ? data.toast.length : 0);
            this.updateNotificationBadge(totalCount);

        } catch (error) {
            console.error('[NotificationManager] Error loading unread notifications:', error);
        }
    }
    
    /**
     * Update notification badge in navigation
     */
    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    /**
     * Trigger server to send push notifications via FCM
     */
    async sendPushNotifications(notifications) {
        if (this.debugMode) {
            console.log('[NotificationManager] Triggering server to send', notifications.length, 'push notifications via FCM...');
        }
        
        // Extract notification IDs to send to server
        const notificationIds = notifications.map(n => n.id);
        
        try {
            // Tell the server to send these push notifications via FCM
            const formData = new FormData();
            formData.append('notification_ids', JSON.stringify(notificationIds));
            formData.append('csrf_token', this.csrfToken);
            
            if (this.debugMode) {
                console.log('[NotificationManager] CSRF Debug:', {
                    csrfToken: this.csrfToken,
                    csrfTokenLength: this.csrfToken.length,
                    metaTag: document.querySelector('meta[name="csrf-token"]'),
                    metaContent: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                });
            }
            
            const response = await fetch(`${this.baseUrl}/notification/sendPush`, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (this.debugMode) {
                console.log('[NotificationManager] Server push response:', result);
            }
            
            if (result.success) {
                if (this.debugMode) {
                    console.log('[NotificationManager] Server successfully sent', result.sent_count || notificationIds.length, 'push notifications');
                }
                
                // Mark notifications as sent/read
                await this.markNotificationsRead('push', notificationIds);
                
            } else {
                console.error('[NotificationManager] Server failed to send push notifications:', result.message);
            }
            
        } catch (error) {
            console.error('[NotificationManager] Error triggering server push notifications:', error);
        }
    }
    
    /**
     * Show toast notifications
     */
    showToastNotifications(notifications) {
        const container = this.getToastContainer();
        
        notifications.forEach(notification => {
            // Check if toast already exists or was dismissed
            if (document.querySelector(`[data-notification-id="${notification.id}"]`) || 
                this.dismissedToasts.has(notification.id)) {
                return;
            }
            
            const toast = this.createToastElement(notification);
            container.appendChild(toast);
            
            // Auto-dismiss after 8 seconds (reduced from 10)
            setTimeout(() => {
                if (!this.dismissedToasts.has(notification.id)) {
                    this.dismissToast(toast);
                }
            }, 8000);
        });
        
        // Show/hide clear all button based on visible toasts
        this.updateClearAllButton();
    }
    
    /**
     * Get or create toast container
     */
    getToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '99999';
            container.style.pointerEvents = 'none';
            document.body.appendChild(container);
            
            // Add clear all button (initially hidden)
            const clearAllBtn = document.createElement('button');
            clearAllBtn.id = 'clear-all-toasts';
            clearAllBtn.className = 'btn btn-sm btn-outline-secondary mb-2';
            clearAllBtn.style.display = 'none';
            clearAllBtn.innerHTML = '<i class="fas fa-times me-1"></i>Clear All';
            clearAllBtn.onclick = (e) => {
                e.preventDefault();
                console.log('🔴 Clear All button clicked');
                this.clearAllToasts();
            };
            clearAllBtn.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🔵 Clear All addEventListener triggered');
                this.clearAllToasts();
            });
            container.appendChild(clearAllBtn);
        }
        return container;
    }
    
    /**
     * Create toast element
     */
    createToastElement(notification) {
        const toast = document.createElement('div');
        toast.className = 'toast-notification alert alert-info alert-dismissible fade show';
        toast.setAttribute('data-notification-id', notification.id);
        toast.style.minWidth = '300px';
        toast.style.marginBottom = '10px';
        toast.style.pointerEvents = 'auto';
        toast.style.zIndex = '100000';
        
        const eventIcon = notification.event_type === 'car_show' ? 'fa-car' : 'fa-calendar';
        
        toast.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="fas ${eventIcon} me-3 mt-1 text-primary"></i>
                <div class="flex-grow-1">
                    <h6 class="alert-heading mb-1">${this.escapeHtml(notification.title)}</h6>
                    <p class="mb-0 small">${this.escapeHtml(notification.message).substring(0, 100)}${notification.message.length > 100 ? '...' : ''}</p>
                    <small class="text-muted">${this.formatTimeAgo(notification.created_at)}</small>
                </div>
                <button type="button" class="btn-close toast-close" aria-label="Close" data-toast-id="${notification.id}"></button>
            </div>
        `;
        
        // Add multiple event handlers to ensure it works
        const closeButton = toast.querySelector('.btn-close');
        if (closeButton) {
            // Method 1: onclick
            closeButton.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🟢 onclick handler for toast:', notification.id);
                this.dismissToast(toast);
                return false;
            };
            
            // Method 2: addEventListener
            closeButton.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔵 addEventListener handler for toast:', notification.id);
                this.dismissToast(toast);
            });
            
            // Method 3: mousedown as backup
            closeButton.addEventListener('mousedown', (e) => {
                e.preventDefault();
                console.log('🟡 mousedown handler for toast:', notification.id);
            });
        }
        
        return toast;
    }
    
    /**
     * Dismiss toast notification
     */
    async dismissToast(toast) {
        if (!toast) {
            console.error('dismissToast called with null/undefined toast');
            return;
        }
        
        const notificationId = toast.getAttribute('data-notification-id');
        console.log('🗑️ Dismissing toast notification:', notificationId);
        
        if (notificationId) {
            // Add to dismissed set to prevent re-showing
            this.dismissedToasts.add(parseInt(notificationId));
        }
        
        // Animate out
        toast.classList.remove('show');
        toast.classList.add('fade-out');
        
        // Remove the toast after animation
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
                console.log('✅ Toast removed from DOM:', notificationId);
            }
            // Update clear all button after toast is removed
            this.updateClearAllButton();
        }, 150);
        
        // Mark as read on server
        if (notificationId) {
            try {
                await this.markNotificationsRead('toast', [notificationId]);
                console.log('📤 Toast marked as read on server:', notificationId);
            } catch (error) {
                console.error('❌ Error marking toast as read:', error);
            }
        }
    }
    
    /**
     * Mark notifications as read
     */
    async markNotificationsRead(type, notificationIds) {
        const formData = new FormData();
        formData.append('type', type);
        formData.append('notification_ids', JSON.stringify(notificationIds));
        formData.append('csrf_token', this.csrfToken);
        
        const response = await fetch(`${this.baseUrl}/notification/markRead`, {
            method: 'POST',
            body: formData
        });
        
        return response.json();
    }
    
    /**
     * Mark all visible toast notifications as read (called on page unload)
     */
    markAllVisibleToastsAsRead() {
        const visibleToasts = document.querySelectorAll('.toast-notification[data-notification-id]');
        const notificationIds = [];
        
        visibleToasts.forEach(toast => {
            const id = toast.getAttribute('data-notification-id');
            if (id) {
                notificationIds.push(parseInt(id));
                this.dismissedToasts.add(parseInt(id));
            }
        });
        
        if (notificationIds.length > 0) {
            // Use sendBeacon for reliable delivery during page unload
            const formData = new FormData();
            formData.append('type', 'toast');
            formData.append('notification_ids', JSON.stringify(notificationIds));
            formData.append('csrf_token', this.csrfToken);
            
            navigator.sendBeacon(`${this.baseUrl}/notification/markRead`, formData);
        }
    }
    
    /**
     * Update clear all button visibility
     */
    updateClearAllButton() {
        const clearAllBtn = document.getElementById('clear-all-toasts');
        const visibleToasts = document.querySelectorAll('.toast-notification[data-notification-id]');
        
        if (clearAllBtn) {
            clearAllBtn.style.display = visibleToasts.length > 1 ? 'block' : 'none';
        }
    }
    
    /**
     * Clear all visible toast notifications
     */
    async clearAllToasts() {
        const visibleToasts = document.querySelectorAll('.toast-notification[data-notification-id]');
        const notificationIds = [];
        
        visibleToasts.forEach(toast => {
            const id = toast.getAttribute('data-notification-id');
            if (id) {
                notificationIds.push(parseInt(id));
                this.dismissedToasts.add(parseInt(id));
            }
            
            // Animate out
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 150);
        });
        
        // Hide clear all button
        this.updateClearAllButton();
        
        // Mark all as read on server
        if (notificationIds.length > 0) {
            try {
                await this.markNotificationsRead('toast', notificationIds);
            } catch (error) {
                console.error('Error marking all toasts as read:', error);
            }
        }
    }
    
    /**
     * Request push notification permission
     */
    async requestPushPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            try {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    console.log('Push notifications enabled');
                }
            } catch (error) {
                console.error('Error requesting push permission:', error);
            }
        }
    }
    
    /**
     * Show browser push notification
     */
    showPushNotification(title, message, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: `${this.baseUrl}/public/images/notification-icon.png`,
                badge: `${this.baseUrl}/public/images/notification-badge.png`,
                ...options
            });
            
            // Auto-close after 10 seconds
            setTimeout(() => {
                notification.close();
            }, 10000);
            
            return notification;
        }
    }
    
    /**
     * Show alert message
     */
    showAlert(type, message) {
        // Try to use existing toast system if available
        if (typeof showToast === 'function') {
            showToast(type, message);
            return;
        }
        
        // Fallback to browser alert
        alert(message);
    }
    
    /**
     * Get CSRF token name
     */
    getCsrfTokenName() {
        return document.querySelector('meta[name="csrf-token-name"]')?.getAttribute('content') || 'csrf_token';
    }
    
    /**
     * Escape HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * Format time ago
     */
    formatTimeAgo(dateString) {
        // Handle null, undefined, or empty dateString
        if (!dateString || dateString === null || dateString === undefined || dateString === '') {
            return 'Unknown time';
        }

        // Convert UTC database timestamp to local timezone
        let date;
        try {
            if (window.TimezoneHelper && window.TimezoneHelper.parseMySQLDateTime) {
                date = window.TimezoneHelper.parseMySQLDateTime(dateString);
            } else {
                // Fallback: assume UTC and convert to local
                // Handle both 'YYYY-MM-DD HH:MM:SS' and 'YYYY-MM-DDTHH:MM:SSZ' formats
                if (typeof dateString === 'string') {
                    if (dateString.includes('T')) {
                        date = new Date(dateString);
                    } else {
                        // MySQL datetime format: add 'T' and 'Z' for proper UTC parsing
                        date = new Date(dateString.replace(' ', 'T') + 'Z');
                    }
                } else {
                    date = new Date(dateString);
                }
            }

            // Check if date is valid
            if (isNaN(date.getTime())) {
                console.warn('Invalid date in formatTimeAgo:', dateString);
                return 'Invalid date';
            }

        } catch (error) {
            console.error('Error parsing date in formatTimeAgo:', dateString, error);
            return 'Invalid date';
        }

        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        // Handle negative differences (future dates)
        if (diffInSeconds < 0) {
            return 'Just now';
        }

        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days !== 1 ? 's' : ''} ago`;
        }
    }
    
    /**
     * Debug logging method
     */
    debugLog(message, data = null) {
        if (this.debugMode) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[NotificationManager ${timestamp}] ${message}`, data || '');
        }
    }
    
    /**
     * Toggle debug mode
     */
    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        localStorage.setItem('notification_debug', this.debugMode.toString());
        console.log(`Notification debug mode ${this.debugMode ? 'enabled' : 'disabled'}`);
        return this.debugMode;
    }
    
    /**
     * Setup mobile-specific debugging and event handling
     */
    setupMobileDebugging() {
        console.log('🔧 Mobile notification debugging enabled');
        
        // Add visual feedback for all button interactions on mobile
        document.addEventListener('touchstart', (e) => {
            if (e.target.matches('.btn') || e.target.closest('.btn')) {
                const btn = e.target.matches('.btn') ? e.target : e.target.closest('.btn');
                btn.style.opacity = '0.7';
            }
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            if (e.target.matches('.btn') || e.target.closest('.btn')) {
                const btn = e.target.matches('.btn') ? e.target : e.target.closest('.btn');
                setTimeout(() => {
                    btn.style.opacity = '';
                }, 150);
            }
        }, { passive: true });
        
        // Log all modal-related events for debugging
        document.addEventListener('click', (e) => {
            if (e.target.closest('.modal-footer')) {
                console.log('🔍 Modal footer click detected:', e.target);
            }
        });
        
        // Add fallback event handlers for modal buttons
        const self = this;
        const setupFallbacks = () => {
            setTimeout(() => {
                self.setupMobileFallbackHandlers();
            }, 1000);
        };
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupFallbacks);
        } else {
            setupFallbacks();
        }
    }
    
    /**
     * Setup fallback event handlers for mobile devices
     */
    setupMobileFallbackHandlers() {
        // Find and enhance modal buttons with direct event listeners
        const modal = document.getElementById('notificationModal');
        if (modal) {
            const subscribeBtn = modal.querySelector('.btn-primary');
            const unsubscribeBtn = modal.querySelector('.btn-danger');
            
            if (subscribeBtn) {
                subscribeBtn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔧 Mobile subscribe button touched');
                    if (typeof window.subscribeToEventModal === 'function') {
                        window.subscribeToEventModal(e);
                    }
                }, { passive: false });
                
                // Also add a click handler as backup
                subscribeBtn.addEventListener('click', (e) => {
                    console.log('🔧 Mobile subscribe button clicked');
                }, { passive: false });
            }
            
            if (unsubscribeBtn) {
                unsubscribeBtn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔧 Mobile unsubscribe button touched');
                    if (typeof window.unsubscribeFromEventModal === 'function') {
                        window.unsubscribeFromEventModal(e);
                    }
                }, { passive: false });
                
                // Also add a click handler as backup
                unsubscribeBtn.addEventListener('click', (e) => {
                    console.log('🔧 Mobile unsubscribe button clicked');
                }, { passive: false });
            }
        }
    }
}

// Initialize notification manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('[NotificationManager] DOM ready, initializing...');
    try {
        window.notificationManager = new NotificationManager();
        console.log('[NotificationManager] Successfully initialized');
    } catch (error) {
        console.error('[NotificationManager] Failed to initialize:', error);
    }
});

// Global functions for backward compatibility
function openNotificationModal(eventId, eventType) {
    if (window.notificationManager) {
        const button = document.createElement('button');
        button.dataset.eventId = eventId;
        button.dataset.eventType = eventType;
        window.notificationManager.openSubscriptionModal(button);
    }
}

function showToast(type, message) {
    if (window.notificationManager) {
        const notification = {
            id: Date.now(),
            title: type.charAt(0).toUpperCase() + type.slice(1),
            message: message,
            created_at: new Date().toISOString(),
            event_type: 'system'
        };
        window.notificationManager.showToastNotifications([notification]);
    }
}

// Make NotificationManager available globally
window.NotificationManager = NotificationManager;

} // End duplicate loading prevention