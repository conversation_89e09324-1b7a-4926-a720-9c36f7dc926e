<?php
// Ensure variables are defined with fallback values
$is_subscribed = $is_subscribed ?? false;
$event_type = $event_type ?? 'calendar_event';
$event = $event ?? (object)['title' => 'Unknown Event', 'start_date' => gmdate('Y-m-d H:i:s')];
$csrf_token = $csrf_token ?? '';

// Debug logging
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_log("Modal variables - event_type: $event_type, is_subscribed: " . ($is_subscribed ? 'true' : 'false'));
}
?>
<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-bell me-2"></i>
        <?php echo $is_subscribed ? 'Update' : 'Subscribe to'; ?> Notifications
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>

<div class="modal-body notification-modal-body">
    <div class="container-fluid px-0">
        <div class="mb-4">
            <h6 class="text-primary">
                <i class="fas fa-<?php echo $event_type === 'car_show' ? 'car' : 'calendar'; ?> me-2"></i>
                <?php echo htmlspecialchars($event->title); ?>
            </h6>
            <p class="text-muted mb-0">
                <i class="fas fa-clock me-2"></i>
                <?php echo formatDateTimeForUser($event->start_date, $_SESSION['user_id'] ?? null, 'F j, Y \a\t g:i A'); ?>
            </p>
            <?php if ($event_type === 'car_show' && !empty($event->registration_end)): ?>
                <p class="text-muted mb-0">
                    <i class="fas fa-calendar-times me-2"></i>
                    Registration ends: <?php echo formatDateTimeForUser($event->registration_end, $_SESSION['user_id'] ?? null, 'F j, Y \a\t g:i A'); ?>
                </p>
            <?php endif; ?>
        </div>

        <form id="notificationSubscriptionForm">
            <input type="hidden" name="event_id" value="<?php echo $event->id; ?>">
            <input type="hidden" name="event_type" value="<?php echo $event_type; ?>">
            <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
            
            <div class="mb-4">
                <label class="form-label fw-bold mb-3">
                    <i class="fas fa-clock me-2"></i>When would you like to be notified?
                </label>
                <div class="row g-3">
                    <div class="col-12 col-md-6">
                        <div class="notification-times-column">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="15" id="time_15">
                                <label class="form-check-label" for="time_15">15 minutes before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="30" id="time_30">
                                <label class="form-check-label" for="time_30">30 minutes before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="60" id="time_60" checked>
                                <label class="form-check-label" for="time_60">1 hour before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="120" id="time_120">
                                <label class="form-check-label" for="time_120">2 hours before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="360" id="time_360">
                                <label class="form-check-label" for="time_360">6 hours before</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="notification-times-column">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="720" id="time_720">
                                <label class="form-check-label" for="time_720">12 hours before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="1440" id="time_1440" checked>
                                <label class="form-check-label" for="time_1440">1 day before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="2880" id="time_2880">
                                <label class="form-check-label" for="time_2880">2 days before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="4320" id="time_4320">
                                <label class="form-check-label" for="time_4320">3 days before</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="notification_times[]" value="10080" id="time_10080">
                                <label class="form-check-label" for="time_10080">1 week before</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($event_type === 'car_show' && !empty($event->registration_end)): ?>
                <div class="mb-4">
                    <div class="form-check form-switch mb-3" id="notify_registration_end_container">
                        <input class="form-check-input" type="checkbox" name="notify_registration_end" id="notify_registration_end">
                        <label class="form-check-label fw-bold" for="notify_registration_end">
                            <i class="fas fa-calendar-times me-2"></i>
                            Also notify me about registration deadline
                        </label>
                    </div>
                    
                    <div id="registrationTimesContainer" class="mt-3" style="display: none;">
                        <label class="form-label mb-3">Registration deadline reminders:</label>
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <div class="notification-times-column">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="registration_times[]" value="60" id="reg_time_60">
                                        <label class="form-check-label" for="reg_time_60">1 hour before</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="registration_times[]" value="360" id="reg_time_360">
                                        <label class="form-check-label" for="reg_time_360">6 hours before</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="registration_times[]" value="1440" id="reg_time_1440" checked>
                                        <label class="form-check-label" for="reg_time_1440">1 day before</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-6">
                                <div class="notification-times-column">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="registration_times[]" value="2880" id="reg_time_2880">
                                        <label class="form-check-label" for="reg_time_2880">2 days before</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="registration_times[]" value="4320" id="reg_time_4320">
                                        <label class="form-check-label" for="reg_time_4320">3 days before</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="registration_times[]" value="10080" id="reg_time_10080">
                                        <label class="form-check-label" for="reg_time_10080">1 week before</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Note:</strong> Notifications will be sent according to your notification preferences. 
                You can manage these in your <a href="<?php echo BASE_URL; ?>/notification/preferences" target="_blank">notification settings</a>.
            </div>
        </form>
    </div>
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
    <?php if ($is_subscribed): ?>
        <button type="button" class="btn btn-danger" onclick="unsubscribeFromEventModal(event)" ontouchend="unsubscribeFromEventModal(event)">
            <i class="fas fa-bell-slash me-2"></i>Unsubscribe
        </button>
    <?php endif; ?>
    <button type="button" class="btn btn-primary" onclick="subscribeToEventModal(event)" ontouchend="subscribeToEventModal(event)">
        <i class="fas fa-bell me-2"></i><?php echo $is_subscribed ? 'Update' : 'Subscribe'; ?>
    </button>
</div>

<script>
// Initialize modal functionality immediately (no DOMContentLoaded needed for dynamic content)
(function() {
    // Toggle registration times visibility
    const notifyRegistrationEnd = document.getElementById('notify_registration_end');
    if (notifyRegistrationEnd) {
        notifyRegistrationEnd.addEventListener('change', function() {
            const container = document.getElementById('registrationTimesContainer');
            if (container) {
                container.style.display = this.checked ? 'block' : 'none';
            }
        });
    }
    
    // Debug: Check if global functions are available
    if (typeof window.subscribeToEventModal === 'undefined') {
        console.warn('Global notification functions not available, modal may not work properly');
    } else {
        console.log('✅ Global notification functions are available');
    }
    
    // Trigger FCM permission prompt when user tries to subscribe to event notifications
    // This is the perfect moment - they're actively trying to get notifications!
    if (window.notificationPermissionManager && Notification.permission === 'default') {
        // Dispatch event to trigger permission prompt
        document.dispatchEvent(new CustomEvent('eventSubscriptionAttempt'));
    }
})();
</script>