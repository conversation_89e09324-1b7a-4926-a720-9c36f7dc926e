<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="<?php echo BASE_URL; ?>/notification_center" class="btn btn-outline-secondary btn-sm me-3">
                        <i class="fas fa-arrow-left me-1"></i>Back to Messages
                    </a>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Conversation Thread -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <!-- Desktop: Show "Conversation:" -->
                            <h5 class="mb-0 d-none d-md-block">
                                <i class="fas fa-comments text-primary me-2"></i>
                                Conversation: <?php echo htmlspecialchars($message->subject); ?>
                            </h5>
                            <!-- Mobile: Just show subject -->
                            <h5 class="mb-0 d-md-none">
                                <i class="fas fa-comments text-primary me-2"></i>
                                <?php echo htmlspecialchars($message->subject); ?>
                            </h5>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-info"><?php echo count($messageThread); ?> message<?php echo count($messageThread) !== 1 ? 's' : ''; ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body p-0">
                    <!-- Thread Messages -->
                    <?php if (!empty($messageThread)): ?>
                        <?php foreach ($messageThread as $index => $threadMessage): ?>
                            <?php 
                            $isCurrentUser = ($threadMessage->from_user_id == $currentUserId);
                            $isOriginal = ($index === 0);
                            $isReply = !$isOriginal;
                            ?>
                            
                            <div class="message-item <?php echo $isReply ? 'reply-message' : 'original-message'; ?> <?php echo $isCurrentUser ? 'from-current-user' : 'from-other-user'; ?>" 
                                 style="<?php echo $isReply ? 'margin-left: 2rem; border-left: 3px solid #dee2e6;' : ''; ?>">
                                
                                <div class="p-4 <?php echo $index < count($messageThread) - 1 ? 'border-bottom' : ''; ?>">
                                    <!-- Message Header -->
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="message-meta">
                                            <!-- Desktop Layout -->
                                            <div class="d-none d-md-block">
                                                <div class="d-flex align-items-center mb-1">
                                                    <?php if ($isReply): ?>
                                                        <i class="fas fa-reply text-muted me-2"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-envelope text-primary me-2"></i>
                                                    <?php endif; ?>

                                                    <strong class="<?php echo $isCurrentUser ? 'text-success' : 'text-primary'; ?>">
                                                        <?php echo $isCurrentUser ? 'You' : htmlspecialchars($threadMessage->from_user_name ?? 'System'); ?>
                                                    </strong>

                                                    <?php if ($isOriginal): ?>
                                                        <span class="badge bg-primary ms-2">Original</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary ms-2">Reply</span>
                                                    <?php endif; ?>

                                                    <?php if (!$threadMessage->is_read && $threadMessage->to_user_id == $currentUserId): ?>
                                                        <span class="badge bg-danger ms-2">Unread</span>
                                                    <?php endif; ?>
                                                </div>

                                                <small class="text-muted">
                                                    <?php echo date('F j, Y \a\t g:i A', strtotime($threadMessage->created_at)); ?>
                                                    <?php if ($isCurrentUser): ?>
                                                        <span class="ms-2">• Sent to <?php echo htmlspecialchars($threadMessage->to_user_id == $currentUserId ? 'You' : 'Other User'); ?></span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>

                                            <!-- Mobile Layout - Horizontal -->
                                            <div class="d-md-none">
                                                <div class="d-flex align-items-center flex-wrap">
                                                    <?php if ($isReply): ?>
                                                        <i class="fas fa-reply text-muted me-2" style="font-size: 14px;"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-envelope text-primary me-2" style="font-size: 14px;"></i>
                                                    <?php endif; ?>

                                                    <strong class="<?php echo $isCurrentUser ? 'text-success' : 'text-primary'; ?> me-2" style="font-size: 14px;">
                                                        <?php echo $isCurrentUser ? 'You' : htmlspecialchars($threadMessage->from_user_name ?? 'System'); ?>
                                                    </strong>

                                                    <?php if ($isOriginal): ?>
                                                        <span class="badge bg-primary me-2" style="font-size: 10px;">Original</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary me-2" style="font-size: 10px;">Reply</span>
                                                    <?php endif; ?>

                                                    <?php if (!$threadMessage->is_read && $threadMessage->to_user_id == $currentUserId): ?>
                                                        <span class="badge bg-danger me-2" style="font-size: 10px;">Unread</span>
                                                    <?php endif; ?>
                                                </div>

                                                <small class="text-muted d-block mt-1" style="font-size: 12px;">
                                                    <?php echo date('M j, Y g:i A', strtotime($threadMessage->created_at)); ?>
                                                    <?php if ($isCurrentUser): ?>
                                                        <span class="ms-2">• Sent to <?php echo htmlspecialchars($threadMessage->to_user_id == $currentUserId ? 'You' : 'Other User'); ?></span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Show Info (only for original message) -->
                                    <?php if ($isOriginal && !empty($threadMessage->show_title)): ?>
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-car me-1"></i>
                                            <strong>Related Show:</strong> 
                                            <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $threadMessage->show_id; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($threadMessage->show_title); ?>
                                            </a>
                                            <?php if (!empty($threadMessage->show_location)): ?>
                                                <span class="text-muted">• <?php echo htmlspecialchars($threadMessage->show_location); ?></span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Message Content -->
                                    <div class="message-content <?php echo $isCurrentUser ? 'bg-light' : ''; ?>" 
                                         style="<?php echo $isCurrentUser ? 'padding: 1rem; border-radius: 0.5rem; background-color: #f8f9fa !important;' : ''; ?>">
                                        <?php echo nl2br(htmlspecialchars($threadMessage->message)); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="p-4 text-center text-muted">
                            <i class="fas fa-inbox fa-2x mb-3"></i>
                            <p>No messages found in this conversation.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Reply Section -->
            <?php if (!empty($messageThread)): ?>
                <?php 
                $lastMessage = end($messageThread);
                $canReply = ($lastMessage->requires_reply || $lastMessage->message_type === 'direct');
                ?>
                <?php if ($canReply): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-reply me-2"></i>Reply to this conversation
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (isset($can_send_notifications) && $can_send_notifications): ?>
                            <form action="<?php echo BASE_URL; ?>/notification_center/reply" method="POST">
                                <input type="hidden" name="parent_message_id" value="<?php echo $lastMessage->parent_message_id ?: $lastMessage->id; ?>">
                                <input type="hidden" name="to_user_id" value="<?php echo $lastMessage->from_user_id == $currentUserId ? $lastMessage->to_user_id : $lastMessage->from_user_id; ?>">
                                
                                <div class="mb-3">
                                    <label for="reply_message" class="form-label">Your Reply</label>
                                    <textarea class="form-control" id="reply_message" name="message" rows="4" 
                                              placeholder="Type your reply here..." required></textarea>
                                    <div class="form-text">
                                        Your reply will be added to this conversation thread.
                                    </div>
                                </div>
                                
                                <!-- Desktop Layout -->
                                <div class="d-none d-md-flex justify-content-between align-items-center">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>Send Reply
                                    </button>
                                    <small class="text-muted">
                                        Replies are sent using the recipient's notification preferences.
                                    </small>
                                </div>

                                <!-- Mobile Layout - Compact -->
                                <div class="d-md-none">
                                    <button type="submit" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-paper-plane me-1"></i>Send Reply
                                    </button>
                                    <small class="text-muted d-block text-center" style="font-size: 11px;">
                                        Sent using recipient's notification preferences
                                    </small>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Cannot Send Replies
                                </h6>
                                <p class="mb-2">You cannot send replies because all your notification types are disabled.</p>
                                <p class="mb-0">
                                    <strong>To send replies:</strong> Enable at least one notification type in your 
                                    <a href="<?php echo BASE_URL; ?>/profile/notifications" class="alert-link">notification preferences</a>.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <div class="d-flex flex-column flex-md-row justify-content-center align-items-center gap-2">
                        <?php if (!empty($messageThread)): ?>
                            <?php $firstMessage = $messageThread[0]; ?>
                            <?php if ($firstMessage->show_id): ?>
                                <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $firstMessage->show_id; ?>"
                                   class="btn btn-primary action-button">
                                    <i class="fas fa-car me-1"></i>View Related Show
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>

                        <button type="button" class="btn btn-outline-secondary action-button"
                                onclick="archiveMessage(<?php echo $message->id; ?>)">
                            <i class="fas fa-archive me-1"></i>Archive Conversation
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Threaded Message Styles */
.message-item {
    transition: background-color 0.2s ease;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.original-message {
    background-color: #fff;
}

.reply-message {
    background-color: #fafbfc;
    position: relative;
}

.reply-message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.from-current-user .message-content {
    background-color: #e3f2fd !important;
    border-left: 3px solid #2196f3;
}

.from-other-user .message-content {
    background-color: #f5f5f5;
    border-left: 3px solid #6c757d;
}

.message-content {
    font-size: 1rem;
    line-height: 1.6;
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
}

.alert-warning {
    border-left: 4px solid #ffc107;
}

/* Thread indicators */
.reply-message {
    border-left: 3px solid #dee2e6;
    margin-left: 2rem;
}

.message-meta strong {
    font-weight: 600;
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .message-content {
        font-size: 14px;
        padding: 0.75rem;
        line-height: 1.4;
    }

    .reply-message {
        margin-left: 1rem !important;
    }

    /* Mobile header optimizations */
    .card-header h5 {
        font-size: 16px;
        line-height: 1.3;
    }

    /* Mobile message meta - horizontal layout */
    .message-meta .d-flex.flex-wrap {
        gap: 0.25rem;
    }

    .message-meta .badge {
        font-size: 10px !important;
        padding: 0.2rem 0.4rem;
    }

    /* Mobile reply section */
    .card-body {
        padding: 1rem;
    }

    /* Mobile form elements */
    .form-control {
        font-size: 14px;
    }

    .btn {
        font-size: 14px;
        padding: 0.5rem 1rem;
    }

    /* Mobile back button */
    .btn-sm {
        font-size: 12px;
        padding: 0.25rem 0.5rem;
    }

    /* Mobile action buttons - same size */
    .action-button {
        width: 100%;
        max-width: 250px;
        margin-bottom: 0.5rem;
    }

    .action-button:last-child {
        margin-bottom: 0;
    }
}

/* Action buttons - consistent sizing on all devices */
.action-button {
    min-width: 180px;
    padding: 0.75rem 1.5rem;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

/* Desktop action buttons */
@media (min-width: 769px) {
    .action-button {
        width: auto;
        margin: 0 0.5rem;
    }

    .action-button:first-child {
        margin-left: 0;
    }

    .action-button:last-child {
        margin-right: 0;
    }
}
</style>

<script>
function archiveMessage(messageId) {
    if (!confirm('Archive this message?')) {
        return;
    }
    
    fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '<?php echo BASE_URL; ?>/notification_center';
        } else {
            alert('Failed to archive: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to archive message');
    });
}

// Auto-resize textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('reply_message');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
});
</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>