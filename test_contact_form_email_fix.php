<?php
/**
 * Test Contact Form Email Fix
 * 
 * Tests that contact forms now use the contact form email directly instead of user ID 1
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>📧 Test Contact Form Email Fix</h1>";

try {
    $db = new Database();
    
    echo "<h2>🎯 Contact Form Email Issue - FIXED</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Root Cause Identified and Fixed:</h3>";
    echo "<ul>";
    echo "<li>❌ <strong>Problem:</strong> Contact forms were using non-existent user ID 1 as sender</li>";
    echo "<li>🔍 <strong>Result:</strong> Email failures because user ID 1 doesn't exist in your system</li>";
    echo "<li>⚡ <strong>Solution:</strong> Use contact form email directly, bypass unified messaging for contact forms</li>";
    echo "<li>✅ <strong>Benefit:</strong> Proper reply-to functionality and no more user ID 1 errors</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔧 Implementation Changes</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📝 Before vs After:</h3>";
    
    echo "<h4>❌ Before (Broken):</h4>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
    echo "// HomeController::sendContactMessageToAdmins()\n";
    echo "\$messageId = \$messageModel->sendMessage(\n";
    echo "    1, // From system user (DOESN'T EXIST!)\n";
    echo "    \$admin->id, // To admin\n";
    echo "    \$subject,\n";
    echo "    \$message\n";
    echo ");\n\n";
    echo "// Problem: User ID 1 doesn't exist, causes email failures";
    echo "</pre>";
    
    echo "<h4>✅ After (Fixed):</h4>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
    echo "// HomeController::sendContactMessageToAdmins()\n";
    echo "\$sent = \$emailService->sendContactFormEmail(\n";
    echo "    \$admin->email,           // To admin email\n";
    echo "    \$subject,                // Contact form subject\n";
    echo "    \$htmlBody,               // Formatted HTML email\n";
    echo "    \$plainText,              // Plain text version\n";
    echo "    \$data['email'],          // Reply-to: contact form email\n";
    echo "    \$data['name']            // Sender name\n";
    echo ");\n\n";
    echo "// Solution: Direct email sending with proper reply-to headers";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>📧 Email Header Comparison</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Email Headers Now vs Before:</h3>";
    
    echo "<table border='1' cellpadding='10' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Header</th><th>Before (Broken)</th><th>After (Fixed)</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>From</strong></td>";
    echo "<td style='color: red;'>❌ User ID 1 (doesn't exist)</td>";
    echo "<td style='color: green;'>✅ System email from /admin/settings_email</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Reply-To</strong></td>";
    echo "<td style='color: red;'>❌ System email (not helpful)</td>";
    echo "<td style='color: green;'>✅ Contact form email (perfect for replies)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Subject</strong></td>";
    echo "<td style='color: orange;'>⚠️ Contact Form: [subject]</td>";
    echo "<td style='color: green;'>✅ Contact Form: [subject]</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Content</strong></td>";
    echo "<td style='color: orange;'>⚠️ Plain text message</td>";
    echo "<td style='color: green;'>✅ Formatted HTML with contact details</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Delivery</strong></td>";
    echo "<td style='color: red;'>❌ Failed (user ID 1 error)</td>";
    echo "<td style='color: green;'>✅ Success (direct email sending)</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    echo "<h2>🧪 Test Contact Form Processing</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Simulate Contact Form Submission:</h3>";
    
    echo "<form method='post' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>Test Contact Form:</h4>";
    echo "<table>";
    echo "<tr><td><strong>Name:</strong></td><td><input type='text' name='test_name' value='John Doe' style='width: 200px; padding: 5px;'></td></tr>";
    echo "<tr><td><strong>Email:</strong></td><td><input type='email' name='test_email' value='<EMAIL>' style='width: 200px; padding: 5px;'></td></tr>";
    echo "<tr><td><strong>Subject:</strong></td><td><input type='text' name='test_subject' value='Test Contact Form' style='width: 200px; padding: 5px;'></td></tr>";
    echo "<tr><td><strong>Message:</strong></td><td><textarea name='test_message' style='width: 200px; height: 60px; padding: 5px;'>This is a test contact form message to verify the email fix is working.</textarea></td></tr>";
    echo "</table>";
    echo "<button type='submit' name='test_contact_form' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-top: 10px;'>Test Contact Form Email</button>";
    echo "</form>";
    
    if (isset($_POST['test_contact_form'])) {
        echo "<h4>🔄 Contact Form Test Results:</h4>";
        
        try {
            // Load required classes
            require_once APPROOT . '/models/EmailService.php';
            
            $emailService = new EmailService();
            
            // Get admin users
            $db->query("SELECT id, name, email FROM users WHERE role = 'admin' AND status = 'active'");
            $db->execute();
            $admins = $db->resultSet();
            
            if (empty($admins)) {
                echo "<p style='color: red;'>❌ No admin users found</p>";
            } else {
                $testData = [
                    'name' => $_POST['test_name'] ?? 'Test User',
                    'email' => $_POST['test_email'] ?? '<EMAIL>',
                    'subject' => $_POST['test_subject'] ?? 'Test Subject',
                    'message' => $_POST['test_message'] ?? 'Test message'
                ];
                
                $subject = 'Contact Form: ' . $testData['subject'];
                $sentCount = 0;
                $errors = [];
                
                foreach ($admins as $admin) {
                    // Create HTML email body
                    $htmlBody = "<html><body>";
                    $htmlBody .= "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>";
                    $htmlBody .= "<h2 style='color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;'>New Contact Form Submission</h2>";
                    
                    $htmlBody .= "<div style='background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
                    $htmlBody .= "<p><strong>From:</strong> " . htmlspecialchars($testData['name']) . " &lt;" . htmlspecialchars($testData['email']) . "&gt;</p>";
                    $htmlBody .= "<p><strong>Subject:</strong> " . htmlspecialchars($testData['subject']) . "</p>";
                    $htmlBody .= "<p><strong>Submitted:</strong> " . date('Y-m-d H:i:s') . "</p>";
                    $htmlBody .= "</div>";
                    
                    $htmlBody .= "<div style='background-color: #ffffff; padding: 20px; border-left: 4px solid #007bff; margin: 20px 0;'>";
                    $htmlBody .= "<h3 style='color: #333; margin-top: 0;'>Message:</h3>";
                    $htmlBody .= "<p>" . nl2br(htmlspecialchars($testData['message'])) . "</p>";
                    $htmlBody .= "</div>";
                    
                    $htmlBody .= "<div style='background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                    $htmlBody .= "<p style='margin: 0; color: #666; font-size: 14px;'>";
                    $htmlBody .= "<strong>To reply:</strong> Simply reply to this email and your response will go directly to " . htmlspecialchars($testData['email']);
                    $htmlBody .= "</p></div>";
                    
                    $htmlBody .= "</div></body></html>";
                    
                    // Send using new method
                    $sent = $emailService->sendContactFormEmail(
                        $admin->email,
                        $subject,
                        $htmlBody,
                        strip_tags($testData['message']),
                        $testData['email'],
                        $testData['name']
                    );
                    
                    if ($sent) {
                        $sentCount++;
                        echo "<p style='color: green;'>✅ Email sent successfully to: " . htmlspecialchars($admin->name) . " (" . htmlspecialchars($admin->email) . ")</p>";
                    } else {
                        $errors[] = "Failed to send to: " . htmlspecialchars($admin->name) . " (" . htmlspecialchars($admin->email) . ")";
                        echo "<p style='color: red;'>❌ Failed to send to: " . htmlspecialchars($admin->name) . " (" . htmlspecialchars($admin->email) . ")</p>";
                    }
                }
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px; margin: 15px 0;'>";
                echo "<strong>Test Results:</strong><br>";
                echo "Total Admins: " . count($admins) . "<br>";
                echo "Emails Sent: {$sentCount}<br>";
                echo "Failures: " . count($errors) . "<br>";
                echo "</div>";
                
                if ($sentCount > 0) {
                    echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Contact form emails are now working without user ID 1 errors!</p>";
                    
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
                    echo "<h4>✅ What's Fixed:</h4>";
                    echo "<ul>";
                    echo "<li>No more user ID 1 lookup failures</li>";
                    echo "<li>Proper reply-to headers set to contact form email</li>";
                    echo "<li>Professional HTML email formatting</li>";
                    echo "<li>Direct email delivery (no unified messaging dependency)</li>";
                    echo "</ul>";
                    echo "</div>";
                } else {
                    echo "<p style='color: red;'>❌ All emails failed. Check SMTP configuration in /admin/settings_email</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
            echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
    }
    echo "</div>";
    
    echo "<h2>📊 Benefits of the Fix</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 Improvements:</h3>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Aspect</th><th>Before</th><th>After</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Email Delivery</strong></td>";
    echo "<td style='color: red;'>❌ Failed (user ID 1 error)</td>";
    echo "<td style='color: green;'>✅ Success (direct sending)</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Reply Functionality</strong></td>";
    echo "<td style='color: red;'>❌ Replies go to system email</td>";
    echo "<td style='color: green;'>✅ Replies go to contact form sender</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Email Format</strong></td>";
    echo "<td style='color: orange;'>⚠️ Plain text</td>";
    echo "<td style='color: green;'>✅ Professional HTML formatting</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Error Tracking</strong></td>";
    echo "<td style='color: red;'>❌ Mysterious user ID 1 failures</td>";
    echo "<td style='color: green;'>✅ Clear SMTP error messages</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Dependencies</strong></td>";
    echo "<td style='color: red;'>❌ Requires unified messaging system</td>";
    echo "<td style='color: green;'>✅ Direct email service only</td>";
    echo "</tr>";
    
    echo "</table>";
    echo "</div>";
    
    echo "<h2>📋 Files Updated</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>Files to copy to server:</strong></p>";
    echo "<ul>";
    echo "<li><code>controllers/HomeController.php</code> - Updated sendContactMessageToAdmins() to use direct email sending</li>";
    echo "<li><code>models/EmailService.php</code> - Added sendContactFormEmail() method with proper reply-to headers</li>";
    echo "</ul>";
    
    echo "<p><strong>What this fixes:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>No more user ID 1 errors:</strong> Contact forms no longer try to lookup non-existent user</li>";
    echo "<li>✅ <strong>Proper reply functionality:</strong> Admins can reply directly to contact form senders</li>";
    echo "<li>✅ <strong>Professional email format:</strong> HTML emails with proper formatting and contact details</li>";
    echo "<li>✅ <strong>Reliable delivery:</strong> Direct SMTP sending without unified messaging dependencies</li>";
    echo "<li>✅ <strong>Better error tracking:</strong> Clear SMTP errors instead of mysterious user lookup failures</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Contact form email fix test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
