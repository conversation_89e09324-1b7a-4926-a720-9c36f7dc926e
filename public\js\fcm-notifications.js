/**
 * FCM Notifications JavaScript v1.0.0
 * 
 * This file handles Firebase Cloud Messaging (FCM) for push notifications
 * using the Firebase Web SDK v9+ modular approach.
 * 
 * Location: /public/js/fcm-notifications.js
 * Dependencies: Firebase Web SDK
 */

// Import Firebase modules (will be loaded from CDN)
let messaging = null;
let isFirebaseInitialized = false;

// Add debug logging at the top
console.log('[FCM] fcm-notifications.js loaded');

/**
 * FCM Notification Manager Class
 */
class FCMNotificationManager {
    constructor() {
        console.log('[FCM] FCMNotificationManager constructor called');
        this.baseUrl = window.BASE_URL || '';
        this.debugMode = localStorage.getItem('notification_debug') === 'true' ||
                         document.body.dataset.debugMode === 'true' ||
                         window.location.search.includes('debug=1');
        this.fcmToken = null;
        this.isSupported = this.checkSupport();
        this.tokenKey = 'fcm_token';
        this.tokenTimestampKey = 'fcm_token_timestamp';
        this.tokenMaxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
        this.messaging = null;
        this.vapidKey = null;
        
        console.log('[FCM] FCM Notification Manager initialized');
        console.log('[FCM] Is supported:', this.isSupported);
        console.log('[FCM] Debug mode:', this.debugMode);
    }

    checkSupport() {
        const supported = 'serviceWorker' in navigator && 
                         'PushManager' in window && 
                         'Notification' in window &&
                         !this.isIOS();
        
        console.log('[FCM] Support check:', {
            serviceWorker: 'serviceWorker' in navigator,
            pushManager: 'PushManager' in window,
            notification: 'Notification' in window,
            isIOS: this.isIOS(),
            supported: supported
        });
        
        return supported;
    }

    isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    }

    async initializeFirebase() {
        console.log('[FCM] Initializing Firebase...');
        
        try {
            // Check if Firebase SDK is loaded
            if (!window.firebase) {
                throw new Error('Firebase SDK not loaded. Check if Firebase scripts are included.');
            }
            
            console.log('[FCM] Firebase SDK version:', window.firebase.SDK_VERSION || 'unknown');
            
            // Ensure our unified service worker is registered first
            const registration = await this.ensureUnifiedServiceWorker();
            
            // Initialize Firebase app if not already done
            if (!window.firebase.apps || window.firebase.apps.length === 0) {
                console.log('[FCM] Initializing Firebase app...');
                window.firebase.initializeApp({
                    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
                    authDomain: "rowaneliterides.firebaseapp.com",
                    projectId: "rowaneliterides",
                    storageBucket: "rowaneliterides.firebasestorage.app",
                    messagingSenderId: "310533125467",
                    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
                });
            }

            // Initialize messaging with our unified service worker
            if (!window.firebase.messaging.isSupported()) {
                throw new Error('Firebase messaging is not supported in this browser');
            }
            
            console.log('[FCM] Using unified service worker for Firebase messaging:', registration.scope);
            
            // Initialize messaging - Firebase will automatically use the active service worker
            this.messaging = window.firebase.messaging();
            
            console.log('[FCM] Firebase messaging initialized with unified service worker');
            
            // Get VAPID key
            await this.getVAPIDKey();
            
            console.log('[FCM] Firebase initialized successfully');
            return true;
            
        } catch (error) {
            console.error('[FCM] Firebase initialization failed:', error);
            throw error;
        }
    }

    async ensureUnifiedServiceWorker() {
        console.log('[FCM] Ensuring unified service worker is registered...');
        
        try {
            // Check if service worker is already registered
            let registration = await navigator.serviceWorker.getRegistration('/');
            
            if (!registration) {
                console.log('[FCM] Registering unified service worker...');
                registration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });
                console.log('[FCM] Unified service worker registered:', registration.scope);
            } else {
                console.log('[FCM] Unified service worker already registered:', registration.scope);
            }
            
            // Wait for it to be ready
            await navigator.serviceWorker.ready;
            
            // Verify it's active
            if (!registration.active) {
                console.log('[FCM] Waiting for unified service worker to activate...');
                await new Promise((resolve) => {
                    const checkState = () => {
                        if (registration.active) {
                            resolve();
                        } else {
                            setTimeout(checkState, 100);
                        }
                    };
                    checkState();
                });
            }
            
            console.log('[FCM] Unified service worker is active and ready');
            return registration;
            
        } catch (error) {
            console.error('[FCM] Failed to ensure unified service worker:', error);
            throw error;
        }
    }

    async getVAPIDKey() {
        console.log('[FCM] Getting VAPID key...');
        
        try {
            const response = await fetch('/api/pwa/vapid-key');
            const data = await response.json();
            
            if (data.success && data.publicKey) {
                this.vapidKey = data.publicKey;
                console.log('[FCM] VAPID key obtained');
                return this.vapidKey;
            } else {
                throw new Error('Failed to get VAPID key: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('[FCM] Error getting VAPID key:', error);
            throw error;
        }
    }

    async requestPermissionAndGetToken() {
        console.log('[FCM] requestPermissionAndGetToken called');
        
        try {
            // Initialize Firebase first if not done
            if (!this.messaging) {
                await this.initializeFirebase();
            }
            
            // Check if we have a valid cached token first
            const cachedToken = this.getCachedToken();
            if (cachedToken) {
                console.log('[FCM] Using cached FCM token');
                this.fcmToken = cachedToken;
                return cachedToken;
            }

            console.log('[FCM] No valid cached token, requesting new one...');
            
            // Check permission first
            if (Notification.permission === 'denied') {
                throw new Error('Notification permission denied');
            }
            
            // Request permission if needed
            if (Notification.permission === 'default') {
                console.log('[FCM] Requesting notification permission...');
                const permission = await Notification.requestPermission();
                console.log('[FCM] Permission result:', permission);
                
                if (permission !== 'granted') {
                    throw new Error('Notification permission denied by user');
                }
            }

            console.log('[FCM] Permission granted, getting token...');

            // Ensure service worker is ready
            await navigator.serviceWorker.ready;
            
            // Get token with VAPID key - Firebase will use the active service worker
            const token = await this.messaging.getToken({
                vapidKey: this.vapidKey
            });

            if (token) {
                console.log('[FCM] New FCM token generated:', token.substring(0, 20) + '...');
                this.fcmToken = token;
                this.cacheToken(token);
                await this.sendTokenToServer(token);
                return token;
            } else {
                throw new Error('No registration token available');
            }
        } catch (error) {
            console.error('[FCM] Error getting FCM token:', error);
            
            // Check if this is the service worker registration error
            if (error.message.includes('firebase-messaging-sw.js') || error.message.includes('failed-service-worker-registration')) {
                console.error('[FCM] Firebase is trying to register its own service worker.');
                console.error('[FCM] You need to create a firebase-messaging-sw.js file or use a different approach.');
                
                // Suggest creating the missing file
                throw new Error('Firebase requires firebase-messaging-sw.js. Please create this file or use a unified service worker approach.');
            }
            
            throw error;
        }
    }

    setupForegroundMessageHandling() {
        if (!this.messaging) {
            console.warn('[FCM] Cannot setup message handling - messaging not initialized');
            return;
        }
        
        console.log('[FCM] Setting up foreground message handling...');
        
        this.messaging.onMessage((payload) => {
            console.log('[FCM] Message received in foreground:', payload);
            this.showNotification(payload);
        });
    }

    showNotification(payload) {
        const notificationTitle = payload.notification?.title || 'New Notification';
        const notificationOptions = {
            body: payload.notification?.body || 'You have a new notification',
            icon: '/public/images/icons/icon-192x192.png',
            badge: '/public/images/icons/badge-72x72.png',
            tag: 'fcm-' + Date.now(),
            requireInteraction: false,
            data: payload.data || {}
        };

        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                registration.showNotification(notificationTitle, notificationOptions);
            });
        }
    }

    getCachedToken() {
        const token = localStorage.getItem(this.tokenKey);
        const timestamp = localStorage.getItem(this.tokenTimestampKey);
        
        console.log('[FCM] Checking cached token:', token ? 'exists' : 'none');
        
        if (!token || !timestamp) {
            return null;
        }

        // Check if token is expired
        const tokenAge = Date.now() - parseInt(timestamp);
        if (tokenAge > this.tokenMaxAge) {
            console.log('[FCM] Cached token expired, will generate new one');
            this.clearCachedToken();
            return null;
        }

        console.log('[FCM] Cached token is valid');
        return token;
    }

    cacheToken(token) {
        console.log('[FCM] Caching token');
        localStorage.setItem(this.tokenKey, token);
        localStorage.setItem(this.tokenTimestampKey, Date.now().toString());
    }

    clearCachedToken() {
        console.log('[FCM] Clearing cached token');
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.tokenTimestampKey);
    }

    async sendTokenToServer(token) {
        console.log('[FCM] Sending token to server...');
        
        // Only send if we don't have this token on server
        const lastSentToken = localStorage.getItem('fcm_last_sent_token');
        if (lastSentToken === token) {
            console.log('[FCM] Token already sent to server');
            return;
        }

        try {
            const response = await fetch('/api/pwa/fcm-subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    fcm_token: token,
                    user_agent: navigator.userAgent
                })
            });

            const data = await response.json();
            console.log('[FCM] Server response:', data);
            
            if (data.success) {
                localStorage.setItem('fcm_last_sent_token', token);
                console.log('[FCM] Token sent to server successfully');
            } else {
                console.error('[FCM] Server rejected token:', data.error);
            }
        } catch (error) {
            console.error('[FCM] Failed to send token to server:', error);
        }
    }
}

// CSS for toast notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .fcm-toast-content {
        display: flex;
        align-items: center;
        padding: 12px;
    }
    
    .fcm-toast-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        border-radius: 4px;
    }
    
    .fcm-toast-text {
        flex: 1;
    }
    
    .fcm-toast-title {
        font-weight: bold;
        margin-bottom: 4px;
        color: #333;
    }
    
    .fcm-toast-body {
        color: #666;
        font-size: 14px;
    }
    
    .fcm-toast-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #999;
        margin-left: 8px;
    }
    
    .fcm-toast-close:hover {
        color: #333;
    }
`;
document.head.appendChild(style);

// Global error handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    if (event.reason && event.reason.message && event.reason.message.includes('pushManager')) {
        console.error('[FCM] Unhandled pushManager error caught:', event.reason);
        console.error('[FCM] Stack trace:', event.reason.stack);
        
        // Prevent the error from showing in console if we're handling it
        event.preventDefault();
        
        // Show user-friendly error
        if (window.fcmManager && window.fcmManager.showAlert) {
            window.fcmManager.showAlert('error', 'Push notification setup failed. Please refresh the page and try again.');
        }
    }
});

// Initialize FCM when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('[FCM] DOM loaded, checking initialization conditions...');
    
    // Only initialize if user is logged in
    if (window.PWA_CONFIG && window.PWA_CONFIG.userId) {
        console.log('[FCM] User logged in, initializing FCM manager...');
        window.fcmManager = new FCMNotificationManager();
        
        // Setup foreground message handling and auto-init
        setTimeout(async () => {
            console.log('[FCM] Starting delayed initialization...');
            if (window.fcmManager) {
                try {
                    // Initialize Firebase first
                    if (window.fcmManager.isSupported) {
                        await window.fcmManager.initializeFirebase();
                        window.fcmManager.setupForegroundMessageHandling();
                        
                        // Auto-initialize FCM if permission already granted
                        if (Notification.permission === 'granted') {
                            console.log('[FCM] Permission already granted, getting token...');
                            await window.fcmManager.requestPermissionAndGetToken();
                        } else {
                            console.log('[FCM] Permission not granted yet, waiting for user action');
                        }
                    }
                } catch (error) {
                    console.error('[FCM] Error during initialization:', error);
                }
            }
        }, 2000);
    } else {
        console.log('[FCM] User not logged in or PWA_CONFIG missing, skipping FCM initialization');
        console.log('[FCM] PWA_CONFIG:', window.PWA_CONFIG);
    }
});

// Make FCM manager globally available
window.FCMNotificationManager = FCMNotificationManager;
console.log('[FCM] FCMNotificationManager class made globally available');
