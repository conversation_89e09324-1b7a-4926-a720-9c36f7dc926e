<?php
/**
 * Debug Email Notifications
 * 
 * Checks why email notifications aren't being sent for contact form messages
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🔍 Debug Email Notifications</h1>";

try {
    $db = new Database();
    
    echo "<h2>📊 Notification Queue Status</h2>";
    
    // Check pending email notifications
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE notification_type = 'email' AND status = 'pending'");
    $pendingEmails = $db->single();
    $pendingCount = $pendingEmails->count ?? 0;
    
    // Check sent email notifications
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE notification_type = 'email' AND status = 'sent'");
    $sentEmails = $db->single();
    $sentCount = $sentEmails->count ?? 0;
    
    // Check failed email notifications
    $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE notification_type = 'email' AND status = 'failed'");
    $failedEmails = $db->single();
    $failedCount = $failedEmails->count ?? 0;
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Status</th><th>Count</th><th>Action Needed</th></tr>";
    echo "<tr><td><strong>Pending</strong></td><td style='color: orange;'>{$pendingCount}</td><td>" . ($pendingCount > 0 ? 'Cron job needs to process these' : 'No pending emails') . "</td></tr>";
    echo "<tr><td><strong>Sent</strong></td><td style='color: green;'>{$sentCount}</td><td>Successfully delivered</td></tr>";
    echo "<tr><td><strong>Failed</strong></td><td style='color: red;'>{$failedCount}</td><td>" . ($failedCount > 0 ? 'Check error messages' : 'No failures') . "</td></tr>";
    echo "</table>";
    
    echo "<h2>📋 Recent Email Queue Entries</h2>";
    
    // Get recent email notifications
    $db->query("SELECT * FROM notification_queue WHERE notification_type = 'email' ORDER BY created_at DESC LIMIT 10");
    $recentEmails = $db->resultSet();
    
    if (!empty($recentEmails)) {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>User ID</th><th>Subject</th><th>Status</th><th>Created</th><th>Error</th></tr>";
        
        foreach ($recentEmails as $email) {
            $statusColor = $email->status === 'sent' ? 'green' : ($email->status === 'failed' ? 'red' : 'orange');
            echo "<tr>";
            echo "<td>{$email->id}</td>";
            echo "<td>{$email->user_id}</td>";
            echo "<td>" . htmlspecialchars(substr($email->subject, 0, 50)) . "</td>";
            echo "<td style='color: {$statusColor};'>{$email->status}</td>";
            echo "<td>{$email->created_at}</td>";
            echo "<td>" . htmlspecialchars($email->error_message ?? 'None') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No email notifications found in queue.</p>";
    }
    
    echo "<h2>🔧 Global Notification Settings</h2>";
    
    // Check global notification settings
    $notificationModel = new NotificationModel();
    $globalSettings = $notificationModel->getNotificationSettings();
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr style='background: #f0f0f0;'><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    $settings = [
        'email_enabled' => $globalSettings['email_enabled'] ?? false,
        'push_enabled' => $globalSettings['push_enabled'] ?? false,
        'toast_enabled' => $globalSettings['toast_enabled'] ?? false,
        'sms_enabled' => $globalSettings['sms_enabled'] ?? false
    ];
    
    foreach ($settings as $key => $value) {
        $status = $value ? '✅ Enabled' : '❌ Disabled';
        $statusColor = $value ? 'green' : 'red';
        echo "<tr>";
        echo "<td><strong>" . ucwords(str_replace('_', ' ', $key)) . "</strong></td>";
        echo "<td>" . ($value ? 'True' : 'False') . "</td>";
        echo "<td style='color: {$statusColor};'>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>👤 User Notification Preferences</h2>";
    
    // Get admin users and their preferences
    $db->query("SELECT u.id, u.name, u.email, 
                       unp.email_notifications, unp.push_notifications, unp.toast_notifications
                FROM users u 
                LEFT JOIN user_notification_preferences unp ON u.id = unp.user_id 
                WHERE u.role = 'admin' AND u.status = 'active'");
    $adminUsers = $db->resultSet();
    
    if (!empty($adminUsers)) {
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Admin</th><th>Email</th><th>Email Notifications</th><th>Push</th><th>Toast</th></tr>";
        
        foreach ($adminUsers as $admin) {
            $emailPref = $admin->email_notifications ? '✅ Enabled' : '❌ Disabled';
            $pushPref = $admin->push_notifications ? '✅ Enabled' : '❌ Disabled';
            $toastPref = $admin->toast_notifications ? '✅ Enabled' : '❌ Disabled';
            
            echo "<tr>";
            echo "<td><strong>" . htmlspecialchars($admin->name) . "</strong></td>";
            echo "<td>" . htmlspecialchars($admin->email) . "</td>";
            echo "<td style='color: " . ($admin->email_notifications ? 'green' : 'red') . ";'>{$emailPref}</td>";
            echo "<td style='color: " . ($admin->push_notifications ? 'green' : 'red') . ";'>{$pushPref}</td>";
            echo "<td style='color: " . ($admin->toast_notifications ? 'green' : 'red') . ";'>{$toastPref}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No active admin users found.</p>";
    }
    
    echo "<h2>🔍 Diagnosis</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Possible Issues:</h3>";
    
    if ($pendingCount > 0) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
        echo "<strong>⚠️ Issue: {$pendingCount} pending email(s) in queue</strong><br>";
        echo "<strong>Cause:</strong> Cron job not running or not processing emails<br>";
        echo "<strong>Solution:</strong> Check if cron job is configured and running";
        echo "</div>";
    }
    
    if (!$globalSettings['email_enabled']) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
        echo "<strong>❌ Issue: Email notifications disabled globally</strong><br>";
        echo "<strong>Cause:</strong> Global email setting is turned off<br>";
        echo "<strong>Solution:</strong> Enable email notifications in admin settings";
        echo "</div>";
    }
    
    $adminWithEmailDisabled = 0;
    foreach ($adminUsers as $admin) {
        if (!$admin->email_notifications) {
            $adminWithEmailDisabled++;
        }
    }
    
    if ($adminWithEmailDisabled > 0) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
        echo "<strong>❌ Issue: {$adminWithEmailDisabled} admin(s) have email notifications disabled</strong><br>";
        echo "<strong>Cause:</strong> Individual admin users have email preferences turned off<br>";
        echo "<strong>Solution:</strong> Admins need to enable email notifications in their preferences";
        echo "</div>";
    }
    
    if ($failedCount > 0) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
        echo "<strong>❌ Issue: {$failedCount} failed email(s)</strong><br>";
        echo "<strong>Cause:</strong> Email delivery failures (check error messages above)<br>";
        echo "<strong>Solution:</strong> Check SMTP configuration and error logs";
        echo "</div>";
    }
    
    if ($pendingCount === 0 && $globalSettings['email_enabled'] && $adminWithEmailDisabled === 0 && $failedCount === 0) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
        echo "<strong>✅ Configuration looks correct</strong><br>";
        echo "Email notifications should be working. Try submitting a new contact form to test.";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>🧪 Manual Test</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Test Email Processing:</h3>";
    
    if ($pendingCount > 0) {
        echo "<p><strong>You have {$pendingCount} pending email(s). Let's try to process them manually:</strong></p>";
        echo "<form method='post' style='margin: 10px 0;'>";
        echo "<button type='submit' name='process_emails' class='btn btn-primary'>Process Pending Emails Now</button>";
        echo "</form>";
        
        if (isset($_POST['process_emails'])) {
            echo "<h4>Processing Results:</h4>";
            try {
                $notificationService = new NotificationService();
                $results = $notificationService->processPendingNotifications(10);
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Processed:</strong> {$results['processed']}<br>";
                echo "<strong>Sent:</strong> {$results['sent']}<br>";
                echo "<strong>Failed:</strong> {$results['failed']}<br>";
                
                if (!empty($results['errors'])) {
                    echo "<strong>Errors:</strong><br>";
                    foreach ($results['errors'] as $error) {
                        echo "• " . htmlspecialchars($error) . "<br>";
                    }
                }
                echo "</div>";
                
                // Refresh counts
                $db->query("SELECT COUNT(*) as count FROM notification_queue WHERE notification_type = 'email' AND status = 'pending'");
                $newPendingEmails = $db->single();
                $newPendingCount = $newPendingEmails->count ?? 0;
                
                echo "<p><strong>Remaining pending emails:</strong> {$newPendingCount}</p>";
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Error processing emails:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
        }
    } else {
        echo "<p>No pending emails to process. Submit a contact form to generate test emails.</p>";
    }
    echo "</div>";
    
    echo "<h2>📋 Recommended Actions</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 Next Steps:</h3>";
    echo "<ol>";
    
    if (!$globalSettings['email_enabled']) {
        echo "<li><strong>Enable global email notifications:</strong> Go to admin notification settings</li>";
    }
    
    if ($adminWithEmailDisabled > 0) {
        echo "<li><strong>Check admin email preferences:</strong> Ensure admins have email notifications enabled</li>";
    }
    
    if ($pendingCount > 0) {
        echo "<li><strong>Set up cron job:</strong> Configure cron to run /cron/process_notifications.php every 2 minutes</li>";
        echo "<li><strong>Manual processing:</strong> Use the button above to process emails manually</li>";
    }
    
    echo "<li><strong>Test contact form:</strong> Submit a new contact form and check if email is queued</li>";
    echo "<li><strong>Check SMTP settings:</strong> Verify email configuration at /admin/settings_email</li>";
    echo "<li><strong>Monitor logs:</strong> Check server error logs for email delivery issues</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Debug Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Email notification debug completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
