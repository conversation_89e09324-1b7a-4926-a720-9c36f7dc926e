<?php
/**
 * PWA Controller for Progressive Web App Features
 * Handles PWA-specific functionality including push notifications, offline sync, and app installation
 */

require_once APPROOT . '/models/NotificationModel.php';

class PwaController {
    private $notificationModel;
    private $vapidKeys;
    
    public function __construct() {
        $this->notificationModel = new NotificationModel();
        $this->initVAPIDKeys();
    }
    
    /**
     * Initialize VAPID keys for push notifications
     */
    private function initVAPIDKeys() {
        // Get VAPID keys from config
        $this->vapidKeys = [
            'publicKey' => defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : null,
            'privateKey' => defined('VAPID_PRIVATE_KEY') ? VAPID_PRIVATE_KEY : null
        ];
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[PWA] VAPID keys initialized - Public key available: ' . (!empty($this->vapidKeys['publicKey']) ? 'Yes' : 'No'));
        }
    }
    
    /**
     * Get VAPID public key for client-side subscription
     */
    public function getVapidKey() {
        header('Content-Type: application/json');
        
        if (empty($this->vapidKeys['publicKey'])) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[PWA] VAPID public key not configured');
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'VAPID public key not configured'
            ]);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'publicKey' => $this->vapidKeys['publicKey']
        ]);
    }
    
    // REMOVED: Dead Web Push subscribe() method - using FCM OAuth v1 instead
    
    /**
     * Unsubscribe user from push notifications
     */
    public function unsubscribe() {
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            return;
        }
        
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Authentication required']);
            return;
        }
        
        try {
            $userId = $_SESSION['user_id'];
            $result = $this->notificationModel->removePushSubscription($userId);
            
            if ($result) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("[PWA] Push subscription removed for user {$userId}");
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Unsubscribed successfully'
                ]);
            } else {
                throw new Exception('Failed to remove subscription');
            }
            
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[PWA] Unsubscribe error: ' . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Send push notification to user
     */
    public function sendNotification() {
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            return;
        }
        
        // Check if user has permission to send notifications (admin/coordinator)
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Authentication required']);
            return;
        }
        
        $allowedRoles = ['admin', 'coordinator', 'judge', 'staff'];
        if (!in_array($_SESSION['role'], $allowedRoles)) {
            http_response_code(403);
            echo json_encode(['success' => false, 'error' => 'Insufficient permissions']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['title']) || !isset($input['message'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Title and message are required']);
            return;
        }
        
        try {
            $title = $input['title'];
            $message = $input['message'];
            $targetUserId = isset($input['user_id']) ? $input['user_id'] : null;
            $icon = isset($input['icon']) ? $input['icon'] : '/images/icon-192x192.png';
            $badge = isset($input['badge']) ? $input['badge'] : '/images/badge-72x72.png';
            $url = isset($input['url']) ? $input['url'] : '/';
            
            $result = $this->notificationModel->sendPushNotification(
                $title,
                $message,
                $targetUserId,
                $icon,
                $badge,
                $url
            );
            
            if ($result) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    $target = $targetUserId ? "user {$targetUserId}" : 'all users';
                    error_log("[PWA] Push notification sent to {$target}: {$title}");
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Notification sent successfully'
                ]);
            } else {
                throw new Exception('Failed to send notification');
            }
            
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[PWA] Send notification error: ' . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Sync offline data when connection is restored
     */
    public function syncOfflineData() {
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            return;
        }
        
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Authentication required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['data']) || !is_array($input['data'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid sync data']);
            return;
        }
        
        try {
            $syncResults = [];
            $userId = $_SESSION['user_id'];
            
            foreach ($input['data'] as $item) {
                if (!isset($item['type']) || !isset($item['data'])) {
                    continue;
                }
                
                switch ($item['type']) {
                    case 'registration':
                        $result = $this->syncRegistration($item['data'], $userId);
                        break;
                    case 'payment':
                        $result = $this->syncPayment($item['data'], $userId);
                        break;
                    case 'scores':
                        $result = $this->syncScores($item['data'], $userId);
                        break;
                    default:
                        $result = ['success' => false, 'message' => 'Unknown sync type'];
                }
                
                $syncResults[] = [
                    'type' => $item['type'],
                    'result' => $result
                ];
            }
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[PWA] Offline data sync completed for user {$userId}: " . count($syncResults) . ' items processed');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Sync completed',
                'results' => $syncResults
            ]);
            
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[PWA] Sync error: ' . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sync registration data
     */
    private function syncRegistration($data, $userId) {
        require_once APPROOT . '/controllers/RegistrationController.php';

        $registrationController = new RegistrationController();

        // Temporarily set POST data
        $originalPost = $_POST;
        $_POST = $data;

        ob_start();
        $result = $registrationController->processRegistration();
        $output = ob_get_clean();

        return ['success' => true, 'message' => 'Registration synced successfully'];
    }

    /**
     * Sync payment data
     */
    private function syncPayment($data, $userId) {
        require_once APPROOT . '/controllers/PaymentController.php';

        $paymentController = new PaymentController();

        // Temporarily set POST data
        $originalPost = $_POST;
        $_POST = $data;

        ob_start();
        $result = $paymentController->processPayment();
        $output = ob_get_clean();

        return ['success' => true, 'message' => 'Payment synced successfully'];
    }

    /**
     * Sync scores data
     */
    private function syncScores($data, $userId) {
        require_once APPROOT . '/controllers/JudgeController.php';

        $judgeController = new JudgeController();

        // Temporarily set POST data
        $originalPost = $_POST;
        $_POST = $data;

        ob_start();
        $result = $judgeController->submitScores();
        $output = ob_get_clean();

        return ['success' => true, 'message' => 'Scores synced successfully'];
    }

    /**
     * Get cached data for offline use
     */
    public function getCachedData() {
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            return;
        }

        try {
            $userId = $_SESSION['user_id'];
            $type = $_GET['type'] ?? 'dashboard';

            $data = [];

            switch ($type) {
                case 'dashboard':
                    $data = $this->getDashboardData($userId);
                    break;
                case 'events':
                    $data = $this->getEventsData($userId);
                    break;
                case 'registrations':
                    $data = $this->getRegistrationsData($userId);
                    break;
                default:
                    $data = ['error' => 'Unknown data type'];
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $data,
                'cached_at' => gmdate('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get cached data error: " . $e->getMessage());
            }

            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to get cached data',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get dashboard data for caching
     */
    private function getDashboardData($userId) {
        require_once APPROOT . '/models/UserModel.php';
        require_once APPROOT . '/models/ShowModel.php';
        require_once APPROOT . '/models/RegistrationModel.php';

        $userModel = new UserModel();
        $showModel = new ShowModel();
        $registrationModel = new RegistrationModel();

        return [
            'user' => $userModel->getUserById($userId),
            'upcoming_shows' => $showModel->getUpcomingShows(5),
            'recent_registrations' => $registrationModel->getUserRegistrations($userId),
            'notifications' => $this->notificationModel->getUnreadNotifications($userId)
        ];
    }

    /**
     * Get events data for caching
     */
    private function getEventsData($userId) {
        require_once APPROOT . '/models/CalendarModel.php';

        $calendarModel = new CalendarModel();

        return [
            'upcoming_events' => $calendarModel->getUpcomingEvents(20),
            'user_events' => $calendarModel->getUserEvents($userId),
            'featured_events' => $calendarModel->getFeaturedEvents(10)
        ];
    }

    /**
     * Get registrations data for caching
     */
    private function getRegistrationsData($userId) {
        require_once APPROOT . '/models/RegistrationModel.php';
        require_once APPROOT . '/models/VehicleModel.php';

        $registrationModel = new RegistrationModel();
        $vehicleModel = new VehicleModel();

        return [
            'registrations' => $registrationModel->getUserRegistrations($userId),
            'vehicles' => $vehicleModel->getUserVehicles($userId),
            'pending_payments' => $registrationModel->getPendingPayments($userId)
        ];
    }

    /**
     * Update PWA usage data
     */
    public function updateUsageData() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            return;
        }

        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Authentication required']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
            return;
        }

        try {
            $userId = $_SESSION['user_id'];

            // Log PWA usage data
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[PWA] Usage data received for user {$userId}: " . json_encode($input));
            }

            // You can store usage data in database if needed
            // For now, just acknowledge receipt

            echo json_encode([
                'success' => true,
                'message' => 'Usage data updated successfully'
            ]);

        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[PWA] Usage data update error: ' . $e->getMessage());
            }

            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Subscribe user to FCM push notifications
     */
    public function fcmSubscribe() {
        header('Content-Type: application/json; charset=utf-8');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'Method not allowed'
            ]);
            return;
        }
        
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'error' => 'Authentication required'
            ]);
            return;
        }
        
        try {
            // Get request data
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['fcm_token'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'FCM token is required'
                ]);
                return;
            }
            
            $userId = $_SESSION['user_id'];
            $fcmToken = trim($input['fcm_token']);
            $userAgent = $input['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // Validate FCM token format
            if (empty($fcmToken) || strlen($fcmToken) < 50) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid FCM token format'
                ]);
                return;
            }
            
            // Save FCM token using NotificationModel
            $result = $this->notificationModel->saveFCMToken($userId, $fcmToken, $userAgent);
            
            if ($result) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("[FCM] Saved FCM token for user {$userId}");
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'FCM token saved successfully',
                    'token_id' => $result
                ]);
            } else {
                throw new Exception('Failed to save FCM token');
            }
            
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[FCM] Subscription error: ' . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Unsubscribe from FCM push notifications
     * Removes FCM token from database when user disables push notifications
     */
    public function fcmUnsubscribe() {
        header('Content-Type: application/json; charset=utf-8');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'Method not allowed'
            ]);
            return;
        }
        
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'error' => 'Authentication required'
            ]);
            return;
        }
        
        try {
            // Get request data
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = $_SESSION['user_id'];
            
            // If specific FCM token provided, remove only that token
            // Otherwise, remove all FCM tokens for the user
            $fcmToken = isset($input['fcm_token']) ? trim($input['fcm_token']) : null;
            
            // Remove FCM token(s) using NotificationModel
            if ($fcmToken) {
                $result = $this->notificationModel->removeFCMToken($userId, $fcmToken);
                $message = 'FCM token removed successfully';
            } else {
                $result = $this->notificationModel->removeAllFCMTokens($userId);
                $message = 'All FCM tokens removed successfully';
            }
            
            if ($result) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    $tokenInfo = $fcmToken ? "specific token" : "all tokens";
                    error_log("[FCM] Removed {$tokenInfo} for user {$userId}");
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => $message,
                    'tokens_removed' => $result
                ]);
            } else {
                // Not necessarily an error - user might not have had any tokens
                echo json_encode([
                    'success' => true,
                    'message' => 'No FCM tokens found to remove',
                    'tokens_removed' => 0
                ]);
            }
            
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[FCM] Unsubscribe error: ' . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
}
