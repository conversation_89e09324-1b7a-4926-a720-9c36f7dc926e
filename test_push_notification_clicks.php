<?php
/**
 * Test Push Notification Clicks
 * 
 * Tests that push notification clicks properly open the website with direct message links
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>📱 Test Push Notification Clicks</h1>";

try {
    $db = new Database();
    
    echo "<h2>🎯 Push Notification Click Issue - Fixed</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Problem and Solution:</h3>";
    echo "<ul>";
    echo "<li>❌ <strong>Problem:</strong> Push notifications closed when clicked but didn't open website</li>";
    echo "<li>🔍 <strong>Cause:</strong> Service worker wasn't properly handling FCM notification data</li>";
    echo "<li>✅ <strong>Solution:</strong> Enhanced service worker click handling with proper URL navigation</li>";
    echo "<li>🎯 <strong>Result:</strong> Push notifications now open website with direct message links</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔧 Service Worker Improvements</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📝 Enhanced Click Handling:</h3>";
    
    echo "<h4>✅ Before (Broken):</h4>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
    echo "// Basic click handling\n";
    echo "url = event.notification.data?.url || '/';\n";
    echo "clients.openWindow(url); // Didn't work properly";
    echo "</pre>";
    
    echo "<h4>✅ After (Fixed):</h4>";
    echo "<pre style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
    echo "// Enhanced click handling with debugging and fallbacks\n";
    echo "console.log('[SW] Notification data:', event.notification.data);\n\n";
    echo "// Get URL from FCM data\n";
    echo "url = event.notification.data?.url || '/';\n\n";
    echo "// Fallback: construct URL from message_id if needed\n";
    echo "if (url === '/' && event.notification.data?.message_id) {\n";
    echo "    url = '/notification_center/viewMessage/' + event.notification.data.message_id;\n";
    echo "}\n\n";
    echo "// Ensure absolute URL\n";
    echo "if (url.startsWith('/')) {\n";
    echo "    url = self.location.origin + url;\n";
    echo "}\n\n";
    echo "// Try to focus existing window first, then open new\n";
    echo "client.focus();\n";
    echo "client.navigate(url); // Navigate existing window\n";
    echo "// OR\n";
    echo "clients.openWindow(url); // Open new window";
    echo "</pre>";
    
    echo "<h4>🎯 Key Improvements:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Debug logging</strong> - See what data is being received</li>";
    echo "<li>✅ <strong>URL fallback</strong> - Construct URL from message_id if needed</li>";
    echo "<li>✅ <strong>Absolute URLs</strong> - Ensure proper navigation</li>";
    echo "<li>✅ <strong>Window management</strong> - Focus existing or open new</li>";
    echo "<li>✅ <strong>Error handling</strong> - Catch and log navigation errors</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 FCM Data Flow</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔄 Complete Data Flow:</h3>";
    
    echo "<h4>1. UnifiedMessageModel sends notification:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "\$notificationModel->sendPushNotification(\n";
    echo "    \$title,\n";
    echo "    \$message,\n";
    echo "    \$userId,\n";
    echo "    \$icon,\n";
    echo "    \$badge,\n";
    echo "    '/notification_center/viewMessage/' . \$messageId,  // URL\n";
    echo "    [\n";
    echo "        'message_id' => \$messageId,\n";
    echo "        'type' => 'message',\n";
    echo "        'from_user' => \$senderName\n";
    echo "    ]\n";
    echo ");";
    echo "</pre>";
    
    echo "<h4>2. NotificationModel prepares FCM data:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "\$notificationData = array_merge([\n";
    echo "    'url' => \$url,  // Direct message URL\n";
    echo "    'timestamp' => time()\n";
    echo "], \$data);";
    echo "</pre>";
    
    echo "<h4>3. FCM Helper sends to Firebase:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "\$message = [\n";
    echo "    'message' => [\n";
    echo "        'token' => \$fcmToken,\n";
    echo "        'notification' => [\n";
    echo "            'title' => \$title,\n";
    echo "            'body' => \$body\n";
    echo "        ],\n";
    echo "        'data' => \$stringData  // Contains URL and message_id\n";
    echo "    ]\n";
    echo "];";
    echo "</pre>";
    
    echo "<h4>4. Service Worker receives and handles click:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "self.addEventListener('notificationclick', event => {\n";
    echo "    const url = event.notification.data?.url;\n";
    echo "    clients.openWindow(url);  // Opens direct message link\n";
    echo "});";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>🧪 Test Push Notification Clicks</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 How to Test:</h3>";
    
    echo "<h4>🎯 Step 1: Enable Push Notifications</h4>";
    echo "<ol>";
    echo "<li>Make sure you have push notifications enabled in your browser</li>";
    echo "<li>Grant permission when prompted</li>";
    echo "<li>Verify you have an active FCM token</li>";
    echo "</ol>";
    
    echo "<h4>🎯 Step 2: Generate Test Notification</h4>";
    echo "<form method='post' style='margin: 15px 0;'>";
    echo "<button type='submit' name='send_test_push' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Send Test Push Notification</button>";
    echo "</form>";
    
    if (isset($_POST['send_test_push'])) {
        echo "<h4>🔄 Sending Test Push Notification:</h4>";
        
        try {
            // Load required classes
            require_once APPROOT . '/models/NotificationModel.php';
            
            $notificationModel = new NotificationModel();
            
            // Get current user (assuming user ID 1 for testing)
            $testUserId = 1;
            $testMessageId = 999; // Test message ID
            
            $result = $notificationModel->sendPushNotification(
                'Test Push Notification Click',
                'Click this notification to test direct message navigation! 🎯',
                $testUserId,
                '/public/images/icons/icon-192x192.png',
                '/public/images/icons/badge-72x72.png',
                '/notification_center/viewMessage/' . $testMessageId,
                [
                    'message_id' => $testMessageId,
                    'type' => 'test',
                    'from_user' => 'System Test'
                ]
            );
            
            if ($result) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px;'>";
                echo "<p style='color: green;'>✅ <strong>Test notification sent successfully!</strong></p>";
                echo "<p><strong>What to do next:</strong></p>";
                echo "<ol>";
                echo "<li>Wait for the push notification to appear</li>";
                echo "<li>Click on the notification</li>";
                echo "<li>Your browser should open/focus and navigate to: <code>/notification_center/viewMessage/{$testMessageId}</code></li>";
                echo "<li>Check browser console for service worker logs</li>";
                echo "</ol>";
                echo "</div>";
            } else {
                echo "<p style='color: red;'>❌ Failed to send test notification. Check FCM configuration.</p>";
            }
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
            echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
    }
    
    echo "<h4>🎯 Step 3: Test Real Contact Form</h4>";
    echo "<ol>";
    echo "<li>Submit a contact form on your website</li>";
    echo "<li>Wait for the push notification</li>";
    echo "<li>Click the notification</li>";
    echo "<li>Should open directly to that specific message</li>";
    echo "</ol>";
    
    echo "<h4>🔍 Debugging Tips:</h4>";
    echo "<ul>";
    echo "<li><strong>Browser Console:</strong> Check for service worker logs starting with [SW]</li>";
    echo "<li><strong>Network Tab:</strong> Verify FCM requests are successful</li>";
    echo "<li><strong>Application Tab:</strong> Check service worker status and registration</li>";
    echo "<li><strong>Notification Permission:</strong> Ensure notifications are allowed</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🎯 Expected Behavior</h2>";
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ What Should Happen:</h3>";
    
    echo "<table border='1' cellpadding='5' style='width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Platform</th><th>Notification Click Behavior</th><th>Result</th></tr>";
    
    echo "<tr>";
    echo "<td><strong>Windows Desktop</strong></td>";
    echo "<td>Click notification → Browser opens/focuses</td>";
    echo "<td style='color: green;'>✅ Navigate to direct message URL</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Android Chrome</strong></td>";
    echo "<td>Click notification → Chrome app opens</td>";
    echo "<td style='color: green;'>✅ Navigate to direct message URL</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>Android PWA</strong></td>";
    echo "<td>Click notification → PWA opens</td>";
    echo "<td style='color: green;'>✅ Navigate to direct message URL</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td><strong>iOS Safari</strong></td>";
    echo "<td>Click notification → Safari opens</td>";
    echo "<td style='color: green;'>✅ Navigate to direct message URL</td>";
    echo "</tr>";
    
    echo "</table>";
    
    echo "<h3>🎯 Console Logs You Should See:</h3>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
    echo "[SW] Notification clicked: fcm-1234567890\n";
    echo "[SW] Notification data: {url: '/notification_center/viewMessage/123', message_id: '123', type: 'message'}\n";
    echo "[SW] Using URL for navigation: /notification_center/viewMessage/123\n";
    echo "[SW] Final URL for navigation: https://yourdomain.com/notification_center/viewMessage/123\n";
    echo "[SW] Found 1 open windows\n";
    echo "[SW] Focusing existing window and navigating to: https://yourdomain.com/notification_center/viewMessage/123";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>📋 Files Updated</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>File updated to fix push notification clicks:</strong></p>";
    echo "<ul>";
    echo "<li><code>sw.js</code> - Enhanced notification click handling with proper URL navigation</li>";
    echo "</ul>";
    
    echo "<p><strong>Changes made:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Enhanced logging</strong> - Debug notification data and URLs</li>";
    echo "<li>✅ <strong>URL fallback logic</strong> - Construct URL from message_id if needed</li>";
    echo "<li>✅ <strong>Absolute URL handling</strong> - Ensure proper navigation</li>";
    echo "<li>✅ <strong>Window management</strong> - Focus existing window or open new</li>";
    echo "<li>✅ <strong>Error handling</strong> - Catch and log navigation errors</li>";
    echo "</ul>";
    
    echo "<p><strong>What this fixes:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Windows push notifications</strong> now open browser with correct URL</li>";
    echo "<li>✅ <strong>Android push notifications</strong> now open app with correct URL</li>";
    echo "<li>✅ <strong>Direct message navigation</strong> works from push notifications</li>";
    echo "<li>✅ <strong>Proper debugging</strong> to troubleshoot any future issues</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Push notification click test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
