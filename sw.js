/**
 * Service Worker for Rowan Elite Rides Events & Shows PWA
 * Provides offline functionality, caching, push notifications, and background sync
 * FCM is now integrated into this unified service worker
 */

const CACHE_NAME = 'rer-events-v1.0.43-cache-mgmt';
const OFFLINE_URL = '/offline.html';
const API_CACHE_NAME = 'rer-api-cache-v1.0.37-fcm-stable';
const IMAGE_CACHE_NAME = 'rer-images-v1.0.37-fcm-stable';
const FORCE_REFRESH_JS = false; // Disable forced JavaScript refresh to prevent constant updates
const DISABLE_ALL_CACHING = true; // Disable all caching for 100% dynamic site

// Resources to cache immediately
const STATIC_CACHE_URLS = [
    '/',
    '/offline.html',
    '/public/css/style.css',
    '/public/css/racing-navigation.css',
    '/public/css/racing-header.css',
    '/public/css/notifications.css',
    '/public/css/custom-calendar.css',
    '/public/css/monthly-event-chart.css',
    '/public/js/main.js',
    '/public/js/notifications.js',
    '/public/js/custom-calendar.js',
    '/public/js/monthly-event-chart.js',
    '/public/js/timezone-helper.js',
    '/public/js/pwa-features.js',
    '/public/js/camera-banner.js',
    '/manifest.json',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
    /\/api\/events/,
    /\/api\/shows/,
    /\/api\/calendar/,
    /\/api\/registrations/,
    /\/api\/user\/dashboard/
];

// FCM Configuration - now handled in this service worker
const FCM_CONFIG = {
    apiKey: "AIzaSyDA0X6Taio8dFXJLPugvVgaWAFMAHs5AMg",
    authDomain: "rowaneliterides.firebaseapp.com",
    projectId: "rowaneliterides",
    storageBucket: "rowaneliterides.firebasestorage.app",
    messagingSenderId: "310533125467",
    appId: "1:310533125467:web:7e819bc634ea3f37bf167e"
};

// Import Firebase scripts for FCM
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Background sync tags
const SYNC_TAGS = {
    REGISTRATION: 'registration-sync',
    PAYMENT: 'payment-sync',
    SCORING: 'scoring-sync',
    NOTIFICATION_SUBSCRIPTION: 'notification-subscription-sync'
};

// Initialize Firebase for FCM
let messaging = null;
try {
    firebase.initializeApp(FCM_CONFIG);
    messaging = firebase.messaging();
    console.log('[SW] Firebase initialized for FCM');
    
    // Verify pushManager is available
    if (!self.registration || !self.registration.pushManager) {
        console.error('[SW] PushManager not available in service worker');
    } else {
        console.log('[SW] PushManager available in service worker');
    }
} catch (error) {
    console.error('[SW] Firebase initialization failed:', error);
}

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('[SW] Installing service worker...');

    // Skip caching if disabled
    if (DISABLE_ALL_CACHING) {
        console.log('[SW] Caching disabled - skipping cache setup');
        // Don't skip waiting automatically - let user choose when to update
        return;
    }

    event.waitUntil(
        Promise.all([
            // Cache static resources
            caches.open(CACHE_NAME).then(cache => {
                console.log('[SW] Caching static resources');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            // Don't skip waiting automatically - let user choose when to update
        ])
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('[SW] Activating service worker...');
    
    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME && 
                            cacheName !== API_CACHE_NAME && 
                            cacheName !== IMAGE_CACHE_NAME) {
                            console.log('[SW] Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            // Take control of all clients
            self.clients.claim()
        ])
    );
});

// Fetch event - handle network requests with caching strategies
self.addEventListener('fetch', event => {
    // Skip caching if disabled
    if (DISABLE_ALL_CACHING) {
        return; // Let browser handle all requests normally, no caching
    }

    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests for caching
    if (request.method !== 'GET') {
        return;
    }

    // Only handle requests for our own domain - let external requests (like Facebook) pass through
    if (url.origin !== self.location.origin) {
        console.log('[SW] Skipping external request:', url.origin);
        return;
    }

    // Extra safety: Skip any Facebook-related requests even if they somehow appear as same-origin
    if (url.href.includes('facebook.com') ||
        url.href.includes('fbcdn.net') ||
        url.href.includes('connect.facebook.net') ||
        url.href.includes('graph.facebook.com')) {
        console.log('[SW] Skipping Facebook request:', url.href);
        return;
    }

    // Skip authentication callbacks and sensitive URLs from caching
    if (url.pathname.includes('/auth/') ||
        url.pathname.includes('/facebookCallback') ||
        url.pathname.includes('/facebook-callback') ||
        url.pathname.includes('/callback') ||
        url.pathname.includes('/oauth') ||
        url.pathname.includes('/login') ||
        url.pathname.includes('/logout') ||
        url.pathname.includes('/register') ||
        url.pathname.includes('/forgotPassword') ||
        url.pathname.includes('/resetPassword') ||
        url.searchParams.has('code') ||
        url.searchParams.has('state') ||
        url.searchParams.has('token') ||
        // Facebook-specific URLs that should never be cached
        url.hostname === 'www.facebook.com' ||
        url.hostname === 'facebook.com' ||
        url.hostname === 'm.facebook.com' ||
        url.hostname === 'graph.facebook.com' ||
        url.hostname === 'connect.facebook.net' ||
        url.pathname.includes('/dialog/oauth') ||
        url.pathname.includes('/checkpoint') ||
        url.pathname.includes('/two_factor') ||
        url.pathname.includes('/device_based_login')) {
        // Let these requests go directly to network without caching
        console.log('[SW] Skipping cache for auth/Facebook URL:', url.hostname + url.pathname);
        return;
    }

    // Handle different types of requests with appropriate strategies
    if (url.pathname.startsWith('/api/')) {
        // API requests - Network First with cache fallback
        event.respondWith(handleApiRequest(request));
    } else if (request.destination === 'image') {
        // Images - Cache First with network fallback
        event.respondWith(handleImageRequest(request));
    } else if (url.pathname.includes('/uploads/')) {
        // User uploads - Cache First
        event.respondWith(handleImageRequest(request));
    } else {
        // HTML pages and other resources - Stale While Revalidate
        event.respondWith(handlePageRequest(request));
    }
});

// Handle API requests with Network First strategy
async function handleApiRequest(request) {
    const cache = await caches.open(API_CACHE_NAME);
    
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('[SW] Network failed for API request, trying cache:', request.url);
        
        // Fallback to cache
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline response for critical API endpoints
        if (request.url.includes('/dashboard') || request.url.includes('/events')) {
            return new Response(JSON.stringify({
                error: 'Offline',
                message: 'This data is not available offline',
                cached: false
            }), {
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            });
        }
        
        throw error;
    }
}

// Handle image requests with Cache First strategy
async function handleImageRequest(request) {
    const cache = await caches.open(IMAGE_CACHE_NAME);
    
    // Try cache first
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        // Fetch from network
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache the image
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('[SW] Failed to load image:', request.url);
        
        // Return placeholder image for failed loads
        return new Response(
            '<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f8f9fa"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#6c757d">Image Unavailable</text></svg>',
            { headers: { 'Content-Type': 'image/svg+xml' } }
        );
    }
}

// Handle page requests with authentication-aware caching
async function handlePageRequest(request) {
    const cache = await caches.open(CACHE_NAME);
    const url = new URL(request.url);

    // For navigation requests (main pages), always fetch fresh to ensure auth state is current
    if (request.mode === 'navigate' ||
        url.pathname === '/' ||
        url.pathname.includes('/dashboard') ||
        url.pathname.includes('/home') ||
        url.pathname.includes('/user/') ||
        url.pathname.includes('/admin/')) {

        try {
            // Always fetch fresh for authenticated pages
            const networkResponse = await fetch(request);
            if (networkResponse.ok) {
                // Only cache if it's not an error page
                const responseText = await networkResponse.clone().text();
                if (!responseText.includes('error') && !responseText.includes('login')) {
                    cache.put(request, networkResponse.clone());
                }
            }
            return networkResponse;
        } catch (error) {
            console.log('[SW] Network failed for page request, trying cache:', request.url);

            // Fallback to cache only for non-authenticated pages
            const cachedResponse = await cache.match(request);
            if (cachedResponse) {
                return cachedResponse;
            }

            // Return offline page for navigation requests
            if (request.mode === 'navigate') {
                return cache.match(OFFLINE_URL);
            }

            throw error;
        }
    }

    // For JavaScript files, force fresh fetch if FORCE_REFRESH_JS is enabled
    if (FORCE_REFRESH_JS && (url.pathname.endsWith('.js') || url.pathname.includes('/js/'))) {
        console.log('[SW] Force refreshing JavaScript file:', url.pathname);
        try {
            const networkResponse = await fetch(request);
            if (networkResponse.ok) {
                cache.put(request, networkResponse.clone());
            }
            return networkResponse;
        } catch (error) {
            console.log('[SW] Network failed for JS file, trying cache:', url.pathname);
            const cachedResponse = await cache.match(request);
            if (cachedResponse) {
                return cachedResponse;
            }
            throw error;
        }
    }

    // For other static resources, use stale-while-revalidate
    const cachedResponse = await cache.match(request);

    // Fetch fresh version in background
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(() => {
        // Network failed, return cached version
        if (cachedResponse) {
            return cachedResponse;
        }
        throw new Error('Network failed and no cache available');
    });

    // Return cached version immediately if available, otherwise wait for network
    return cachedResponse || fetchPromise;
}

// Push notification handling
self.addEventListener('push', event => {
    console.log('[SW] Push notification received');
    
    let notificationData = {
        title: 'RER Events',
        body: 'You have a new notification',
        icon: '/public/images/icons/icon-192x192.png',
        badge: '/public/images/icons/badge-72x72.png',
        tag: 'general',
        requireInteraction: false,
        actions: []
    };
    
    if (event.data) {
        try {
            // Try to parse as JSON first
            const data = event.data.json();
            notificationData = { ...notificationData, ...data };
            console.log('[SW] Parsed JSON push data:', data);
            
            // Add appropriate actions based on notification type
            switch (data.type) {
                case 'event_reminder':
                    notificationData.actions = [
                        { action: 'view', title: 'View Event', icon: '/public/images/icons/action-view.png' },
                        { action: 'dismiss', title: 'Dismiss', icon: '/public/images/icons/action-dismiss.png' }
                    ];
                    break;
                case 'registration_update':
                    notificationData.actions = [
                        { action: 'view_registration', title: 'View Registration', icon: '/public/images/icons/action-view.png' },
                        { action: 'dashboard', title: 'Go to Dashboard', icon: '/public/images/icons/action-dashboard.png' }
                    ];
                    break;
                case 'judging_reminder':
                    notificationData.actions = [
                        { action: 'judge', title: 'Start Judging', icon: '/public/images/icons/action-judge.png' },
                        { action: 'later', title: 'Remind Later', icon: '/public/images/icons/action-later.png' }
                    ];
                    break;
            }
        } catch (error) {
            console.log('[SW] Push data is not JSON, treating as plain text:', error.message);
            
            // Handle plain text payload
            try {
                const textData = event.data.text();
                console.log('[SW] Plain text push data:', textData);
                
                // Use the text as the notification body
                notificationData.body = textData;
                notificationData.title = 'RER Events';
                notificationData.tag = 'text-notification-' + Date.now();
                
            } catch (textError) {
                console.error('[SW] Error reading push data as text:', textError);
                // Use default notification data
                notificationData.body = 'You have a new notification (data parsing failed)';
            }
        }
    } else {
        console.log('[SW] Push event has no data, using default notification');
    }
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title, notificationData)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('[SW] Notification clicked:', event.notification.tag);
    
    event.notification.close();
    
    let url = '/';
    
    // Handle different actions
    switch (event.action) {
        case 'view':
        case 'view_registration':
            url = event.notification.data?.url || '/user/dashboard';
            break;
        case 'judge':
            url = '/judge/dashboard';
            break;
        case 'dashboard':
            url = '/user/dashboard';
            break;
        case 'dismiss':
        case 'later':
            return; // Don't open anything
        default:
            // Default click (no action button)
            url = event.notification.data?.url || '/';
    }
    
    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true }).then(clientList => {
            // Try to focus existing window
            for (const client of clientList) {
                if (client.url.includes(url.split('?')[0]) && 'focus' in client) {
                    return client.focus();
                }
            }
            
            // Open new window
            if (clients.openWindow) {
                return clients.openWindow(url);
            }
        })
    );
});

// FCM Background Message Handler
if (messaging) {
    messaging.onBackgroundMessage((payload) => {
        console.log('[SW-FCM] Received background message:', payload);
        
        const notificationTitle = payload.notification?.title || 'RER Events';
        const notificationOptions = {
            body: payload.notification?.body || 'You have a new notification',
            icon: payload.notification?.icon || '/public/images/icons/icon-192x192.png',
            badge: '/public/images/icons/badge-72x72.png',
            tag: payload.data?.tag || 'fcm-notification',
            requireInteraction: false,
            actions: [],
            data: {
                url: payload.data?.url || '/',
                ...payload.data
            },
            vibrate: [200, 100, 200]
        };
        
        // Add actions based on notification type
        if (payload.data?.type) {
            switch (payload.data.type) {
                case 'event_reminder':
                    notificationOptions.actions = [
                        { action: 'view', title: 'View Event', icon: '/public/images/icons/action-view.png' },
                        { action: 'dismiss', title: 'Dismiss', icon: '/public/images/icons/action-dismiss.png' }
                    ];
                    break;
                case 'registration_update':
                    notificationOptions.actions = [
                        { action: 'view_registration', title: 'View Registration', icon: '/public/images/icons/action-view.png' },
                        { action: 'dashboard', title: 'Dashboard', icon: '/public/images/icons/action-dashboard.png' }
                    ];
                    break;
                case 'judging_reminder':
                    notificationOptions.actions = [
                        { action: 'judge', title: 'Start Judging', icon: '/public/images/icons/action-judge.png' },
                        { action: 'later', title: 'Remind Later', icon: '/public/images/icons/action-later.png' }
                    ];
                    break;
            }
        }
        
        // Show notification
        return self.registration.showNotification(notificationTitle, notificationOptions);
    });
} else {
    console.warn('[SW] FCM messaging not available for background message handling');
}

// Message handler for cache management
self.addEventListener('message', event => {
    console.log('[SW] Message received:', event.data);

    if (event.data && event.data.type === 'CLEAR_AUTH_CACHE') {
        event.waitUntil(clearAuthenticationCache());
    } else if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    } else if (event.data && event.data.type === 'CLEAR_ALL_DATA') {
        event.waitUntil(clearAllCacheData());
    } else if (event.data && event.data.type === 'PREPARE_UPDATE') {
        event.waitUntil(prepareForUpdate(event.data.timestamp));
    }
});

// Clear authentication-related cached pages
async function clearAuthenticationCache() {
    try {
        const cache = await caches.open(CACHE_NAME);
        const keys = await cache.keys();

        // Clear cached pages that might contain user-specific content
        const authRelatedUrls = keys.filter(request => {
            const url = new URL(request.url);
            return url.pathname === '/' ||
                   url.pathname.includes('/dashboard') ||
                   url.pathname.includes('/home') ||
                   url.pathname.includes('/user/') ||
                   url.pathname.includes('/admin/');
        });

        await Promise.all(authRelatedUrls.map(request => cache.delete(request)));
        console.log('[SW] Cleared authentication cache for', authRelatedUrls.length, 'pages');
    } catch (error) {
        console.error('[SW] Failed to clear authentication cache:', error);
    }
}

// Clear all cache data (for complete cache clearing)
async function clearAllCacheData() {
    try {
        console.log('[SW] Clearing all cache data...');
        
        // Get all cache names
        const cacheNames = await caches.keys();
        console.log('[SW] Found caches:', cacheNames);
        
        // Delete all caches
        await Promise.all(cacheNames.map(cacheName => {
            console.log('[SW] Deleting cache:', cacheName);
            return caches.delete(cacheName);
        }));
        
        console.log('[SW] All caches cleared successfully');
        
        // Clear any stored data in the service worker
        if (self.indexedDB) {
            try {
                // Clear FCM token database if it exists
                const deleteReq = self.indexedDB.deleteDatabase('fcm_token_details_db');
                deleteReq.onsuccess = () => console.log('[SW] FCM token database cleared');
                deleteReq.onerror = () => console.log('[SW] FCM token database not found or could not be cleared');
            } catch (e) {
                console.warn('[SW] Could not clear FCM database:', e);
            }
        }
        
    } catch (error) {
        console.error('[SW] Failed to clear all cache data:', error);
    }
}

// Prepare for PWA update
async function prepareForUpdate(timestamp) {
    try {
        console.log('[SW] Preparing for PWA update, timestamp:', timestamp);
        
        // Clear all caches to force fresh content
        await clearAllCacheData();
        
        // Set flag in IndexedDB or cache to indicate update is needed
        try {
            const cache = await caches.open('pwa-update-flags');
            const response = new Response(JSON.stringify({
                updateRequired: true,
                timestamp: timestamp,
                clearedAt: Date.now()
            }));
            await cache.put('/pwa-update-flag', response);
            console.log('[SW] PWA update flag set');
        } catch (e) {
            console.warn('[SW] Could not set PWA update flag:', e);
        }
        
        // Skip waiting to activate immediately if there's a new version
        self.skipWaiting();
        
    } catch (error) {
        console.error('[SW] Failed to prepare for update:', error);
    }
}

// Background sync for offline actions
self.addEventListener('sync', event => {
    console.log('[SW] Background sync triggered:', event.tag);

    switch (event.tag) {
        case SYNC_TAGS.REGISTRATION:
            event.waitUntil(syncRegistrations());
            break;
        case SYNC_TAGS.PAYMENT:
            event.waitUntil(syncPayments());
            break;
        case SYNC_TAGS.SCORING:
            event.waitUntil(syncScoring());
            break;
        case SYNC_TAGS.NOTIFICATION_SUBSCRIPTION:
            event.waitUntil(syncNotificationSubscription());
            break;
    }
});

// Sync offline registrations
async function syncRegistrations() {
    try {
        const db = await openIndexedDB();
        const pendingRegistrations = await getFromIndexedDB(db, 'pendingRegistrations');
        
        for (const registration of pendingRegistrations) {
            try {
                const response = await fetch('/api/registrations', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(registration.data)
                });
                
                if (response.ok) {
                    await removeFromIndexedDB(db, 'pendingRegistrations', registration.id);
                    console.log('[SW] Synced registration:', registration.id);
                }
            } catch (error) {
                console.error('[SW] Failed to sync registration:', error);
            }
        }
    } catch (error) {
        console.error('[SW] Registration sync failed:', error);
    }
}

// Sync offline payments
async function syncPayments() {
    try {
        const db = await openIndexedDB();
        const pendingPayments = await getFromIndexedDB(db, 'pendingPayments');
        
        for (const payment of pendingPayments) {
            try {
                const response = await fetch('/api/payments', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payment.data)
                });
                
                if (response.ok) {
                    await removeFromIndexedDB(db, 'pendingPayments', payment.id);
                    console.log('[SW] Synced payment:', payment.id);
                }
            } catch (error) {
                console.error('[SW] Failed to sync payment:', error);
            }
        }
    } catch (error) {
        console.error('[SW] Payment sync failed:', error);
    }
}

// Sync offline scoring
async function syncScoring() {
    try {
        const db = await openIndexedDB();
        const pendingScores = await getFromIndexedDB(db, 'pendingScores');
        
        for (const score of pendingScores) {
            try {
                const response = await fetch('/api/judging/scores', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(score.data)
                });
                
                if (response.ok) {
                    await removeFromIndexedDB(db, 'pendingScores', score.id);
                    console.log('[SW] Synced score:', score.id);
                }
            } catch (error) {
                console.error('[SW] Failed to sync score:', error);
            }
        }
    } catch (error) {
        console.error('[SW] Scoring sync failed:', error);
    }
}

// DISABLED: Old Web Push API sync - now using FCM OAuth v1 instead
async function syncNotificationSubscription() {
    console.log('[SW] Notification sync handled by FCM system');
    return;
    
    /* OLD WEB PUSH CODE DISABLED
    try {
        const registration = await self.registration;
        const subscription = await registration.pushManager.getSubscription();
        
        if (subscription) {
            const response = await fetch('/api/notifications/subscribe', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    subscription: subscription.toJSON(),
                    userAgent: navigator.userAgent
                })
            });
            
            if (response.ok) {
                console.log('[SW] Notification subscription synced');
            }
        }
    } catch (error) {
        console.error('[SW] Notification subscription sync failed:', error);
    }
    */
}

// IndexedDB helpers
function openIndexedDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('REREventsDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = event => {
            const db = event.target.result;
            
            // Create object stores
            if (!db.objectStoreNames.contains('pendingRegistrations')) {
                db.createObjectStore('pendingRegistrations', { keyPath: 'id' });
            }
            if (!db.objectStoreNames.contains('pendingPayments')) {
                db.createObjectStore('pendingPayments', { keyPath: 'id' });
            }
            if (!db.objectStoreNames.contains('pendingScores')) {
                db.createObjectStore('pendingScores', { keyPath: 'id' });
            }
            if (!db.objectStoreNames.contains('cachedData')) {
                db.createObjectStore('cachedData', { keyPath: 'key' });
            }
        };
    });
}

function getFromIndexedDB(db, storeName) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const request = store.getAll();
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
    });
}

function removeFromIndexedDB(db, storeName, id) {
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.delete(id);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
    });
}

// Periodic background sync for data updates
self.addEventListener('periodicsync', event => {
    if (event.tag === 'background-data-sync') {
        event.waitUntil(updateCachedData());
    }
});

async function updateCachedData() {
    try {
        // Update critical data in background
        const endpoints = [
            '/api/events/upcoming',
            '/api/user/dashboard',
            '/api/notifications/unread'
        ];
        
        const cache = await caches.open(API_CACHE_NAME);
        
        for (const endpoint of endpoints) {
            try {
                const response = await fetch(endpoint);
                if (response.ok) {
                    await cache.put(endpoint, response.clone());
                }
            } catch (error) {
                console.log('[SW] Failed to update cached data for:', endpoint);
            }
        }
    } catch (error) {
        console.error('[SW] Background data sync failed:', error);
    }
}

// ===== END OF SERVICE WORKER =====
// FCM functionality is handled by firebase-messaging-sw.js
