<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Notification Settings</h1>
            <p class="text-muted">Configure how and when you receive notifications</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/user/event_subscriptions" class="btn btn-success me-2">
                <i class="fas fa-calendar-check me-2"></i>Event Subscriptions
            </a>
            <a href="<?php echo BASE_URL; ?>/user/profile" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Profile
            </a>
        </div>
    </div>

    <?php flash('notification_preferences'); ?>

    <form method="POST" action="<?php echo BASE_URL; ?>/user/notifications">
        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
        
        <div class="row">
            <!-- Notification Types -->
            <div class="col-lg-6">
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notification Types</h5>
                    </div>
                    <div class="card-body">
                        <!-- Push Notification Setup Banner -->
                        <div id="push-notification-banner" class="alert alert-info alert-dismissible fade show" style="display: none;">
                            <i class="fas fa-bell me-2"></i>
                            <strong>Enable Push Notifications</strong>
                            <p class="mb-2">Get instant notifications about events, registrations, and updates directly in your browser!</p>
                            <button type="button" class="btn btn-sm btn-primary me-2" onclick="enablePushNotifications()">
                                <i class="fas fa-bell me-1"></i>Enable Now
                            </button>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        
                        <?php if (isset($data['global_settings']['email_enabled']) && $data['global_settings']['email_enabled']): ?>
                            <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications"
                                       style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                       <?php echo (isset($data['preferences']->email_notifications) && $data['preferences']->email_notifications == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_notifications">
                                    <i class="fas fa-envelope me-2 text-primary"></i>
                                    <strong>Email Notifications</strong>
                                    <small class="d-block text-muted">Receive notifications via email</small>
                                </label>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($data['global_settings']['sms_enabled']) && $data['global_settings']['sms_enabled']): ?>
                            <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications"
                                       style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                       <?php echo (isset($data['preferences']->sms_notifications) && $data['preferences']->sms_notifications == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_notifications">
                                    <i class="fas fa-sms me-2 text-success"></i>
                                    <strong>SMS Notifications</strong>
                                    <small class="d-block text-muted">Receive notifications via text message</small>
                                </label>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($data['global_settings']['push_enabled']) && $data['global_settings']['push_enabled']): ?>
                            <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                <input class="form-check-input" type="checkbox" id="push_notifications" name="push_notifications"
                                       style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                       <?php echo (isset($data['preferences']->push_notifications) && $data['preferences']->push_notifications == '1') ? 'checked' : ''; ?>
                                       onchange="handlePushNotificationToggle(this)">
                                <label class="form-check-label" for="push_notifications">
                                    <i class="fas fa-bell me-2 text-warning"></i>
                                    <strong>Push Notifications</strong>
                                    <small class="d-block text-muted">Receive browser push notifications</small>
                                </label>
                                
                                <!-- FCM Test Button (only show in debug mode) -->
                                <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="testFCMSubscription()">
                                        <i class="fas fa-test-tube me-1"></i>Test FCM Setup
                                    </button>
                                    <small class="d-block text-muted mt-1">Debug: Manually trigger FCM subscription</small>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Push Notification Troubleshooting Section -->
                        <?php if (isset($data['global_settings']['push_enabled']) && $data['global_settings']['push_enabled']): ?>
                            <div class="card border-warning mb-3">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Push Notification Troubleshooting
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">
                                        <small>
                                            <strong>Not receiving push notifications?</strong> This tool will reset all your push notification subscriptions 
                                            and clear them from your browser. You can then re-enable push notifications to start fresh.
                                        </small>
                                    </p>
                                    
                                    <div class="alert alert-danger" style="font-size: 0.875rem;">
                                        <i class="fas fa-warning me-2"></i>
                                        <strong>Warning:</strong> This will remove push notifications from ALL your devices and browsers. 
                                        You'll need to re-enable push notifications on each device after using this reset.
                                    </div>
                                    
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="showResetConfirmation()">
                                        <i class="fas fa-refresh me-2"></i>Reset Push Subscriptions
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($data['global_settings']['toast_enabled']) && $data['global_settings']['toast_enabled']): ?>
                            <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                <input class="form-check-input" type="checkbox" id="toast_notifications" name="toast_notifications"
                                       style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                       <?php echo (isset($data['preferences']->toast_notifications) && $data['preferences']->toast_notifications == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="toast_notifications">
                                    <i class="fas fa-comment me-2 text-info"></i>
                                    <strong>In-App Notifications</strong>
                                    <small class="d-block text-muted">Show notifications while using the site</small>
                                </label>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" value="<?php echo htmlspecialchars($data['user']->email); ?>" readonly>
                            <div class="form-text">Email address cannot be changed here. Contact support if needed.</div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($data['user']->phone ?? ''); ?>" 
                                   placeholder="+1234567890">
                            <div class="form-text">Required for SMS notifications. Include country code.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Categories -->
            <div class="col-lg-6">
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Notification Categories</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                            <input class="form-check-input" type="checkbox" id="event_reminders" name="event_reminders"
                                   style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                   <?php echo (isset($data['preferences']->event_reminders) && $data['preferences']->event_reminders == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="event_reminders">
                                <i class="fas fa-calendar me-2 text-primary"></i>
                                <strong>Event Reminders</strong>
                                <small class="d-block text-muted">Reminders about upcoming events you're registered for</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                            <input class="form-check-input" type="checkbox" id="registration_updates" name="registration_updates"
                                   style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                   <?php echo (isset($data['preferences']->registration_updates) && $data['preferences']->registration_updates == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="registration_updates">
                                <i class="fas fa-car me-2 text-success"></i>
                                <strong>Registration Updates</strong>
                                <small class="d-block text-muted">Updates about your vehicle registrations</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                            <input class="form-check-input" type="checkbox" id="judging_updates" name="judging_updates"
                                   style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                   <?php echo (isset($data['preferences']->judging_updates) && $data['preferences']->judging_updates == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="judging_updates">
                                <i class="fas fa-gavel me-2 text-warning"></i>
                                <strong>Judging Updates</strong>
                                <small class="d-block text-muted">Notifications about judging progress and results</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                            <input class="form-check-input" type="checkbox" id="award_notifications" name="award_notifications"
                                   style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                   <?php echo (isset($data['preferences']->award_notifications) && $data['preferences']->award_notifications == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="award_notifications">
                                <i class="fas fa-trophy me-2 text-warning"></i>
                                <strong>Award Notifications</strong>
                                <small class="d-block text-muted">Notifications when you win awards</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                            <input class="form-check-input" type="checkbox" id="system_announcements" name="system_announcements"
                                   style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                   <?php echo (isset($data['preferences']->system_announcements) && $data['preferences']->system_announcements == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="system_announcements">
                                <i class="fas fa-bullhorn me-2 text-info"></i>
                                <strong>System Announcements</strong>
                                <small class="d-block text-muted">Important system updates and announcements</small>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Timing Preferences -->
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Reminder Timing</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="reminder_times" class="form-label">When to send event reminders</label>
                            <select class="form-select" id="reminder_times" name="reminder_times">
                                <option value="[1440]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[1440]') ? 'selected' : ''; ?>>
                                    1 day before only
                                </option>
                                <option value="[1440, 60]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[1440, 60]') ? 'selected' : ''; ?>>
                                    1 day and 1 hour before
                                </option>
                                <option value="[1440, 60, 15]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[1440, 60, 15]') ? 'selected' : ''; ?>>
                                    1 day, 1 hour, and 15 minutes before
                                </option>
                                <option value="[2880, 1440, 60]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[2880, 1440, 60]') ? 'selected' : ''; ?>>
                                    2 days, 1 day, and 1 hour before
                                </option>
                                <option value="[10080, 1440]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[10080, 1440]') ? 'selected' : ''; ?>>
                                    1 week and 1 day before
                                </option>
                            </select>
                            <div class="form-text">Choose when you want to receive reminders before events</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i>Save Notification Preferences
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-label {
    cursor: pointer;
}

.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}
</style>

<script>
// Simple toast notification function
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    container.appendChild(toast);
    
    // Initialize Bootstrap toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Initialize FCM Manager if not already available
function initializeFCMManager() {
    if (typeof FCMNotificationManager !== 'undefined' && !window.fcmManager) {
        window.fcmManager = new FCMNotificationManager();
        console.log('FCM Manager initialized for notifications page');
    }
}

// Function to enable push notifications (called from banner button)
function enablePushNotifications() {
    const checkbox = document.getElementById('push_notifications');
    if (checkbox) {
        checkbox.checked = true;
        handlePushNotificationToggle(checkbox);
    }
}

// Check if we should show the push notification banner
function checkPushNotificationStatus() {
    const checkbox = document.getElementById('push_notifications');
    const banner = document.getElementById('push-notification-banner');
    
    if (checkbox && banner) {
        // Show banner if push notifications are not enabled and permission is not granted
        if (!checkbox.checked && Notification.permission !== 'granted') {
            banner.style.display = 'block';
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeFCMManager();
    
    // Check if we should show the push notification banner
    setTimeout(() => {
        checkPushNotificationStatus();
    }, 500);
    
    // Check if FCM manager is available after a short delay
    setTimeout(() => {
        if (!window.fcmManager) {
            console.warn('FCM Manager not available - push notifications may not work');
        } else {
            console.log('FCM Manager ready for notifications page');
        }
    }, 1000);
});

// Test FCM subscription function (for debugging)
function testFCMSubscription() {
    console.log('Testing FCM subscription...');
    
    if (!window.fcmManager) {
        showToast('FCM Manager not available. Please refresh the page.', 'error');
        return;
    }
    
    if (!window.fcmManager.isSupported) {
        showToast('FCM not supported on this device/browser.', 'warning');
        return;
    }
    
    // Show current permission status
    const permission = Notification.permission;
    console.log('Current notification permission:', permission);
    showToast(`Current permission: ${permission}`, 'info');
    
    if (permission === 'granted') {
        // Try to get token
        window.fcmManager.requestPermissionAndGetToken().then(token => {
            if (token) {
                console.log('FCM token obtained:', token);
                showToast('FCM token obtained successfully!', 'success');
            } else {
                console.error('Failed to get FCM token');
                showToast('Failed to get FCM token', 'error');
            }
        }).catch(error => {
            console.error('FCM error:', error);
            showToast('FCM error: ' + error.message, 'error');
        });
    } else {
        // Request permission first
        Notification.requestPermission().then(result => {
            console.log('Permission request result:', result);
            showToast(`Permission ${result}`, result === 'granted' ? 'success' : 'warning');
            
            if (result === 'granted') {
                // Now try to get token
                window.fcmManager.requestPermissionAndGetToken().then(token => {
                    if (token) {
                        console.log('FCM token obtained:', token);
                        showToast('FCM token obtained successfully!', 'success');
                    } else {
                        console.error('Failed to get FCM token');
                        showToast('Failed to get FCM token', 'error');
                    }
                }).catch(error => {
                    console.error('FCM error:', error);
                    showToast('FCM error: ' + error.message, 'error');
                });
            }
        });
    }
}

function handlePushNotificationToggle(checkbox) {
    if (checkbox.checked) {
        // User enabled push notifications, request browser permission and setup FCM
        // This is called from checkbox change event, which is a valid user gesture
        if ('Notification' in window) {
            // Check current permission status
            if (Notification.permission === 'denied') {
                // Permission is permanently denied
                checkbox.checked = false;
                showToast('Push notifications are blocked in your browser. Please enable them in your browser settings and try again.', 'error');
                return;
            }
            
            // Request permission if not already granted
            if (Notification.permission !== 'granted') {
                console.log('[Notifications] Requesting permission from checkbox toggle (user gesture)');
                Notification.requestPermission().then(function(permission) {
                if (permission === 'granted') {
                    console.log('Push notification permission granted');
                    // Trigger FCM subscription
                    if (window.fcmManager) {
                        window.fcmManager.requestPermissionAndGetToken().then(token => {
                            if (token) {
                                console.log('FCM token obtained:', token);
                                // Show success message
                                showToast('Push notifications enabled successfully!', 'success');
                            } else {
                                console.error('Failed to get FCM token');
                                showToast('Failed to setup push notifications. Please try again.', 'error');
                                checkbox.checked = false;
                            }
                        }).catch(error => {
                            console.error('FCM setup error:', error);
                            showToast('Error setting up push notifications: ' + error.message, 'error');
                            checkbox.checked = false;
                        });
                    } else {
                        console.error('FCM Manager not available');
                        showToast('Push notification system not ready. Please refresh the page and try again.', 'warning');
                        checkbox.checked = false;
                    }
                } else {
                    console.log('Push notification permission denied');
                    // Uncheck the box if permission denied
                    checkbox.checked = false;
                    showToast('Push notifications were denied. Please enable them in your browser settings to receive push notifications.', 'warning');
                }
                }).catch(error => {
                    console.error('Permission request error:', error);
                    checkbox.checked = false;
                    showToast('Error requesting notification permission: ' + error.message, 'error');
                });
            } else {
                // Permission already granted, setup FCM directly
                console.log('[Notifications] Permission already granted, setting up FCM subscription');
                if (window.fcmManager) {
                    window.fcmManager.requestPermissionAndGetToken().then(token => {
                        if (token) {
                            console.log('FCM token obtained:', token);
                            showToast('Push notifications enabled successfully!', 'success');
                        } else {
                            console.error('Failed to get FCM token');
                            showToast('Failed to setup push notifications. Please try again.', 'error');
                            checkbox.checked = false;
                        }
                    }).catch(error => {
                        console.error('FCM setup error:', error);
                        showToast('Error setting up push notifications: ' + error.message, 'error');
                        checkbox.checked = false;
                    });
                } else {
                    console.error('FCM Manager not available');
                    showToast('Push notification system not ready. Please refresh the page and try again.', 'warning');
                    checkbox.checked = false;
                }
            }
        } else {
            // Browser doesn't support notifications
            checkbox.checked = false;
            showToast('Your browser does not support push notifications.', 'error');
        }
    } else {
        // User disabled push notifications - clean up everything
        console.log('[Notifications] User disabled push notifications, cleaning up...');
        disablePushNotifications();
    }
}

// Function to disable and clean up push notifications
async function disablePushNotifications() {
    try {
        // 1. Unsubscribe from push notifications
        if ('serviceWorker' in navigator && 'PushManager' in window) {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.getSubscription();
            
            if (subscription) {
                console.log('[Notifications] Unsubscribing from push notifications...');
                await subscription.unsubscribe();
                console.log('[Notifications] Push subscription removed');
            }
        }
        
        // 2. Clear FCM token from server
        if (window.fcmManager && window.fcmManager.fcmToken) {
            try {
                await fetch(`${window.BASE_URL || ''}/api/pwa/fcm-unsubscribe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        fcm_token: window.fcmManager.fcmToken
                    })
                });
                console.log('[Notifications] FCM token removed from server');
            } catch (error) {
                console.error('[Notifications] Error removing FCM token from server:', error);
            }
        }
        
        // 3. Clear local FCM token
        if (window.fcmManager) {
            window.fcmManager.fcmToken = null;
        }
        
        // 4. Clear any stored notification data
        try {
            localStorage.removeItem('fcm_token');
            localStorage.removeItem('notification_permission');
            sessionStorage.removeItem('fcm_token');
            sessionStorage.removeItem('notification_permission');
        } catch (error) {
            console.error('[Notifications] Error clearing storage:', error);
        }
        
        // 5. Show instructions for manual permission reset
        showPermissionResetInstructions();
        
        showToast('Push notifications disabled successfully. All tokens and subscriptions removed.', 'success');
        console.log('[Notifications] Push notifications disabled and cleaned up');
        
    } catch (error) {
        console.error('[Notifications] Error disabling push notifications:', error);
        showToast('Error disabling push notifications: ' + error.message, 'error');
    }
}

// Show instructions for manually resetting browser notification permissions
function showPermissionResetInstructions() {
    // Only show if permission is still granted (meaning user needs to reset it manually)
    if (Notification.permission === 'granted') {
        const modalHtml = `
            <div class="modal fade" id="permissionResetModal" tabindex="-1" aria-labelledby="permissionResetModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title" id="permissionResetModalLabel">
                                <i class="fas fa-info-circle me-2"></i>Complete Push Notification Cleanup
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>Optional:</strong> For complete cleanup, you can also reset browser notification permissions.
                            </div>
                            
                            <p><strong>We've already:</strong></p>
                            <ul class="mb-3">
                                <li>✓ Removed all notification tokens from our servers</li>
                                <li>✓ Unsubscribed from push notifications</li>
                                <li>✓ Cleared local notification data</li>
                            </ul>
                            
                            <p><strong>To completely reset browser permissions (optional):</strong></p>
                            
                            <div class="accordion" id="browserInstructions">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="chromeHeading">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chromeCollapse">
                                            <i class="fab fa-chrome me-2"></i>Chrome / Edge
                                        </button>
                                    </h2>
                                    <div id="chromeCollapse" class="accordion-collapse collapse" data-bs-parent="#browserInstructions">
                                        <div class="accordion-body">
                                            <ol>
                                                <li>Click the <i class="fas fa-lock"></i> lock icon in the address bar</li>
                                                <li>Click "Notifications"</li>
                                                <li>Select "Block" or "Ask"</li>
                                                <li>Refresh this page</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="firefoxHeading">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#firefoxCollapse">
                                            <i class="fab fa-firefox me-2"></i>Firefox
                                        </button>
                                    </h2>
                                    <div id="firefoxCollapse" class="accordion-collapse collapse" data-bs-parent="#browserInstructions">
                                        <div class="accordion-body">
                                            <ol>
                                                <li>Click the <i class="fas fa-shield-alt"></i> shield icon in the address bar</li>
                                                <li>Click the ">" arrow next to "Notifications"</li>
                                                <li>Select "Block" or "Always Ask"</li>
                                                <li>Refresh this page</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="safariHeading">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#safariCollapse">
                                            <i class="fab fa-safari me-2"></i>Safari
                                        </button>
                                    </h2>
                                    <div id="safariCollapse" class="accordion-collapse collapse" data-bs-parent="#browserInstructions">
                                        <div class="accordion-body">
                                            <ol>
                                                <li>Go to Safari → Preferences → Websites</li>
                                                <li>Click "Notifications" in the left sidebar</li>
                                                <li>Find this website and change to "Deny"</li>
                                                <li>Refresh this page</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>When you re-enable push notifications later, you'll be asked for permission again!</strong>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                                <i class="fas fa-check me-2"></i>Got it!
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing modal if present
        const existingModal = document.getElementById('permissionResetModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Show modal after a short delay
        setTimeout(() => {
            const modal = new bootstrap.Modal(document.getElementById('permissionResetModal'));
            modal.show();
            
            // Clean up modal after it's hidden
            document.getElementById('permissionResetModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }, 1000);
    }
}

// Show reset confirmation modal
function showResetConfirmation() {
    const modalHtml = `
        <div class="modal fade" id="resetConfirmModal" tabindex="-1" aria-labelledby="resetConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title" id="resetConfirmModalLabel">
                            <i class="fas fa-exclamation-triangle me-2"></i>Reset Push Subscriptions
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-warning me-2"></i>
                            <strong>This action cannot be undone!</strong>
                        </div>
                        
                        <p><strong>This will:</strong></p>
                        <ul class="mb-3">
                            <li>Remove push notifications from ALL your devices and browsers</li>
                            <li>Clear all stored notification tokens</li>
                            <li>Disable push notifications in your preferences</li>
                            <li>Clear any pending push notifications</li>
                        </ul>
                        
                        <p><strong>After reset, you will need to:</strong></p>
                        <ul class="mb-3">
                            <li>Re-enable push notifications on this page</li>
                            <li>Grant browser permission again on each device</li>
                            <li>Set up notifications on mobile devices again</li>
                        </ul>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmReset" required>
                            <label class="form-check-label" for="confirmReset">
                                I understand this will remove push notifications from all my devices
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-warning" onclick="confirmReset()" id="confirmResetBtn" disabled>
                            <i class="fas fa-refresh me-2"></i>Reset All Subscriptions
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if present
    const existingModal = document.getElementById('resetConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Enable/disable confirm button based on checkbox
    const checkbox = document.getElementById('confirmReset');
    const confirmBtn = document.getElementById('confirmResetBtn');
    
    checkbox.addEventListener('change', function() {
        confirmBtn.disabled = !this.checked;
    });
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('resetConfirmModal'));
    modal.show();
    
    // Clean up modal after it's hidden
    document.getElementById('resetConfirmModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Confirm and execute reset
function confirmReset() {
    const checkbox = document.getElementById('confirmReset');
    if (!checkbox.checked) {
        showToast('Please confirm you understand the consequences', 'warning');
        return;
    }
    
    // Show loading state
    const confirmBtn = document.getElementById('confirmResetBtn');
    const originalText = confirmBtn.innerHTML;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';
    confirmBtn.disabled = true;
    
    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?php echo BASE_URL; ?>/user/resetPushSubscriptions';
    
    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '<?php echo $data['csrf_token']; ?>';
    form.appendChild(csrfInput);
    
    // Add confirmation
    const confirmInput = document.createElement('input');
    confirmInput.type = 'hidden';
    confirmInput.name = 'confirm_reset';
    confirmInput.value = 'yes';
    form.appendChild(confirmInput);
    
    // Submit form
    document.body.appendChild(form);
    form.submit();
}

// Clear browser push subscriptions (client-side cleanup)
function clearBrowserPushSubscriptions() {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
        navigator.serviceWorker.ready.then(function(registration) {
            return registration.pushManager.getSubscription();
        }).then(function(subscription) {
            if (subscription) {
                return subscription.unsubscribe();
            }
        }).then(function(successful) {
            if (successful) {
                console.log('Browser push subscription cleared');
                showToast('Browser push subscription cleared', 'info');
            }
        }).catch(function(error) {
            console.error('Error clearing browser push subscription:', error);
        });
    }
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>