<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Notification Settings</h1>
            <p class="text-muted">Manage notification settings, SMS providers, queue, testing, and installation</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Notification settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error) && !empty($error)) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Notification Settings Sections -->
    <div class="row g-4 mb-5">
        
        <!-- Notification Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-cog text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Settings</h4>
                    </div>
                    <p class="card-text text-muted">Configure notification providers, email settings, and SMS configuration.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/notification_settings" class="stretched-link text-decoration-none">
                        <span class="d-none">View Notification Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Notification Queue Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-list text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Queue</h4>
                    </div>
                    <p class="card-text text-muted">Monitor notification queue, pending notifications, and delivery status.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/notification_queue" class="stretched-link text-decoration-none">
                        <span class="d-none">View Notification Queue</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Test Notifications Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-vial text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Test</h4>
                    </div>
                    <p class="card-text text-muted">Test notification delivery, send test messages, and verify configuration.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/test_notifications" class="stretched-link text-decoration-none">
                        <span class="d-none">Test Notifications</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Install Notifications Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-download text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Install</h4>
                    </div>
                    <p class="card-text text-muted">Install notification system components, database tables, and dependencies.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/installNotifications" class="stretched-link text-decoration-none">
                        <span class="d-none">Install Notifications</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Notification System Information</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>System Status</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check-circle text-success me-2"></i> Notification system installed</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> Database tables created</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> Cron job configured</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Quick Actions</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <a href="<?php echo BASE_URL; ?>/admin/notification_settings" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-cog me-1"></i> Settings
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/notification_queue" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-list me-1"></i> Queue
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/test_notifications" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-vial me-1"></i> Test
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#fcmCleanupModal">
                                    <i class="fas fa-broom me-1"></i> FCM Cleanup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FCM Token Statistics Section -->
    <?php if (isset($fcm_stats) && $fcm_stats): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-mobile-alt text-primary me-2"></i>
                        FCM Push Token Statistics
                    </h5>
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="stat-box">
                                <h3 class="text-primary"><?php echo $fcm_stats->total ?? 0; ?></h3>
                                <p class="text-muted mb-0">Total Tokens</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-box">
                                <h3 class="text-success"><?php echo $fcm_stats->active ?? 0; ?></h3>
                                <p class="text-muted mb-0">Active Tokens</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-box">
                                <h3 class="text-warning"><?php echo $fcm_stats->inactive ?? 0; ?></h3>
                                <p class="text-muted mb-0">Inactive Tokens</p>
                            </div>
                        </div>
                    </div>
                    <?php if (($fcm_stats->inactive ?? 0) > 50): ?>
                    <div class="alert alert-warning mt-3 mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>High number of inactive tokens detected.</strong>
                        Consider running cleanup to improve database performance.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- FCM Cleanup Modal -->
<div class="modal fade" id="fcmCleanupModal" tabindex="-1" aria-labelledby="fcmCleanupModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fcmCleanupModalLabel">
                    <i class="fas fa-broom me-2"></i>FCM Token Cleanup
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Clean up old and unused FCM push notification tokens to improve database performance.</p>

                <div class="row g-3">
                    <!-- Cleanup Inactive Tokens -->
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-clock text-warning fa-2x mb-3"></i>
                                <h6>Cleanup Inactive Tokens</h6>
                                <p class="small text-muted">Remove tokens inactive for specified days</p>
                                <form method="post" action="<?php echo BASE_URL; ?>/admin/fcm_cleanup" class="d-inline">
                                    <input type="hidden" name="action" value="cleanup_inactive">
                                    <div class="mb-2">
                                        <label class="form-label small">Days old:</label>
                                        <input type="number" name="days" value="30" min="1" max="365" class="form-control form-control-sm">
                                    </div>
                                    <button type="submit" class="btn btn-warning btn-sm">
                                        <i class="fas fa-clock me-1"></i>Clean Inactive
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Cleanup Orphaned Tokens -->
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-user-slash text-danger fa-2x mb-3"></i>
                                <h6>Cleanup Orphaned Tokens</h6>
                                <p class="small text-muted">Remove tokens for deleted users</p>
                                <form method="post" action="<?php echo BASE_URL; ?>/admin/fcm_cleanup" class="d-inline">
                                    <input type="hidden" name="action" value="cleanup_orphaned">
                                    <button type="submit" class="btn btn-danger btn-sm">
                                        <i class="fas fa-user-slash me-1"></i>Clean Orphaned
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Cleanup Duplicates -->
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-copy text-info fa-2x mb-3"></i>
                                <h6>Cleanup Duplicates</h6>
                                <p class="small text-muted">Remove duplicate tokens</p>
                                <form method="post" action="<?php echo BASE_URL; ?>/admin/fcm_cleanup" class="d-inline">
                                    <input type="hidden" name="action" value="cleanup_duplicates">
                                    <button type="submit" class="btn btn-info btn-sm">
                                        <i class="fas fa-copy me-1"></i>Clean Duplicates
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Automatic Cleanup:</strong> The system automatically cleans up inactive tokens during token registration (10% chance) and via daily cron job.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php flash('fcm_cleanup'); ?>

<?php require APPROOT . '/views/includes/footer.php'; ?>