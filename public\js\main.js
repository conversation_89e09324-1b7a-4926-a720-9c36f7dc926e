/**
 * Events and Shows Management System
 * Main JavaScript File
 */

// CSRF Token Management
let csrfToken = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('[Main] DOM loaded, initializing...');
    
    // Get CSRF token
    const csrfMeta = document.querySelector('meta[name="csrf-token"]');
    if (csrfMeta) {
        csrfToken = csrfMeta.getAttribute('content');
        console.log('[Main] CSRF token found');
    }

    // Note: FCM initialization is now handled in fcm-notifications.js
    console.log('[Main] Main.js initialization complete');
});

// Function to get CSRF token for AJAX requests
function getCSRFToken() {
    return csrfToken;
}

// Function to manually request notification permission
async function requestNotificationPermission() {
    console.log('[Main] Manual notification permission requested');
    try {
        if (window.fcmManager && typeof window.fcmManager.requestPermissionAndGetToken === 'function') {
            await window.fcmManager.requestPermissionAndGetToken();
        } else {
            console.error('[Main] FCM manager not available or method missing');
        }
    } catch (error) {
        console.error('[Main] Failed to request notification permission:', error);
    }
}
