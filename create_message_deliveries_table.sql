-- Create message_deliveries table for tracking delivery status
-- This table tracks the delivery status of messages sent via different methods

CREATE TABLE IF NOT EXISTS `message_deliveries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `delivery_method` enum('email','push','toast','sms') NOT NULL,
  `status` enum('pending','sent','failed','queued') NOT NULL DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `attempts` int(11) DEFAULT 1,
  `last_attempt` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_message_method` (`message_id`, `delivery_method`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_status` (`status`),
  KEY `idx_delivery_method` (`delivery_method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Show table structure
DESCRIBE message_deliveries;

-- Show any existing data
SELECT COUNT(*) as total_records FROM message_deliveries;

SELECT '✅ message_deliveries table created successfully!' as result;
