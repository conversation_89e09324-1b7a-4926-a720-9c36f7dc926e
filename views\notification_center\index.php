<?php require_once APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>Messages
                        <span class="badge bg-secondary ms-2" id="total-count"><?php echo $counts['total_count']; ?></span>
                    </h2>
                    <p class="text-muted mb-0">Manage your notifications and messages</p>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshPage()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Status Filter Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'all' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=all">
                        <span class="d-none d-md-inline">All Messages</span>
                        <span class="d-md-none"><i class="fas fa-list"></i></span>
                        <span class="badge bg-secondary ms-1" id="all-tab-count">...</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'unread' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=unread">
                        <span class="d-none d-md-inline">Unread</span>
                        <span class="d-md-none"><i class="fas fa-envelope"></i></span>
                        <span class="badge bg-danger ms-1" id="unread-tab-count">...</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $current_status === 'archived' ? 'active' : ''; ?>"
                       href="<?php echo BASE_URL; ?>/notification_center?status=archived">
                        <span class="d-none d-md-inline">Archived</span>
                        <span class="d-md-none"><i class="fas fa-archive"></i></span>
                        <span class="badge bg-secondary ms-1" id="archived-tab-count">...</span>
                    </a>
                </li>
            </ul>

            <!-- Bulk Actions -->
            <?php if ($current_status !== 'archived' && !empty($messages)): ?>
            <div class="row mb-3">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all">
                            <label class="form-check-label" for="select-all">
                                <small class="text-muted">Select all</small>
                            </label>
                        </div>
                        <div id="bulk-actions" style="display: none;">
                            <!-- Desktop bulk actions -->
                            <div class="d-none d-md-block">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-success" onclick="bulkMarkAsRead()">
                                        <i class="fas fa-check me-1"></i>Mark Read
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="bulkArchive()">
                                        <i class="fas fa-archive me-1"></i>Archive
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                                        <i class="fas fa-trash me-1"></i>Delete
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm ms-2" onclick="clearSelection()">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </button>
                                </div>
                            </div>
                            <!-- Mobile bulk actions -->
                            <div class="d-md-none">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="bulkMarkAsRead()" title="Mark Read">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="bulkArchive()" title="Archive">
                                        <i class="fas fa-archive"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="bulkDelete()" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()" title="Clear">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Messages List -->
            <?php if (empty($messages)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No messages found</h4>
                    <p class="text-muted">
                        <?php if ($current_status === 'unread'): ?>
                            You have no unread messages.
                        <?php elseif ($current_status === 'archived'): ?>
                            You have no archived messages.
                        <?php else: ?>
                            You have no messages yet.
                        <?php endif; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="message-list">
                    <?php 
                    // Simple filter: Hide self-reply duplicates and track conversation info
                    $displayedMessages = [];
                    $hiddenSelfReplies = [];
                    $conversationInfo = [];
                    
                    foreach ($messages as $message) {
                        // Check if this is a self-reply (user sent to themselves and it's a reply)
                        if ($message->from_user_id == $user_id && 
                            $message->to_user_id == $user_id && 
                            $message->parent_message_id) {
                            // This is a self-reply - track it for conversation info
                            $hiddenSelfReplies[$message->parent_message_id][] = $message;
                            
                            // Only hide self-replies if their parent message is also in the current result set
                            $parentInResults = false;
                            foreach ($messages as $potentialParent) {
                                if ($potentialParent->id == $message->parent_message_id) {
                                    $parentInResults = true;
                                    break;
                                }
                            }
                            
                            // If parent is not in results, show this self-reply (e.g., in unread tab)
                            if (!$parentInResults) {
                                $displayedMessages[] = $message;
                            }
                        } else {
                            // This is a regular message or original message - show it
                            $displayedMessages[] = $message;
                        }
                    }
                    
                    // Build conversation info for displayed messages
                    foreach ($displayedMessages as $message) {
                        $messageId = $message->id;
                        $selfReplies = isset($hiddenSelfReplies[$messageId]) ? $hiddenSelfReplies[$messageId] : [];
                        
                        $conversationInfo[$messageId] = [
                            'self_replies' => $selfReplies,
                            'self_reply_count' => count($selfReplies),
                            'has_unread_self_replies' => false,
                            'latest_self_reply' => null
                        ];
                        
                        // Check for unread self-replies
                        foreach ($selfReplies as $reply) {
                            if (!$reply->is_read) {
                                $conversationInfo[$messageId]['has_unread_self_replies'] = true;
                            }
                            if (!$conversationInfo[$messageId]['latest_self_reply'] || 
                                strtotime($reply->created_at) > strtotime($conversationInfo[$messageId]['latest_self_reply']->created_at)) {
                                $conversationInfo[$messageId]['latest_self_reply'] = $reply;
                            }
                        }
                        
                        // Don't modify the original message read status - it breaks filtering
                    }
                    ?>
                    
                    <script>
                    // Update conversation counts in tabs
                    document.addEventListener('DOMContentLoaded', function() {
                        // Set all tab counts to conversation counts
                        document.getElementById('all-tab-count').textContent = '<?php echo $conversationCounts['all']; ?>';
                        document.getElementById('unread-tab-count').textContent = '<?php echo $conversationCounts['unread']; ?>';
                        document.getElementById('archived-tab-count').textContent = '<?php echo $conversationCounts['archived']; ?>';
                    });
                    </script>
                    
                    <?php foreach ($displayedMessages as $message): ?>
                        <?php 
                        // Get conversation info for this message
                        $convInfo = isset($conversationInfo[$message->id]) ? $conversationInfo[$message->id] : null;
                        $selfReplies = $convInfo ? $convInfo['self_replies'] : [];
                        $conversationClasses = '';
                        if (!empty($selfReplies)) {
                            $conversationClasses .= ' conversation-thread self-reply-indicator';
                        }
                        ?>
                        <?php 
                        // Determine if this should show as unread (original message unread OR has unread self-replies)
                        $showAsUnread = !$message->is_read || ($convInfo && $convInfo['has_unread_self_replies']);
                        ?>
                        <div class="card mb-2 message-item <?php echo $showAsUnread ? 'message-unread' : ''; ?><?php echo $conversationClasses; ?>"
                             data-message-id="<?php echo $message->id; ?>">
                            <div class="card-body py-2">
                                <!-- Mobile Layout -->
                                <div class="d-md-none">
                                    <div class="d-flex align-items-start">
                                        <!-- Mobile Checkbox (smaller) -->
                                        <?php if ($current_status !== 'archived'): ?>
                                        <div class="flex-shrink-0 me-2">
                                            <input class="form-check-input message-checkbox" type="checkbox"
                                                   value="<?php echo $message->id; ?>" style="margin-top: 2px;">
                                        </div>
                                        <?php endif; ?>

                                        <!-- Mobile Icon (smaller) -->
                                        <div class="flex-shrink-0 me-2">
                                            <?php
                                            $iconClass = 'fas fa-envelope';
                                            $iconColor = 'text-primary';

                                            switch($message->message_type) {
                                                case 'show_notification':
                                                    $iconClass = 'fas fa-car';
                                                    $iconColor = 'text-success';
                                                    break;
                                                case 'system':
                                                    $iconClass = 'fas fa-cog';
                                                    $iconColor = 'text-info';
                                                    break;
                                                case 'reply':
                                                    $iconClass = 'fas fa-reply';
                                                    $iconColor = 'text-warning';
                                                    break;
                                                default:
                                                    $iconClass = 'fas fa-envelope';
                                                    $iconColor = 'text-primary';
                                            }
                                            ?>
                                            <i class="<?php echo $iconClass . ' ' . $iconColor; ?>" style="font-size: 14px;"></i>
                                        </div>

                                        <!-- Mobile Content -->
                                        <div class="flex-grow-1 min-width-0 mobile-message-content">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1 min-width-0">
                                                    <h6 class="mb-1 <?php echo !$message->is_read ? 'fw-bold' : ''; ?>" style="font-size: 14px;">
                                                        <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>"
                                                           class="text-decoration-none text-dark">
                                                            <?php echo htmlspecialchars($message->subject); ?>
                                                        </a>

                                                        <?php if ($showAsUnread): ?>
                                                            <span class="badge bg-danger ms-1" style="font-size: 10px;">New</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted" style="font-size: 12px;">
                                                        <?php echo htmlspecialchars(substr($message->message, 0, 60)); ?>
                                                        <?php if (strlen($message->message) > 60): ?>...<?php endif; ?>
                                                    </p>
                                                    <small class="text-muted" style="font-size: 11px;">
                                                        <?php echo htmlspecialchars($message->from_user_name ?? 'System'); ?>
                                                        • <?php echo date('M j, g:i A', strtotime($message->created_at)); ?>
                                                    </small>
                                                </div>
                                                <div class="flex-shrink-0 ms-2">
                                                    <div class="btn-group-vertical btn-group-sm">
                                                        <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>"
                                                           class="btn btn-outline-primary btn-sm" style="padding: 2px 6px; font-size: 11px;" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if (!$message->is_read): ?>
                                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                                    style="padding: 2px 6px; font-size: 11px;"
                                                                    onclick="markAsRead(<?php echo $message->id; ?>)" title="Mark Read">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <?php if ($current_status === 'archived'): ?>
                                                            <button type="button" class="btn btn-outline-info btn-sm"
                                                                    style="padding: 2px 6px; font-size: 11px;"
                                                                    onclick="unarchiveMessage(<?php echo $message->id; ?>)" title="Restore">
                                                                <i class="fas fa-undo"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                                    style="padding: 2px 6px; font-size: 11px;"
                                                                    onclick="archiveMessage(<?php echo $message->id; ?>)" title="Archive">
                                                                <i class="fas fa-archive"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Desktop Layout -->
                                <div class="d-none d-md-block">
                                    <div class="row align-items-center">
                                        <!-- Checkbox -->
                                        <?php if ($current_status !== 'archived'): ?>
                                        <div class="col-auto">
                                            <div class="form-check">
                                                <input class="form-check-input message-checkbox" type="checkbox"
                                                       value="<?php echo $message->id; ?>">
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <!-- Message Icon -->
                                        <div class="col-auto">
                                            <div class="message-icon">
                                                <i class="<?php echo $iconClass . ' ' . $iconColor; ?>"></i>
                                            </div>
                                        </div>

                                        <!-- Message Content -->
                                        <div class="col">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <h6 class="mb-1 <?php echo !$message->is_read ? 'fw-bold' : ''; ?>">
                                                    <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>" 
                                                       class="text-decoration-none text-dark">
                                                        <?php echo htmlspecialchars($message->subject); ?>
                                                    </a>
                                                    
                                                    <?php if ($showAsUnread): ?>
                                                        <span class="badge bg-danger ms-2">Unread</span>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Simple Self-Reply Indicators -->
                                                    <?php if (!empty($selfReplies)): ?>
                                                        <span class="badge bg-warning ms-2" title="You replied to yourself">
                                                            <i class="fas fa-user-edit me-1"></i><?php echo count($selfReplies); ?> self-repl<?php echo count($selfReplies) == 1 ? 'y' : 'ies'; ?>
                                                        </span>
                                                        
                                                        <?php if ($convInfo && $convInfo['has_unread_self_replies']): ?>
                                                            <span class="badge bg-danger ms-1" title="Unread self-replies">
                                                                <i class="fas fa-exclamation me-1"></i>New replies
                                                            </span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                    

                                                </h6>
                                                <p class="mb-1 text-muted small">
                                                    <?php if (!empty($selfReplies)): ?>
                                                        <i class="fas fa-layer-group me-1 text-warning"></i><strong>Conversation:</strong> 
                                                    <?php endif; ?>
                                                    <?php echo htmlspecialchars(substr($message->message, 0, 100)); ?>
                                                    <?php if (strlen($message->message) > 100): ?>...<?php endif; ?>
                                                    <?php if (!empty($selfReplies)): ?>
                                                        <br><small class="text-warning"><i class="fas fa-info-circle me-1"></i>This includes <?php echo count($selfReplies); ?> of your replies (consolidated view).</small>
                                                    <?php endif; ?>
                                                </p>
                                                <small class="text-muted">
                                                    From: <?php echo htmlspecialchars($message->from_user_name ?? 'System'); ?>
                                                    <?php if (!empty($message->show_title)): ?>
                                                        • Show: <?php echo htmlspecialchars($message->show_title); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <small class="text-muted d-block">
                                                    <?php if (!empty($selfReplies) && $convInfo && $convInfo['latest_self_reply']): ?>
                                                        <i class="fas fa-clock me-1"></i>Latest: <?php echo date('M j, Y g:i A', strtotime($convInfo['latest_self_reply']->created_at)); ?>
                                                        <br><span class="text-muted">Started: <?php echo date('M j, Y g:i A', strtotime($message->created_at)); ?></span>
                                                    <?php else: ?>
                                                        <i class="fas fa-clock me-1"></i><?php echo date('M j, Y g:i A', strtotime($message->created_at)); ?>
                                                    <?php endif; ?>
                                                </small>
                                                
                                                <!-- Action Buttons -->
                                                <div class="btn-group btn-group-sm mt-2" role="group">
                                                    <!-- View Button -->
                                                    <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>" 
                                                       class="btn btn-outline-primary btn-sm" title="View Message">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    
                                                    <!-- Mark as Read (only for unread messages) -->
                                                    <?php if (!$message->is_read): ?>
                                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                                onclick="markAsRead(<?php echo $message->id; ?>)" title="Mark as Read">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Reply (only for messages that allow replies) -->
                                                    <?php if ($message->requires_reply || $message->message_type === 'direct'): ?>
                                                        <a href="<?php echo BASE_URL; ?>/notification_center/viewMessage/<?php echo $message->id; ?>#reply" 
                                                           class="btn btn-outline-info btn-sm" title="Reply">
                                                            <i class="fas fa-reply"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Archive/Delete based on status -->
                                                    <?php if ($current_status === 'archived'): ?>
                                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                                onclick="unarchiveMessage(<?php echo $message->id; ?>)" title="Restore">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                                onclick="deleteMessage(<?php echo $message->id; ?>)" title="Delete Permanently">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                                                onclick="archiveMessage(<?php echo $message->id; ?>)" title="Archive">
                                                            <i class="fas fa-archive"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Messages pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($current_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?status=<?php echo $current_status; ?>&page=<?php echo $current_page - 1; ?>">
                                        Previous
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                                <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?status=<?php echo $current_status; ?>&page=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($current_page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/notification_center?status=<?php echo $current_status; ?>&page=<?php echo $current_page + 1; ?>">
                                        Next
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.message-unread {
    border-left: 5px solid #007bff !important;
    background-color: #e3f2fd !important;
    box-shadow: 0 2px 8px rgba(0,123,255,0.2) !important;
}

.message-unread .card-body {
    background-color: #e3f2fd !important;
}

.message-unread h6,
.message-unread h6 a {
    font-weight: bold !important;
    color: #0056b3 !important;
}

.message-unread .message-icon {
    position: relative;
}

.message-unread .message-icon::after {
    content: "●";
    color: #dc3545;
    font-size: 8px;
    position: absolute;
    top: -2px;
    right: -2px;
    background: white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.message-item {
    transition: all 0.3s ease;
    position: relative;
}

/* Conversation Threading Styles */
.message-item .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.message-item .badge.bg-info {
    background-color: #17a2b8 !important;
}

.message-item .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.message-item .badge.bg-danger {
    background-color: #dc3545 !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Conversation indicators */
.conversation-thread {
    border-left: 3px solid #17a2b8;
    background: linear-gradient(90deg, rgba(23,162,184,0.1) 0%, rgba(255,255,255,0) 20%);
}

.self-reply-indicator {
    border-left: 3px solid #ffc107;
    background: linear-gradient(90deg, rgba(255,193,7,0.1) 0%, rgba(255,255,255,0) 20%);
}
}

.message-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.message-icon {
    width: 40px;
    text-align: center;
}

.message-item .card-body {
    padding: 0.75rem 1rem;
}

.message-item .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
}

.message-item .btn-group-sm .btn i {
    font-size: 0.7rem;
}

@media (max-width: 768px) {
    .message-item .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .message-item .btn {
        margin-bottom: 0.25rem;
    }

    #bulk-actions .btn-group {
        flex-direction: column;
        width: 100%;
    }

    #bulk-actions .btn {
        margin-bottom: 0.25rem;
    }

    /* Mobile-specific optimizations */
    .card-body {
        padding: 0.75rem !important;
    }

    .message-item .card-body {
        padding: 0.5rem !important;
    }

    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 14px;
    }

    .btn-group-vertical .btn {
        margin-bottom: 2px;
    }

    .btn-group-vertical .btn:last-child {
        margin-bottom: 0;
    }

    /* Ensure no horizontal scrolling */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Compact mobile message layout */
    .mobile-message-content {
        line-height: 1.3;
    }

    .mobile-message-content h6 {
        line-height: 1.2;
        margin-bottom: 0.25rem;
    }

    .mobile-message-content p {
        line-height: 1.3;
        margin-bottom: 0.25rem;
    }

    /* Mobile tab icons */
    .nav-tabs .nav-link i {
        font-size: 16px;
    }

    /* Mobile bulk actions */
    .d-md-none .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 12px;
    }

    /* Mobile message cards - more compact */
    .d-md-none .message-item {
        margin-bottom: 0.5rem !important;
    }

    .d-md-none .message-item .card-body {
        padding: 0.5rem !important;
    }
}
</style>

<script>
// Global variables
let selectedMessages = new Set();

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // Select all checkbox
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }

    // Individual message checkboxes
    const messageCheckboxes = document.querySelectorAll('.message-checkbox');
    messageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
}

function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.message-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        if (selectAll.checked) {
            selectedMessages.add(checkbox.value);
        } else {
            selectedMessages.delete(checkbox.value);
        }
    });
    
    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.message-checkbox:checked');
    const bulkActions = document.getElementById('bulk-actions');
    const selectAll = document.getElementById('select-all');
    
    // Update selected messages set
    selectedMessages.clear();
    checkboxes.forEach(cb => selectedMessages.add(cb.value));
    
    // Show/hide bulk actions
    if (bulkActions) {
        bulkActions.style.display = selectedMessages.size > 0 ? 'block' : 'none';
    }
    
    // Update select all checkbox state
    const allCheckboxes = document.querySelectorAll('.message-checkbox');
    if (selectAll && allCheckboxes.length > 0) {
        selectAll.checked = selectedMessages.size === allCheckboxes.length;
        selectAll.indeterminate = selectedMessages.size > 0 && selectedMessages.size < allCheckboxes.length;
    }
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.message-checkbox');
    const selectAll = document.getElementById('select-all');
    
    checkboxes.forEach(checkbox => checkbox.checked = false);
    if (selectAll) selectAll.checked = false;
    
    selectedMessages.clear();
    updateBulkActions();
}

// Individual message actions
function markAsRead(messageId) {
    fetch('<?php echo BASE_URL; ?>/notification_center/markRead', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to mark as read: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to mark message as read');
    });
}

function archiveMessage(messageId) {
    // First get thread info
    fetch('<?php echo BASE_URL; ?>/notification_center/getThreadInfo', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(threadInfo => {
        console.log('Archive - Thread info response:', threadInfo); // Debug
        if (threadInfo.success) {
            let confirmMessage;
            if (threadInfo.total_messages > 1) {
                confirmMessage = `Archive this entire conversation thread (${threadInfo.total_messages} messages)?`;
            } else {
                confirmMessage = 'Archive this message?';
            }
            
            if (!confirm(confirmMessage)) return;
            
            // Proceed with archiving
            fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'message_id=' + messageId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to archive: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to archive thread');
            });
        } else {
            console.log('Archive - Thread info failed:', threadInfo);
            alert('Could not get thread information: ' + (threadInfo.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error getting thread info:', error);
        alert('Failed to get thread information');
    });
}

function deleteMessage(messageId) {
    // First get thread info
    fetch('<?php echo BASE_URL; ?>/notification_center/getThreadInfo', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(threadInfo => {
        console.log('Delete - Thread info response:', threadInfo); // Debug
        if (threadInfo.success) {
            let confirmMessage;
            if (threadInfo.total_messages > 1) {
                confirmMessage = `Permanently delete this entire conversation thread (${threadInfo.total_messages} messages)? This cannot be undone.`;
            } else {
                confirmMessage = 'Permanently delete this message? This cannot be undone.';
            }
            
            if (!confirm(confirmMessage)) return;
            
            // Proceed with deletion
            fetch('<?php echo BASE_URL; ?>/notification_center/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'message_id=' + messageId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to delete: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to delete thread');
            });
        } else {
            console.log('Delete - Thread info failed:', threadInfo);
            alert('Could not get thread information: ' + (threadInfo.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error getting thread info:', error);
        alert('Failed to get thread information');
    });
}

function unarchiveMessage(messageId) {
    if (!confirm('Restore this message to your active list?')) return;
    
    fetch('<?php echo BASE_URL; ?>/notification_center/unarchive', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to restore: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to restore message');
    });
}

// Bulk actions
function bulkMarkAsRead() {
    if (selectedMessages.size === 0) {
        alert('Please select messages to mark as read');
        return;
    }
    
    if (!confirm(`Mark ${selectedMessages.size} message(s) as read?`)) return;
    
    const promises = Array.from(selectedMessages).map(messageId => {
        return fetch('<?php echo BASE_URL; ?>/notification_center/markRead', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'message_id=' + messageId
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const failed = results.filter(r => !r.success);
            if (failed.length === 0) {
                location.reload();
            } else {
                alert(`${failed.length} message(s) could not be marked as read`);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Bulk mark as read failed:', error);
            alert('Some messages could not be marked as read');
        });
}

function bulkArchive() {
    if (selectedMessages.size === 0) {
        alert('Please select messages to archive');
        return;
    }
    
    if (!confirm(`Archive ${selectedMessages.size} message(s)?`)) return;
    
    const promises = Array.from(selectedMessages).map(messageId => {
        return fetch('<?php echo BASE_URL; ?>/notification_center/archive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'message_id=' + messageId
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const failed = results.filter(r => !r.success);
            if (failed.length === 0) {
                location.reload();
            } else {
                alert(`${failed.length} message(s) could not be archived`);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Bulk archive failed:', error);
            alert('Some messages could not be archived');
        });
}

function bulkDelete() {
    if (selectedMessages.size === 0) {
        alert('Please select messages to delete');
        return;
    }
    
    if (!confirm(`Permanently delete ${selectedMessages.size} message(s)? This cannot be undone.`)) return;
    
    const promises = Array.from(selectedMessages).map(messageId => {
        return fetch('<?php echo BASE_URL; ?>/notification_center/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'message_id=' + messageId
        }).then(response => response.json());
    });
    
    Promise.all(promises)
        .then(results => {
            const failed = results.filter(r => !r.success);
            if (failed.length === 0) {
                location.reload();
            } else {
                alert(`${failed.length} message(s) could not be deleted`);
                location.reload();
            }
        })
        .catch(error => {
            console.error('Bulk delete failed:', error);
            alert('Some messages could not be deleted');
        });
}

function refreshPage() {
    location.reload();
}


</script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>