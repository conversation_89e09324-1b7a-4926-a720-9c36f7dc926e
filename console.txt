sw.js:72 [SW] Firebase initialized for FCM
sw.js:498 [SW] Message received: Object
sw.js:537 [SW] Clearing all cache data...
sw.js:541 [SW] Found caches: Array(0)
sw.js:549 [SW] All caches cleared successfully
sw.js:556 [SW] FCM token database cleared
sw.js:498 [SW] Message received: Object
sw.js:537 [SW] Clearing all cache data...
sw.js:541 [SW] Found caches: Array(0)
sw.js:549 [SW] All caches cleared successfully
sw.js:556 [SW] FCM token database cleared
sw.js:498 [SW] Message received: Object
sw.js:537 [SW] Clearing all cache data...
sw.js:541 [SW] Found caches: Array(0)
sw.js:549 [SW] All caches cleared successfully
sw.js:556 [SW] FCM token database cleared
login:483 Bootstrap loaded successfully in footer
mobile-notifications-fix.js:20 🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements
desktop-notifications-fix.js:24 🖥️ Desktop notification fix: Initializing desktop enhancements
desktop-notifications-fix.js:217 🖥️ Desktop notification fix: Loaded successfully
camera-banner.js?v=1752518075:31 CameraBanner constructor completed, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518075:476 CameraBanner loaded - version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518075:43 CameraBanner: Init method called, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518075:50 CameraBanner: Debug mode enabled
camera-banner.js?v=1752518075:51 CameraBanner: BASE_URL = https://events.rowaneliterides.com
camera-banner.js?v=1752518075:54 CameraBanner: About to load site logo
login:512 [PWA] Configuration: {baseUrl: 'https://events.rowaneliterides.com', userId: null, userRole: 'guest', debugMode: true, vapidPublicKey: null}
main.js:56 Initializing Bootstrap components
main.js:217 Initializing global datetime validation...
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
image-viewer.js:520 Image Viewer: Successfully initialized
notifications.js:1128 [NotificationManager] DOM ready, initializing...
notifications.js:40 [NotificationManager] Constructor called
notifications.js:41 [NotificationManager] Base URL: 
notifications.js:42 [NotificationManager] CSRF Token: Present
notifications.js:43 [NotificationManager] Is Mobile: false
notifications.js:65 [NotificationManager] Initializing notification system...
notifications.js:550 [NotificationManager] Polling for unread notifications...
notifications.js:81 [NotificationManager] Polling started - checking every 30 seconds
notifications.js:82 [NotificationManager] Polling interval ID: 6
notifications.js:1131 [NotificationManager] Successfully initialized
desktop-notifications-fix.js:51 🖥️ Desktop: Created toast container
desktop-notifications-fix.js:64 🖥️ Desktop: Enhanced toast container positioning
desktop-notifications-fix.js:113 🖥️ Desktop: NotificationManager found, enhancing...
desktop-notifications-fix.js:176 🖥️ Desktop: NotificationManager enhanced successfully
desktop-notifications-fix.js:203 🖥️ Desktop: Added desktop-specific CSS
pwa-features.js?v=1752518075:17 [PWA] PWAFeatures loaded - version: 3.64.2-cache-mgmt-pwa-update
pwa-features.js?v=1752518075:33 [PWA] Initializing PWA features...
camera-banner.js?v=1752518075:484 CameraBanner: Starting initialization...
camera-banner.js?v=1752518075:105 CameraBanner: Starting to load banners from API...
login:544 [PWA] PWA features initialized successfully
login:1 [DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) <input type=​"password" name=​"password" class=​"form-control " value>​
pwa-features.js?v=1752518075:90 [PWA] Service Worker registered: https://events.rowaneliterides.com/
pwa-features.js?v=1752518075:91 [PWA] Caching disabled - all content will be fetched fresh
camera-banner.js?v=1752518075:92 CameraBanner: Site logo loaded: /uploads/branding/logo_1751468505_rides_logo.png
camera-banner.js?v=1752518075:57 CameraBanner: About to load banners
camera-banner.js?v=1752518075:105 CameraBanner: Starting to load banners from API...
notifications.js:558 [NotificationManager] Received notifications: {push_count: 0, toast_count: 0, toast_notifications: Array(0), push_notifications: Array(0), raw_response: {…}}
notifications.js:574 [NotificationManager] Checking push notifications: {has_push_property: true, push_is_array: true, push_length: 0, push_data: Array(0)}
notifications.js:589 [NotificationManager] No push notifications to send
camera-banner.js?v=1752518075:113 CameraBanner: API response status: 200
camera-banner.js?v=1752518075:120 CameraBanner: API response data: {success: true, banners: Array(5), delay: 3000, count: 5, debug: {…}}
camera-banner.js?v=1752518075:121 CameraBanner: API success: true
camera-banner.js?v=1752518075:122 CameraBanner: API banners array: (5) [{…}, {…}, {…}, {…}, {…}]
camera-banner.js?v=1752518075:123 CameraBanner: API banners is array: true
camera-banner.js?v=1752518075:131 CameraBanner: Loaded 5 banners from API
camera-banner.js?v=1752518075:132 CameraBanner: First banner: {id: -1, type: 'image', text: '', image_path: 'https://events.rowaneliterides.com/uploads/branding/logo_1751468505_rides_logo.png', alt_text: 'Rowan Elite Rides Logo', …}
camera-banner.js?v=1752518075:487 CameraBanner: System initialized with 5 banners
camera-banner.js?v=1752518075:113 CameraBanner: API response status: 200
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
camera-banner.js?v=1752518075:120 CameraBanner: API response data: {success: true, banners: Array(5), delay: 3000, count: 5, debug: {…}}
camera-banner.js?v=1752518075:121 CameraBanner: API success: true
camera-banner.js?v=1752518075:122 CameraBanner: API banners array: (5) [{…}, {…}, {…}, {…}, {…}]
camera-banner.js?v=1752518075:123 CameraBanner: API banners is array: true
camera-banner.js?v=1752518075:131 CameraBanner: Loaded 5 banners from API
camera-banner.js?v=1752518075:132 CameraBanner: First banner: {id: -1, type: 'image', text: '', image_path: 'https://events.rowaneliterides.com/uploads/branding/logo_1751468505_rides_logo.png', alt_text: 'Rowan Elite Rides Logo', …}
camera-banner.js?v=1752518075:60 CameraBanner: Initialization complete, banners loaded: 5
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:525 Image Viewer: Already initialized, rebinding events
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
desktop-notifications-fix.js:172 🖥️ Desktop: Forcing initial notification check...
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notifications.js:550 [NotificationManager] Polling for unread notifications...
notifications.js:558 [NotificationManager] Received notifications: {push_count: 0, toast_count: 0, toast_notifications: Array(0), push_notifications: Array(0), raw_response: {…}}
notifications.js:574 [NotificationManager] Checking push notifications: {has_push_property: true, push_is_array: true, push_length: 0, push_data: Array(0)}
notifications.js:589 [NotificationManager] No push notifications to send
pwa-features.js?v=1752518075:2440 [PWA] Starting Facebook login process
pwa-features.js?v=1752518075:2473 [PWA] Redirecting to Facebook login
Navigated to https://www.facebook.com/privacy/consent/gdp/?params%5Bapp_id%5D=781908744267875&params%5Bfblfb%5D=false&params%5Bkid_directed_site%5D=false&params%5Blogger_id%5D=%229945f1d5-1ca1-4e28-9c8f-942bf5554bf5%22&params%5Bnext%5D=%22confirm%22&params%5Bredirect_uri%5D=%22https%3A%5C%2F%5C%2Fevents.rowaneliterides.com%5C%2Fauth%5C%2FfacebookCallback%22&params%5Breturn_scopes%5D=false&params%5Bscope%5D=%5B%22email%22%2C%22public_profile%22%5D&params%5Bstate%5D=%2241e4f0fbeae467eb4734580c53832b36%22&params%5Bsteps%5D=%7B%7D&params%5Btp%5D=%22unspecified%22&params%5Bcui_gk%5D=%22%5BPASS%5D%3A%22&params%5Bis_limited_login_shim%5D=false&source=gdp_delegated
XHR finished loading: POST "<URL>".
XHR finished loading: POST "<URL>".
XHR finished loading: POST "<URL>".
XHR finished loading: POST "<URL>".
XHR finished loading: POST "<URL>".
XHR finished loading: POST "<URL>".
XHR finished loading: POST "<URL>".
UtRDYSE4Xjx.js:250 
Stop!
UtRDYSE4Xjx.js:250 
This is a browser feature intended for developers. If someone told you to copy-paste something here to enable a Facebook feature or "hack" someone's account, it is a scam and will give them access to your Facebook account.
UtRDYSE4Xjx.js:250 
See https://www.facebook.com/selfxss for more information.
UtRDYSE4Xjx.js:250 

Navigated to https://events.rowaneliterides.com/auth/facebookCallback?code=AQCacf5dRAg-hT85b2QDJ9yWyAvvbh0B1FhJC-DaDr6eubKJx30rqYz_mloEcw_O_ExReRrnMoVMCkIPNsTlgRyl_pFGq9nonjJ03ff5j--JNxN9v8O597OFnZAGeyFGrOY6-jhMQAoXhL4cM1xBVRuj0m4L6wesUVaNoPzEgbzK8S9MPYmhEAJAznyH05PA-tHgyHNAfbU4g5g8Zk5KgGNSnHaE_XcQkDtUNi3QUODL6g8ovoHdMEED5YKe3WVkNQ7B13j7xCJ5Bw6CsvWhb9ZTWu28C9SfVtHwLTesuVgacoGB3gS62_zvvtyuEgMlxjMrFIw8VvX_JZfS_gdTrbbGy9OY6PXiSUjA7rHHptU-zdbvTCWtHK9u8DGxMTM0g6bhfjrjpa5y1Jh3K5fTEMnq&state=41e4f0fbeae467eb4734580c53832b36
Navigated to https://events.rowaneliterides.com/admin/dashboard
dashboard:844 Bootstrap loaded successfully in footer
mobile-notifications-fix.js:20 🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements
desktop-notifications-fix.js:24 🖥️ Desktop notification fix: Initializing desktop enhancements
desktop-notifications-fix.js:217 🖥️ Desktop notification fix: Loaded successfully
camera-banner.js?v=1752518093:31 CameraBanner constructor completed, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518093:476 CameraBanner loaded - version: 3.64.0-cache-bust-fix
dashboard:873 [PWA] Configuration: {baseUrl: 'https://events.rowaneliterides.com', userId: 3, userRole: 'admin', debugMode: true, vapidPublicKey: null}
camera-banner.js?v=1752518093:43 CameraBanner: Init method called, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518093:50 CameraBanner: Debug mode enabled
camera-banner.js?v=1752518093:51 CameraBanner: BASE_URL = https://events.rowaneliterides.com
camera-banner.js?v=1752518093:54 CameraBanner: About to load site logo
fcm-notifications.js:28 [FCM] FCM Notification Manager initialized
fcm-notifications.js:29 [FCM] Is supported: true
notification-permission-manager.js:23 [FCM-Permission] Manager initialized
notification-permission-manager.js:62 [FCM-Permission] FCM Manager found
main.js:56 Initializing Bootstrap components
main.js:217 Initializing global datetime validation...
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
image-viewer.js:520 Image Viewer: Successfully initialized
notifications.js:1128 [NotificationManager] DOM ready, initializing...
notifications.js:40 [NotificationManager] Constructor called
notifications.js:41 [NotificationManager] Base URL: 
notifications.js:42 [NotificationManager] CSRF Token: Present
notifications.js:43 [NotificationManager] Is Mobile: false
notifications.js:65 [NotificationManager] Initializing notification system...
notifications.js:550 [NotificationManager] Polling for unread notifications...
notifications.js:81 [NotificationManager] Polling started - checking every 30 seconds
notifications.js:82 [NotificationManager] Polling interval ID: 10
notifications.js:1131 [NotificationManager] Successfully initialized
desktop-notifications-fix.js:51 🖥️ Desktop: Created toast container
desktop-notifications-fix.js:64 🖥️ Desktop: Enhanced toast container positioning
desktop-notifications-fix.js:113 🖥️ Desktop: NotificationManager found, enhancing...
desktop-notifications-fix.js:176 🖥️ Desktop: NotificationManager enhanced successfully
desktop-notifications-fix.js:203 🖥️ Desktop: Added desktop-specific CSS
pwa-features.js?v=1752518093:17 [PWA] PWAFeatures loaded - version: 3.64.2-cache-mgmt-pwa-update
pwa-features.js?v=1752518093:33 [PWA] Initializing PWA features...
camera-banner.js?v=1752518093:484 CameraBanner: Starting initialization...
camera-banner.js?v=1752518093:105 CameraBanner: Starting to load banners from API...
dashboard:905 [PWA] PWA features initialized successfully
pwa-features.js?v=1752518093:90 [PWA] Service Worker registered: https://events.rowaneliterides.com/
pwa-features.js?v=1752518093:91 [PWA] Caching disabled - all content will be fetched fresh
fcm-notifications.js:140 [FCM] Using existing main service worker for FCM: https://events.rowaneliterides.com/
fcm-notifications.js:88 [FCM] Firebase initialized successfully
camera-banner.js?v=1752518093:92 CameraBanner: Site logo loaded: /uploads/branding/logo_1751468505_rides_logo.png
camera-banner.js?v=1752518093:57 CameraBanner: About to load banners
camera-banner.js?v=1752518093:105 CameraBanner: Starting to load banners from API...
camera-banner.js?v=1752518093:113 CameraBanner: API response status: 200
camera-banner.js?v=1752518093:120 CameraBanner: API response data: {success: true, banners: Array(5), delay: 3000, count: 5, debug: {…}}
camera-banner.js?v=1752518093:121 CameraBanner: API success: true
camera-banner.js?v=1752518093:122 CameraBanner: API banners array: (5) [{…}, {…}, {…}, {…}, {…}]
camera-banner.js?v=1752518093:123 CameraBanner: API banners is array: true
camera-banner.js?v=1752518093:131 CameraBanner: Loaded 5 banners from API
camera-banner.js?v=1752518093:132 CameraBanner: First banner: {id: -1, type: 'image', text: '', image_path: 'https://events.rowaneliterides.com/uploads/branding/logo_1751468505_rides_logo.png', alt_text: 'Rowan Elite Rides Logo', …}
camera-banner.js?v=1752518093:487 CameraBanner: System initialized with 5 banners
notifications.js:558 [NotificationManager] Received notifications: {push_count: 0, toast_count: 0, toast_notifications: Array(0), push_notifications: Array(0), raw_response: {…}}
notifications.js:574 [NotificationManager] Checking push notifications: {has_push_property: true, push_is_array: true, push_length: 0, push_data: Array(0)}
notifications.js:589 [NotificationManager] No push notifications to send
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
camera-banner.js?v=1752518093:113 CameraBanner: API response status: 200
camera-banner.js?v=1752518093:120 CameraBanner: API response data: {success: true, banners: Array(5), delay: 3000, count: 5, debug: {…}}
camera-banner.js?v=1752518093:121 CameraBanner: API success: true
camera-banner.js?v=1752518093:122 CameraBanner: API banners array: (5) [{…}, {…}, {…}, {…}, {…}]
camera-banner.js?v=1752518093:123 CameraBanner: API banners is array: true
camera-banner.js?v=1752518093:131 CameraBanner: Loaded 5 banners from API
camera-banner.js?v=1752518093:132 CameraBanner: First banner: {id: -1, type: 'image', text: '', image_path: 'https://events.rowaneliterides.com/uploads/branding/logo_1751468505_rides_logo.png', alt_text: 'Rowan Elite Rides Logo', …}
camera-banner.js?v=1752518093:60 CameraBanner: Initialization complete, banners loaded: 5
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:525 Image Viewer: Already initialized, rebinding events
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
desktop-notifications-fix.js:172 🖥️ Desktop: Forcing initial notification check...
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notifications.js:550 [NotificationManager] Polling for unread notifications...
notifications.js:558 [NotificationManager] Received notifications: {push_count: 0, toast_count: 0, toast_notifications: Array(0), push_notifications: Array(0), raw_response: {…}}
notifications.js:574 [NotificationManager] Checking push notifications: {has_push_property: true, push_is_array: true, push_length: 0, push_data: Array(0)}
notifications.js:589 [NotificationManager] No push notifications to send
fcm-notifications.js:195 [FCM] Permission already granted, generating token...
fcm-notifications.js:237 [FCM] Service worker ready for token generation: https://events.rowaneliterides.com/
notification-permission-manager.js:205 [FCM-Permission] Skipping prompt for trigger: dashboard
 Fetch failed loading: POST "https://events.rowaneliterides.com/api/pwa/usage".
window.fetch @ main.js:48
updatePWAUsageData @ dashboard:934
initializePWA @ dashboard:894
Navigated to https://events.rowaneliterides.com/
(index):1200 Bootstrap loaded successfully in footer
mobile-notifications-fix.js:20 🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements
desktop-notifications-fix.js:24 🖥️ Desktop notification fix: Initializing desktop enhancements
desktop-notifications-fix.js:217 🖥️ Desktop notification fix: Loaded successfully
camera-banner.js?v=1752518110:31 CameraBanner constructor completed, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518110:476 CameraBanner loaded - version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518110:43 CameraBanner: Init method called, version: 3.64.0-cache-bust-fix
camera-banner.js?v=1752518110:50 CameraBanner: Debug mode enabled
camera-banner.js?v=1752518110:51 CameraBanner: BASE_URL = https://events.rowaneliterides.com
camera-banner.js?v=1752518110:54 CameraBanner: About to load site logo
(index):1229 [PWA] Configuration: {baseUrl: 'https://events.rowaneliterides.com', userId: 3, userRole: 'admin', debugMode: true, vapidPublicKey: null}
fcm-notifications.js:28 [FCM] FCM Notification Manager initialized
fcm-notifications.js:29 [FCM] Is supported: true
notification-permission-manager.js:23 [FCM-Permission] Manager initialized
notification-permission-manager.js:62 [FCM-Permission] FCM Manager found
main.js:56 Initializing Bootstrap components
main.js:217 Initializing global datetime validation...
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
image-viewer.js:520 Image Viewer: Successfully initialized
notifications.js:1128 [NotificationManager] DOM ready, initializing...
notifications.js:40 [NotificationManager] Constructor called
notifications.js:41 [NotificationManager] Base URL: 
notifications.js:42 [NotificationManager] CSRF Token: Present
notifications.js:43 [NotificationManager] Is Mobile: false
notifications.js:65 [NotificationManager] Initializing notification system...
notifications.js:550 [NotificationManager] Polling for unread notifications...
notifications.js:81 [NotificationManager] Polling started - checking every 30 seconds
notifications.js:82 [NotificationManager] Polling interval ID: 10
notifications.js:1131 [NotificationManager] Successfully initialized
desktop-notifications-fix.js:51 🖥️ Desktop: Created toast container
desktop-notifications-fix.js:64 🖥️ Desktop: Enhanced toast container positioning
desktop-notifications-fix.js:113 🖥️ Desktop: NotificationManager found, enhancing...
desktop-notifications-fix.js:176 🖥️ Desktop: NotificationManager enhanced successfully
desktop-notifications-fix.js:203 🖥️ Desktop: Added desktop-specific CSS
pwa-features.js?v=1752518110:17 [PWA] PWAFeatures loaded - version: 3.64.2-cache-mgmt-pwa-update
pwa-features.js?v=1752518110:33 [PWA] Initializing PWA features...
camera-banner.js?v=1752518110:484 CameraBanner: Starting initialization...
camera-banner.js?v=1752518110:105 CameraBanner: Starting to load banners from API...
(index):1261 [PWA] PWA features initialized successfully
pwa-features.js?v=1752518110:90 [PWA] Service Worker registered: https://events.rowaneliterides.com/
pwa-features.js?v=1752518110:91 [PWA] Caching disabled - all content will be fetched fresh
fcm-notifications.js:140 [FCM] Using existing main service worker for FCM: https://events.rowaneliterides.com/
fcm-notifications.js:88 [FCM] Firebase initialized successfully
camera-banner.js?v=1752518110:92 CameraBanner: Site logo loaded: /uploads/branding/logo_1751468505_rides_logo.png
camera-banner.js?v=1752518110:57 CameraBanner: About to load banners
camera-banner.js?v=1752518110:105 CameraBanner: Starting to load banners from API...
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
camera-banner.js?v=1752518110:113 CameraBanner: API response status: 200
camera-banner.js?v=1752518110:120 CameraBanner: API response data: {success: true, banners: Array(5), delay: 3000, count: 5, debug: {…}}
camera-banner.js?v=1752518110:121 CameraBanner: API success: true
camera-banner.js?v=1752518110:122 CameraBanner: API banners array: (5) [{…}, {…}, {…}, {…}, {…}]
camera-banner.js?v=1752518110:123 CameraBanner: API banners is array: true
camera-banner.js?v=1752518110:131 CameraBanner: Loaded 5 banners from API
camera-banner.js?v=1752518110:132 CameraBanner: First banner: {id: -1, type: 'image', text: '', image_path: 'https://events.rowaneliterides.com/uploads/branding/logo_1751468505_rides_logo.png', alt_text: 'Rowan Elite Rides Logo', …}
camera-banner.js?v=1752518110:487 CameraBanner: System initialized with 5 banners
notifications.js:558 [NotificationManager] Received notifications: {push_count: 0, toast_count: 0, toast_notifications: Array(0), push_notifications: Array(0), raw_response: {…}}
notifications.js:574 [NotificationManager] Checking push notifications: {has_push_property: true, push_is_array: true, push_length: 0, push_data: Array(0)}
notifications.js:589 [NotificationManager] No push notifications to send
camera-banner.js?v=1752518110:113 CameraBanner: API response status: 200
camera-banner.js?v=1752518110:120 CameraBanner: API response data: {success: true, banners: Array(5), delay: 3000, count: 5, debug: {…}}
camera-banner.js?v=1752518110:121 CameraBanner: API success: true
camera-banner.js?v=1752518110:122 CameraBanner: API banners array: (5) [{…}, {…}, {…}, {…}, {…}]
camera-banner.js?v=1752518110:123 CameraBanner: API banners is array: true
camera-banner.js?v=1752518110:131 CameraBanner: Loaded 5 banners from API
camera-banner.js?v=1752518110:132 CameraBanner: First banner: {id: -1, type: 'image', text: '', image_path: 'https://events.rowaneliterides.com/uploads/branding/logo_1751468505_rides_logo.png', alt_text: 'Rowan Elite Rides Logo', …}
camera-banner.js?v=1752518110:60 CameraBanner: Initialization complete, banners loaded: 5
image-viewer.js:510 Image Viewer: Initializing...
image-viewer.js:514 Image Viewer: Found 0 thumbnails
image-viewer.js:525 Image Viewer: Already initialized, rebinding events
image-viewer.js:152 Image Viewer: bindEvents found 0 thumbnails
desktop-notifications-fix.js:172 🖥️ Desktop: Forcing initial notification check...
desktop-notifications-fix.js:166 🖥️ Desktop: Enhanced loadUnreadNotifications called
notifications.js:550 [NotificationManager] Polling for unread notifications...
notifications.js:558 [NotificationManager] Received notifications: {push_count: 0, toast_count: 0, toast_notifications: Array(0), push_notifications: Array(0), raw_response: {…}}
notifications.js:574 [NotificationManager] Checking push notifications: {has_push_property: true, push_is_array: true, push_length: 0, push_data: Array(0)}
notifications.js:589 [NotificationManager] No push notifications to send
fcm-notifications.js:195 [FCM] Permission already granted, generating token...
fcm-notifications.js:237 [FCM] Service worker ready for token generation: https://events.rowaneliterides.com/
