# Changelog

All notable changes to the Events and Shows Management System will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [3.64.3] - 2025-01-27

### Enhanced: Push Notification Complete Cleanup System
- **Complete Cleanup on Disable**: When users disable push notifications, system performs comprehensive cleanup including token removal, browser unsubscription, and local data clearing
- **New FCM Unsubscribe API**: Added `/api/pwa/fcm-unsubscribe` endpoint for proper token removal from server
- **Permission Reset Instructions**: Shows helpful modal with browser-specific instructions for manually resetting notification permissions
- **Fresh Permission Flow**: When re-enabling after permission reset, users get fresh permission prompt for clean setup
- **Enhanced User Feedback**: Proper success/error messages throughout enable/disable process with clear status indicators

### Technical Implementation
- **New API Endpoint**: Added `fcm-unsubscribe` route in `core/App.php` and `fcmUnsubscribe()` method in `PwaController.php`
- **Database Methods**: Added `removeFCMToken()` and `removeAllFCMTokens()` methods in `NotificationModel.php`
- **Enhanced JavaScript**: Complete rewrite of `handlePushNotificationToggle()` and new `disablePushNotifications()` function
- **Browser Compatibility**: Added browser-specific permission reset instructions for Chrome, Firefox, and Safari
- **Error Handling**: Comprehensive error handling and debug logging throughout the cleanup process

## [3.64.2] - 2025-01-27

### Added: Developer Cache Management System
- **Cache Management Card**: Added new cache management card to `/admin/settings_developer`
- **Site-Specific Cache Clearing**: Clears browser data only for events.rowaneliterides.com domain
- **Comprehensive Data Clearing**: Removes cookies, localStorage, sessionStorage, IndexedDB, and service worker cache
- **Warning Modal System**: Detailed confirmation modal explaining what will be cleared
- **User Feedback System**: Toast notifications during the clearing process with loading, success, and error states
- **Automatic Page Reload**: Page automatically reloads after cache clearing completes
- **Development Troubleshooting**: Perfect for development without losing data from other sites
- **Enhanced Developer Experience**: Eliminates need to use browser's "Clear browsing data" which affects all sites

### Technical Implementation
- **JavaScript Cache API Integration**: Uses modern browser APIs for comprehensive cache clearing
- **IndexedDB Cleanup**: Clears common database names including PWA and FCM databases
- **Service Worker Management**: Unregisters service workers and clears cache storage
- **Cookie Domain Targeting**: Specifically targets cookies for the current domain only
- **Error Handling**: Graceful fallbacks for unsupported browser features
- **Bootstrap Modal Integration**: Uses existing Bootstrap modal system for confirmations

## [3.67.0] - 2025-01-27

### Added: Push Notification Subscription Reset Tool

#### **New Features**
- **Push Subscription Reset System**: Added comprehensive reset functionality for users experiencing push notification issues
  - New `resetUserPushSubscriptions()` method in NotificationModel
  - New `getUserPushSubscriptionStatus()` method for subscription diagnostics
  - New `resetPushSubscriptions()` controller method in UserController
  - Added troubleshooting section to `/user/notifications` page

#### **User Interface Enhancements**
- **Clear Warning System**: Added prominent warnings about reset consequences
  - Multi-step confirmation modal with checkbox requirement
  - Detailed explanation of what will be reset and what users need to do after
  - Visual warnings to prevent accidental usage

#### **Database Operations**
- **Comprehensive Reset**: Reset removes all FCM tokens, clears preferences, and cleans notification queue
- **Transaction Safety**: All database operations wrapped in transactions for data integrity
- **Logging**: Debug logging for troubleshooting and monitoring

#### **Browser Integration**
- **Client-Side Cleanup**: JavaScript functions to clear browser push subscriptions
- **Re-subscription Support**: Seamless path for users to re-enable notifications after reset

## [3.66.0] - 2025-01-27

### Major Cleanup: FCM System Optimization
- **Removed Dead Web Push Code**: Cleaned up unused Web Push API components
  - Removed `PwaController::subscribe()` method and `/api/pwa/subscribe` route
  - Commented out `NotificationModel::savePushSubscription()` method
  - Eliminated `push_subscriptions` table references
- **Fixed Browser Permission Violation**: Resolved automatic permission request issue
  - Created `getTokenOnly()` method for token retrieval without permission requests
  - Updated auto-initialization to prevent permission violations
  - Separated permission requests from token generation
- **System Architecture Clarification**: Confirmed FCM OAuth v1 implementation
  - Verified FCM HTTP v1 API with OAuth 2.0 authentication
  - Confirmed service account JSON integration
  - Validated `fcm_tokens` table usage
- **Documentation Updates**: Updated structure.md to reflect cleaned system
- **Testing Tools**: Created `test_fcm_system.php` for system verification
- **Disabled Conflicting PWA System**: Eliminated dual notification system conflicts
  - Disabled `initPushNotifications()` in pwa-features.js
  - Disabled `syncNotificationSubscription()` in sw.js
  - Eliminated 404 errors from `/api/notifications/subscribe` calls

## [3.65.0] - 2025-01-27

### Major Update: FCM Migration
- **Push Notification System Overhaul**: Migrated from Web Push API with VAPID to Firebase Cloud Messaging (FCM) HTTP v1 API
  - **FCM Integration**: Complete implementation of Firebase Cloud Messaging for better reliability and delivery rates
  - **New FCM Helper**: Created `FCMv1Helper` class for OAuth2 authentication and notification sending
  - **Database Schema**: New `fcm_tokens` table for storing FCM registration tokens
  - **Frontend Implementation**: New FCM JavaScript manager with Firebase Web SDK integration
  - **Service Worker**: Firebase messaging service worker for background notifications
  - **API Endpoints**: New FCM subscription endpoint (`/api/notifications/fcm-subscribe.php`)
  - **Migration Tools**: Complete migration script and testing utilities

### Technical Improvements
- **Token Management**: Automatic cleanup of invalid FCM tokens
- **Error Handling**: Improved error handling for notification failures
- **Debug Support**: Enhanced debugging capabilities for FCM troubleshooting
- **Security**: Proper OAuth2 implementation for FCM API authentication
- **Performance**: Better notification delivery rates and reliability

### New Files Added
- `/helpers/fcm_v1_helper.php` - FCM HTTP v1 API helper class
- `/public/js/fcm-notifications.js` - Frontend FCM management
- `/firebase-messaging-sw.js` - Firebase service worker
- `/api/notifications/fcm-subscribe.php` - FCM token subscription endpoint
- `/migrate_to_fcm.php` - Migration script from Web Push to FCM
- `/test_fcm_complete.php` - Complete FCM testing utility
- `/FCM_SETUP_GUIDE.md` - Comprehensive setup guide

### Breaking Changes
- **Notification System**: Users will need to re-enable notifications (FCM tokens are different from Web Push subscriptions)
- **Configuration Required**: Firebase project configuration must be completed (see FCM_SETUP_GUIDE.md)
- **Service Account**: Firebase service account key required for server-side operations

### Migration Notes
- Old `push_subscriptions` table remains for reference but is no longer used
- Users must re-grant notification permissions to receive FCM notifications
- Complete setup guide provided in `FCM_SETUP_GUIDE.md`

## [3.64.1] - 2025-01-27

### Enhanced
- **Registration Page Facebook Integration**: Improved user experience for Facebook users on registration page
  - **Prominent Facebook Notice**: Added eye-catching Facebook login notice with gradient background
  - **Clear Messaging**: Users are informed they don't need to register if they have a Facebook account
  - **Direct Facebook Login**: One-click Facebook login button directly from registration page
  - **Mobile-Responsive Design**: Facebook notice adapts beautifully to mobile devices with centered layout
  - **Enhanced Footer Links**: Added Facebook login link in registration page footer
  - **JavaScript Integration**: Added Facebook login functionality with proper error handling and loading states

### Technical Improvements
- **Mobile-First Design**: Facebook notice uses responsive flexbox layout for optimal mobile experience
- **Visual Enhancement**: Custom CSS styling with Facebook brand colors and hover effects
- **User Experience**: Reduces friction for Facebook users wanting to join the platform
- **Code Organization**: Clean separation of Facebook login functionality with proper backup system

## [3.63.22] - 2025-01-27

### Fixed
- **Bootstrap Loading Issue**: Fixed "Bootstrap is not defined" error on image editor show page
  - **Timing Fix**: Modified debug.js to wait for DOM ready before checking Bootstrap availability
  - **Proper Initialization**: Added Bootstrap availability checking with retry logic and fallback
  - **Manual Dropdown Support**: Added manual dropdown initialization if Bootstrap fails to load
  - **Code Cleanup**: Removed duplicate Bootstrap loading logic from image editor show page
  - **PWA Test Removal**: Removed unnecessary PWA test code that could cause conflicts

### Enhanced
- **Debug Script Improvements**: Enhanced debug.js with better error handling and initialization timing
  - **Bootstrap Detection**: Improved Bootstrap availability detection with multiple retry attempts
  - **Fallback Mechanisms**: Added manual dropdown initialization as fallback for Bootstrap failures
  - **Event Handling**: Better event listener management for dropdown components
  - **Console Logging**: Enhanced debugging output for troubleshooting Bootstrap issues

## [3.63.21] - 2025-01-27

### Enhanced
- **PWA Camera Integration**: Enhanced existing PWA camera modal for direct image editor upload
  - **Vehicle Images Page**: Added "Take Photo" button alongside upload and editor options
  - **Show Image Editor Page**: Added "Take Photo" button to Image Tools section (/image_editor/show/{id})
  - **Direct Upload**: Camera photos now upload directly to image editor with entity context
  - **Seamless Workflow**: Captured photos automatically redirect to image editor for immediate editing
  - **Controller Integration**: Enhanced PWAController with cameraUpload() method for secure photo uploads
  - **Entity Validation**: Proper ownership verification for vehicles, events, and shows
  - **File Handling**: Comprehensive file validation, MIME type checking, and secure storage
  - **Mobile Optimized**: Leverages existing PWA camera modal with banner rotation system

### Fixed
- **PWA Camera Upload Processing**: Now uses existing ImageEditorModel::processImageUpload() for proper image handling
  - **Thumbnail Generation**: Automatic thumbnail creation following site standards
  - **Image Optimization**: Applies configured image optimization settings
  - **Database Integration**: Proper insertion with all required fields and relationships
  - **Primary Image Logic**: Correctly handles primary image assignment and management
  - **File Processing**: Follows established image processing workflows instead of custom upload logic

## [3.63.20] - 2025-01-27

### Enhanced
- **Calendar Settings Reorganization**: Completely reorganized calendar settings for better usability
  - **Individual Settings Pages**: Split large calendar settings into focused individual pages
  - **Calendar Display Settings**: Basic calendar formatting, date formats, and view options
  - **Event Chart Settings**: Monthly Event Chart timeline configuration and behavior
  - **Map Settings**: Map provider configuration, API keys, and location defaults
  - **Map Tools**: Geocoding utilities, batch processing, and map verification tools
  - **Event Image Settings**: Image upload limits, file types, and social sharing options
  - **Overview Page**: Main calendar settings page now shows organized cards for each section
  - **Legacy View Identification**: Marked traditional calendar views as legacy options
  - **Controller Methods**: Added complete form processing for all new settings pages

### Improved
- **Settings Organization**: Each table/section now has its own dedicated page for easier management
- **User Experience**: Focused settings pages reduce complexity and improve navigation
- **Map Tools Integration**: Geocoding tools properly integrated with map settings workflow

## [3.63.21] - 2025-01-27

### Fixed
- **URL Routing**: Enhanced router to handle underscore-to-camelCase method name conversion
  - **Underscore Support**: URLs like `/calendar/manage_clubs` now properly route to `manageClubs()` method
  - **Consistent Routing**: Both hyphenated and underscored URL formats now supported
  - **Method Resolution**: Improved method name resolution for better URL flexibility

## [3.63.19] - 2025-01-27

### Fixed
- **Admin Settings Cards**: Fixed inconsistent card behavior in admin settings dashboard
  - **Notifications Card**: Removed individual buttons, now uses single stretched-link like other cards
  - **Calendar & Map Card**: Removed individual buttons, now uses single stretched-link like other cards
  - **Club Ownership Card**: Removed individual buttons, now uses single stretched-link like other cards
  - **New Settings Pages**: Created dedicated settings pages with organized sections for each card
  - **Controller Methods**: Added `settings_notifications()` and `settings_club_ownership()` methods
  - **Consistent UX**: All admin settings cards now have uniform click behavior
  - **Badge Preservation**: Maintained pending count badge for club ownership requests

### Added
- **Settings Navigation**: New organized settings pages with section-based navigation
  - **Notifications Settings**: Dedicated page with Settings, Queue, Test, and Install sections
  - **Club Ownership Settings**: Dedicated page with View Requests, Statistics, and Quick Actions sections

## [3.63.18] - 2024-12-21

### Fixed
- **Notification Preferences**: Fixed undefined variable error and dark card styling
  - **PHP Error**: Resolved "Undefined variable $user" error on line 190 by passing user_id from controller
  - **Card Styling**: Fixed dark/black cards with unreadable text by updating to consistent styling
  - **Visual Consistency**: Cards now match the styling used in `/user/notifications` with white backgrounds and dark borders
  - **Dark Mode**: Restricted dark mode CSS to only apply to notification modals, not general cards
  - **Hover Effects**: Added consistent hover animations matching other notification pages

### Enhanced
- **Card Design**: Updated notification preferences cards to use `shadow-sm border-0` classes
- **User Experience**: Improved readability and visual consistency across notification pages

## [3.63.17] - 2024-12-20

### Fixed
- **Header Navigation**: Fixed "Create Event" link in Events dropdown menu
  - **Desktop Menu**: Corrected link from `/user/createEvent` to `/calendar/createEvent`
  - **Mobile Menu**: Corrected link from `/user/createEvent` to `/calendar/createEvent`
  - **Functionality**: "Create Event" links now properly navigate to the event creation page
  - **Consistency**: Both desktop and mobile navigation now use the correct route

## [3.63.16] - 2024-12-20

### Enhanced
- **Event Details Navigation**: Improved back button functionality
  - **Smart Back Button**: Replaced "Back to Home" and "Back to Calendar" buttons with single "Back" button
  - **Browser History**: Uses browser's back navigation to return to previous page
  - **Fallback Navigation**: Includes fallback to calendar (logged-in users) or home (guests) if no history
  - **Consistent UX**: Same back button behavior for all users regardless of login status
  - **Better Mobile**: Simplified navigation especially beneficial for mobile users

### Changed
- **Event Page Button**: Single "Back" button replaces conditional "Back to Home"/"Back to Calendar" buttons

## [3.63.15] - 2024-12-20

### Enhanced
- **Calendar Event Navigation**: Eliminated popup modal for event clicks
  - **Direct Navigation**: Event clicks now navigate directly to event details page (/calendar/event/ID)
  - **Improved UX**: Streamlined user experience by removing extra click step
  - **Performance**: Removed unused modal HTML and JavaScript code
  - **Consistency**: Updated both calendar and map views for consistent behavior
  - **Mobile Friendly**: Better mobile experience with direct navigation instead of popups

### Removed
- **Event Click Modal**: Completely removed popup modal functionality from calendar views
- **Modal Dependencies**: Cleaned up unused modal-related code and event handlers

## [3.63.14] - 2024-12-20

### Security
- **Calendar Dropdown Access Control**: Fixed calendar dropdown menu access permissions
  - **Admin-Only Items**: Restricted "Manage Calendars", "Manage Venues", and "Manage Clubs" to admin users only
  - **Login-Required Items**: Restricted "Add Event", "Request Club Ownership", and "Import Events" to logged-in users only
  - **Frontend Protection**: Dropdown items now properly hidden based on user authentication status
  - **Backend Protection**: Added access control checks to corresponding controller methods
  - **Consistent Security**: Aligned with existing access control patterns throughout the system

### Fixed
- **Calendar Controller**: Added missing login checks to createEvent() and import() methods
- **Filter API Access**: Removed authentication requirements from filter endpoints (getStates, getCities, getClubs, getVenues)
- **Guest API Access**: Added filter endpoints to guest methods whitelist in controller constructor
- **Filter View Safety**: Added safety checks for calendar data in advanced filter component
- **Guest Filter Experience**: Fixed JavaScript errors when guests use advanced calendar filters
- **Access Control**: Enhanced security for calendar management and event creation features
- **User Experience**: Improved button and dropdown menu visibility based on user permissions
- **Guest User Experience**: Guests can now use advanced filters and see only calendar view without action buttons

## [3.63.9] - 2025-01-27

### Added
- **QR Code Camera Banner Integration**: Enhanced QR code scanner with dynamic banner rotation
  - **Banner System Integration**: QR scanner modal now displays rotating banners while scanning
  - **Unified Experience**: Banner rotation works identically in both camera capture and QR scanning modes
  - **Enhanced User Experience**: Dynamic content display during QR code scanning operations
  - **Container Initialization**: Fixed banner container setup for QR scanner modal

### Fixed
- **PWA Camera Modal**: Resolved banner rotation integration issues in QR code scanner
  - Fixed banner container initialization for QR scanner modal
  - Ensured consistent banner behavior across all camera modal types
  - Enhanced banner system reliability for mobile PWA usage

### Technical Details
- Updated `pwa-features.js` to include banner rotation in QR scanner modal
- Fixed banner container element selection and initialization
- Ensured proper banner system startup for both camera modes
- Added comprehensive banner system integration testing

### Documentation
- **DEBUG_CODE_CLEANUP.md**: Created comprehensive documentation of debug code requiring cleanup
  - Documented remaining debug code in PWA features
  - Outlined cleanup process for production readiness
  - Identified debug patterns and search methods
  - Provided phase-by-phase cleanup recommendations

### Notes
- Debug code cleanup initiated but not completed - production deployment should complete cleanup
- Banner rotation system now fully functional across all camera modal types
- QR code scanning enhanced with dynamic content display

## [3.63.1] - 2025-01-03

### Fixed
- **PWA Notification System**: Fixed critical errors preventing push notifications from working
  - **API Routing Fix**: Added proper routing for `/api/notifications/vapid-key` and `/api/notifications/subscribe` endpoints
  - **VAPID Key Configuration**: Fixed PWAController to use config-defined VAPID keys instead of hardcoded values
  - **JavaScript Error Handling**: Enhanced error handling in pwa-features.js for undefined VAPID keys
  - **URL Base64 Conversion**: Fixed urlBase64ToUint8Array function to handle null/undefined values gracefully
  - **Missing Dependencies**: Removed non-existent validation_helper.php requirement from PWAController
  - **Enhanced Debugging**: Added comprehensive error logging and validation for push notification setup

### Technical Details
- Fixed 404 errors on VAPID key endpoint by updating API routing in App.php
- Enhanced PWAController.getVapidKey() method with proper error handling and config integration
- Updated pwa-features.js with null checks and improved error messages
- Created migration and testing scripts for PWA database tables
- Added backup system for PWA-related fixes

## [3.63.0] - 2025-01-27

### Added
- **Admin Show Management Dashboard**: Comprehensive show management interface for administrators
  - **New Route**: `/admin/shows` - Main dashboard with filtering, sorting, and search capabilities
  - **Statistics Dashboard**: Real-time metrics including total shows, active shows, registrations, and revenue
  - **Advanced Filtering**: Filter by status (all, upcoming, active, completed, cancelled) with URL persistence
  - **Sorting Options**: Sort by name, date, location, registration count, or creation date
  - **Search Functionality**: Full-text search across show names, locations, descriptions, and coordinators
  - **Pagination Support**: Configurable page sizes (10, 25, 50, 100) with user preference memory
  - **Bulk Operations**: Activate, complete, cancel, or delete multiple shows with CSRF protection
  - **Recent Activities**: Real-time display of recent registrations with time-based tracking
  - **Upcoming Deadlines**: Registration and show deadline alerts with color-coded urgency
  - **Role-Based Access**: Admins see all shows, coordinators see only their assigned shows
  - **Quick Actions Panel**: Direct access to common administrative tasks (moved above table for better UX)
  - **Mobile-Responsive Design**: 
    - Mobile-first responsive layout with adaptive columns
    - Hidden columns on smaller screens with mobile-specific info display
    - Touch-friendly interface with optimized button sizes
    - Responsive statistics cards and navigation
  - **Improved Layout**:
    - Quick Actions moved above the shows table for better accessibility
    - Recent Activities and Upcoming Deadlines moved below table in side-by-side layout
    - Full-width table for better data visibility
    - Responsive pagination with smart page number display
  - **Files Added**:
    - `views/admin/shows.php`: Completely redesigned dashboard view with pagination and mobile support
    - `autobackup/admin_shows_dashboard/`: Complete implementation backup and documentation
  - **Files Modified**:
    - `controllers/AdminController.php`: Enhanced shows() method with pagination, updated helper methods
    - `helpers/format_helper.php`: Added timeAgo() function for relative timestamps
    - `structure.md`: Updated with new admin dashboard features
    - `features.md`: Added comprehensive admin show management feature documentation

## [3.62.9] - 2025-01-27

### Enhanced
- **WYSIWYG Editor Mobile Optimization - Professional Grade**: Complete redesign of mobile WYSIWYG editor interface
  - **Problem**: Mobile toolbar had severe usability issues with horizontal scrolling and poor touch interaction
  - **Solution**: Enterprise-grade mobile interface with professional UX design
  - **Key Improvements**:
    - Professional mobile header with essential tools always visible (Bold, Italic, Underline, Lists)
    - Categorized tool sections: Text Formatting, Alignment & Lists, Insert & Tools
    - Zero horizontal scrolling guaranteed on all mobile devices (320px-640px)
    - Touch-optimized interface with 40px minimum touch targets
    - Smooth animations with proper visual feedback
    - Modern design with shadows, borders, and professional color schemes
    - Landscape orientation optimization
    - Automatic responsive behavior on window resize
  - **Files Modified**:
    - `public/css/wysiwyg-editor.css`: Complete mobile responsive redesign
    - `public/js/wysiwyg-editor.js`: Professional mobile toolbar implementation
  - **Backup**: Original files backed up to `autobackup/wysiwyg_mobile_optimization/`
  - **Impact**: Mobile editing experience now matches enterprise software standards

## [3.62.8] - 2025-01-27

### Fixed
- **System-Wide DateTime Validation Fix**: Fixed datetime validation logic across ALL forms using datetime-local fields
  - **Root Cause**: System-wide issue affecting multiple validation layers across the entire website
    1. Server-side string comparison instead of proper DateTime comparison in multiple controllers
    2. Client-side JavaScript validation with inconsistent logic across different forms
    3. HTML5 form validation interfering with custom validation on all datetime forms
  - **Issue**: Users could not set same-day events with different times (e.g., start: 12:00 PM, end: 3:00 PM) on ANY form
    - Forms would scroll back to end date field without submitting
    - No error messages were shown to users
    - Affected ALL forms: coordinator edit show, calendar events, user create show, etc.
  - **Solution**: 
    - **Global JavaScript**: Implemented system-wide datetime validation that automatically detects and fixes ALL forms with datetime-local fields
    - **Server-side**: Replaced string comparison with DateTime object comparison in CoordinatorController
    - **HTML5**: Added `novalidate` attribute to ALL datetime forms to prevent conflicts
    - **Future-proof**: Any new forms with datetime fields will automatically get proper validation
  - **Impact**: Same-day events with different times now work correctly on ALL forms throughout the website
  - **Files Modified**:
    - `controllers/CoordinatorController.php`: Updated editShow() method validation logic (lines ~3578-3592)
    - `controllers/CalendarController.php`: Updated createEvent() and editEvent() methods with DateTime validation
    - `controllers/UserController.php`: Updated createShow() method with comprehensive DateTime validation
    - `controllers/AdminController.php`: Updated addShow() and editShow() methods with comprehensive DateTime validation
    - `public/js/main.js`: Implemented global datetime validation system with automatic form detection
    - `views/coordinator/edit_show.php`: Added `novalidate` attribute and form submission handler
    - `views/calendar/create_event.php`: Added `novalidate` attribute for HTML5 conflict prevention
    - `views/calendar/edit_event.php`: Added `novalidate` attribute for HTML5 conflict prevention
    - `views/user/create_show.php`: Added `novalidate` attribute for HTML5 conflict prevention
    - `views/admin/shows/add_with_template.php`: Added `novalidate` attribute for HTML5 conflict prevention
    - `views/admin/shows/edit_with_template.php`: Added `novalidate` attribute for HTML5 conflict prevention
  - **Debug Tools Created**:
    - `debug_datetime_issue.php`: Diagnostic tool for datetime conversion testing
    - `test_datetime_validation.php`: Test script for validation logic verification
    - `test_js_datetime_validation.html`: JavaScript validation testing tool
    - `SYSTEM_WIDE_DATETIME_VALIDATION_SUMMARY.md`: Complete documentation of system-wide fix

### Technical Details
- **Global Solution**: Implemented automatic detection and fixing of ALL forms with datetime-local fields
- **Server-side**: Changed from `$data['end_date'] < $data['start_date']` to proper DateTime comparison
- **Client-side**: Global validation system that works on ANY form with datetime fields
- **HTML5**: Automatic addition of `novalidate` attribute to prevent browser validation conflicts
- **Future-proof**: New forms automatically inherit proper datetime validation
- Added comprehensive error handling and debug logging for troubleshooting
- Maintains backward compatibility with existing validation logic
- **Scope**: System-wide fix affecting ALL datetime forms throughout the website

## [3.62.7] - 2025-01-27

### Fixed
- **Admin EditShow Timezone Fix**: Fixed timezone display issue in admin edit show form
  - **Root Cause**: Data merging logic in AdminController was overwriting timezone-converted dates with raw UTC values
  - **Solution**: Added explicit preservation of timezone-converted dates after array merge operations
  - **Impact**: Admin edit show form now correctly displays datetime-local inputs in user's timezone
  - **Files Modified**:
    - `controllers/AdminController.php`: Fixed data merging logic in editShow() method
    - `views/admin/shows/edit_with_template.php`: Enhanced debugging and performance improvements
  - **Debug Tools Created**:
    - `debug_admin_editshow_timezone.php`: Diagnostic tool for timezone conversion testing
    - `test_admin_editshow_timezone.php`: Unit test for timezone helper functions
    - `TIMEZONE_EDITSHOW_FIX_SUMMARY.md`: Comprehensive documentation of the fix

### Technical Details
- Template system now properly receives timezone-converted dates for datetime-local inputs
- Form submission continues to convert user input back to UTC for database storage
- All debug logging now respects DEBUG_MODE constant for improved performance
- Maintains backward compatibility with existing timezone implementation

## [3.62.6] - 2025-01-27

### Fixed
- **PHP Timezone Implementation**: Completed timezone compatibility for all PHP files and embedded JavaScript
  - **Controller Fixes**:
    - **AdminController.php**: Fixed notification stats query to use `UTC_DATE()` instead of `CURDATE()`
    - **LayoutEditorController.php**: Changed `date('Y')` to `gmdate('Y')` for consistent UTC year
  - **View JavaScript Fixes**:
    - **notification_queue.php**: Fixed all notification timestamp displays to use TimezoneHelper
      - Created, scheduled, last_attempt, and updated timestamps now properly convert from UTC to user timezone
      - Added fallback UTC conversion when TimezoneHelper is not available
    - **calendar/create_event.php**: Fixed show date conversion for datetime-local inputs
      - Show start/end dates from database now properly converted from UTC to local timezone
      - Added proper datetime-local formatting for HTML5 inputs
    - **calendar/event.php**: Fixed event date handling for related events
      - Event date range calculation now uses proper timezone conversion
      - Related event sorting and display now converts UTC database dates to local timezone
    - **calendar/map.php**: Fixed event sorting and date formatting
      - Map marker sorting now properly converts UTC dates for chronological ordering
      - Event list sorting uses timezone-aware date comparison
      - formatDate() function now converts UTC database dates to user local timezone
    - **judge/judge.php**: Fixed time string formatting
      - formatTime() function now properly converts UTC database timestamps to user timezone
  - **Database Query Optimization**: 
    - Analytics queries in CoordinatorController.php confirmed correct (using DATE() on UTC timestamps for proper grouping)
    - AdminNotificationController.php already using UTC_DATE() correctly

### Technical Details
- All embedded JavaScript now uses TimezoneHelper.parseMySQLDateTime() with UTC fallbacks
- Notification timestamps display in user's local timezone while maintaining UTC database storage
- Calendar event dates properly converted for form inputs, sorting, and display
- Judge interface time displays now timezone-aware
- Maintained UTC database storage while ensuring user-facing displays use local timezone

## [3.62.5] - 2025-01-27

### Fixed
- **JavaScript Timezone Implementation**: Completed timezone compatibility for all JavaScript files
  - **Monthly Event Chart**: Fixed all event date processing to use TimezoneHelper.parseMySQLDateTime()
    - Event display, sorting, filtering, and drag-and-drop now properly convert UTC database dates to user timezone
    - Added fallback UTC conversion when TimezoneHelper is not available
    - Updated version to 3.48.2 with enhanced timezone implementation
  - **Notification Systems**: Fixed notification timestamp display in both assets/js and public/js
    - formatTimeAgo() functions now properly convert UTC database timestamps to local timezone
    - Maintains accurate "time ago" calculations across different user timezones
  - **Calendar Integration**: Verified custom-calendar.js already has proper timezone handling
    - Existing TimezoneHelper integration confirmed working correctly
    - UTC database date conversion with proper fallbacks already implemented
  - **Form Validation**: Confirmed main.js date validations use appropriate local dates
    - HTML date input validation uses local dates as intended
    - No changes needed for form validation logic

### Technical Details
- All event.start and event.end processing now uses TimezoneHelper.parseMySQLDateTime() with UTC fallbacks
- Notification timestamps properly converted from UTC database storage to user local time
- Preserved appropriate local date usage for navigation, validation, and UI interactions
- Maintained cache busting and debug logging with local timestamps where appropriate

## [3.62.4] - 2025-01-27

### Fixed
- **Timezone Consistency**: Completed systematic timezone standardization across entire application
  - **Storage Functions**: All database storage operations now use `gmdate()` for UTC timestamps
  - **Display Functions**: All user-facing date displays now use timezone helper functions
  - **Backup Directory Creation**: Backup directories now use UTC dates for consistency
  - **Copyright Years**: Footer copyright years now use UTC-based year calculation
  - **Vehicle Year Validation**: Vehicle year validation now uses UTC-based current year
  - **Layout Editor**: Template parsing now uses UTC-based current year
  - **Database Schema**: Default footer template updated to use `gmdate()` for copyright year
  - **Registration Displays**: Check-in times and other registration dates now properly converted to user timezone
  - **Removed Duplicate Includes**: Cleaned up duplicate timezone helper includes (helper is already included globally)

### Technical Details
- Updated all `date()` calls to `gmdate()` for UTC storage operations
- Updated all display dates to use `formatDateTimeForUser()` helper function
- Maintained JavaScript date operations for client-side functionality (cache busting, form initialization)
- Preserved SQL DATE() functions for proper UTC timestamp grouping in analytics
- Ensured consistent UTC storage with proper user timezone display throughout application

## [3.62.3] - 2025-01-27

### Added
- **Complete Timezone Implementation**: Finished comprehensive timezone handling system
  - **Calendar Event Storage**: Events now stored in UTC format in database
    - Calendar event creation converts user input to UTC before storage
    - Calendar event editing converts UTC back to user timezone for display
    - AJAX event updates handle timezone conversion properly
    - Recurrence end dates properly converted to/from UTC
  - **New Helper Function**: Added `convertUTCToUserDateTime()` function
    - Converts UTC database times to user's timezone for form display
    - Supports custom format strings (default: 'Y-m-d\TH:i' for datetime-local inputs)
    - Includes proper error handling and fallbacks
  - **Site-wide Display Updates**: All views now show timezone-aware dates
    - User dashboard shows dates in user's selected timezone
    - Staff dashboard and views show timezone-aware dates
    - Payment views (PayPal, details, listings) show user's timezone
    - Coordinator reports show timezone-aware dates
    - Image editor and QR code views show user's timezone
    - Show vehicle listings show timezone-aware dates

### Fixed
- **Calendar Event Forms**: Fixed timezone handling in event creation/editing forms
  - Event forms now display dates in user's timezone when editing
  - Form submissions properly convert to UTC before database storage
  - Recurrence end date fields handle timezone conversion correctly
- **Event Display**: Fixed event detail page timezone display
  - Events now show times in user's selected timezone with timezone indicator
  - Proper fallback to server time for guests or when conversion fails
  - Recurrence end dates display in user's timezone

### Updated
- **Timezone Implementation Guide**: Updated to reflect completed phases
  - Phase 2 (Creation Forms) marked as completed
  - Phase 3 (Display Views) marked as completed
  - Phase 5 (Testing & Validation) marked as completed
- **Features Documentation**: Updated to include complete timezone implementation
- **Show Controllers**: Fixed AdminController and CoordinatorController show editing
  - Show edit forms now display dates in user's timezone
  - Show creation in ShowController now converts to UTC properly

## [3.62.0] - 2024-12-19

### Added
- **JavaScript Timezone Implementation**: Complete client-side timezone handling system
  - **New Timezone Helper Utility**: Created comprehensive timezone-helper.js (v1.0.0)
    - UTC date parsing for MySQL datetime format
    - Manual time/date formatting to avoid browser locale issues
    - Helper functions for consistent date handling across all JavaScript
    - Test event creation utilities with proper timezone handling
  - **Calendar System Updates**: Enhanced all calendar JavaScript files
    - custom-calendar.js (v3.37.0) - UTC parsing for database datetime strings
    - monthly-event-chart.js (v3.48.0) - Timezone-safe time and date formatting
    - calendar-filters.js (v3.47.0) - Consistent timestamp generation
  - **Form Validation Enhancement**: Updated main.js date validation
    - Proper Date object comparison for start/end date validation
    - Enhanced registration date validation logic
  - **Debug System Updates**: All debug and testing files updated
    - monthly-event-debug.js - Timezone helper integration
    - custom-calendar-debug.js - Enhanced timezone-aware logging
    - debug_week2_issue.js - Added timezone awareness

### Technical Implementation
- **Date Parsing**: Database datetime strings now parsed as UTC instead of local time
- **Time Formatting**: Replaced toLocaleTimeString() and toLocaleDateString() with manual formatting
- **Backward Compatibility**: All changes include fallback mechanisms
- **Integration**: Designed to work alongside existing PHP timezone helper functions
- **Comprehensive Backup**: All modified files backed up to autobackup/timezone_implementation/

### Files Modified
- **JavaScript Files**: 7 files updated with timezone improvements
- **New Files**: 1 comprehensive timezone helper utility created
- **Documentation**: Updated features.md and project documentation

## [3.61.2] - 2025-01-27

### Added
- **Complete Timezone Implementation**: Full timezone standardization across the entire application
  - **Date Input Processing**: All date inputs from forms now converted to UTC before database storage
    - UserController::createShow() - Date fields converted using convertUserDateTimeToUTC()
    - CoordinatorController::createShow() - All date processing standardized to UTC
    - AdminController::createShow() and editShow() - Date inputs converted to UTC
  - **Date Display Conversion**: All date displays converted to user's timezone
    - Admin views (age_weights, judging_metrics, judging, registrations, show_categories) - Using formatDateTimeForUser()
    - Registration views - Event dates displayed in user timezone
    - Notification emails - Dates shown in recipient's timezone with timezone abbreviation
    - User notification preferences - Event dates in user timezone
  - **Date Comparison Standardization**: All date comparisons now use UTC for consistency
    - ShowController - Fan voting date checks use UTC timezone
    - ShowController_new - Registration date checks use UTC timezone
  - **Form Editing Enhancement**: Edit forms now display dates in user's timezone
    - Coordinator edit show form - UTC dates converted back to user timezone for editing
    - User create show form - Timezone information added to date fields
    - Visual timezone indicators added to date/datetime fields
  - **Comprehensive Backup System**: Individual file backups created before each modification
    - All modified files backed up to autobackup/timezone_full_implementation/
    - Detailed backup headers documenting changes made

### Technical Implementation
- **Controllers Updated**: UserController, CoordinatorController, AdminController, ShowController, RegistrationController, NotificationModel (7 files)
- **Views Updated**: All admin views, registration views, user forms, coordinator forms, staff views, form designer views (21 files)
- **Date Processing**: Consistent UTC storage with user timezone display throughout
- **Validation Enhanced**: Date validation now handles UTC conversion properly
- **Error Handling**: Proper fallback for date conversion failures
- **Complete Audit**: 28 total files updated with comprehensive timezone standardization
- **Backup System**: 17 backup files created documenting all changes
- **Verification**: Multiple search patterns confirmed no remaining date formatting issues

## [3.61.1] - 2025-01-27

### Fixed
- **Timezone Standardization**: Removed inconsistent timezone overrides in admin views
  - Fixed AdminController date display inconsistencies
  - Standardized all admin show views to use consistent timezone handling
  - Removed hardcoded timezone overrides that were causing display issues

## [3.61.0] - 2025-01-27

### Added
- **User Timezone Management**: Complete timezone management system for user profiles
  - Added timezone column to users table with default 'America/New_York'
  - Created comprehensive timezone helper functions for USA timezones only
  - Added timezone selection dropdown in user profile with validation
  - Implemented timezone conversion functions for proper date/time display
  - Added visual enhancements with icons and current time display in user's timezone
  - Server-side validation ensures only valid USA timezones are accepted
  - Proper error handling and fallback to default timezone
  - CSRF protection maintained with proper input validation

### Technical Details
- **Database Schema**: Added `timezone` VARCHAR(50) column to `users` table
- **Helper Functions**: New `timezone_helper.php` with conversion and validation functions
- **Form Enhancement**: Enhanced profile form with timezone dropdown and visual indicators
- **Security**: Maintained CSRF protection with proper input validation
- **Version**: Updated application version to 3.61.0

## [3.60.0] - 2025-01-27

### Fixed
- **Local Venue Search**: Fixed venue search in forms to properly search local database instead of external APIs
- **CSRF Token Validation**: Fixed CSRF token validation for AJAX requests using header-based tokens
- **Token Duplication**: Added cleanup for duplicated CSRF tokens in headers
- **Venue Search Scope**: Distinguished between local venue search (forms) and API venue search (new venue creation)
- **Address Search**: Enhanced venue search to include address column in addition to name, city, and state

### Added
- **Dual Google API Key Support**: Added separate API key fields for Google's security requirements
  - **Issue**: Google Places API returning "REQUEST_DENIED - API keys with referer restrictions cannot be used with this API"
  - **Issue**: Google requires different API key restrictions for client-side vs server-side APIs
  - **Solution**: Added separate "Server-Side API Key" field in Map Settings for Google Places & Geocoding APIs
  - Client-Side API Key: Maps JavaScript API with HTTP referrer restrictions
  - Server-Side API Key: Places API + Geocoding API with IP address restrictions
  - Added validation to require both keys when Google is selected as provider
  - Updated venue search and geocoding functions to use appropriate API keys
  - Added informational alert explaining Google's dual API key requirements
  - Updated both `/calendar/mapSettings` and `/admin/settings_calendar` pages
  - Added JavaScript to show/hide server API key field based on provider selection

## [3.59.0] - 2025-01-25

### Enhanced
- **Venue Search Location Biasing**: Improved venue search to prioritize results based on user location
  - **Issue**: Google Maps venue search returning results from other countries instead of USA
  - **Issue**: Venue search not considering user's geographic location for relevant results
  - **Solution**: Added user location biasing to all venue search providers (Google Places, Mapbox, HERE, OpenStreetMap)
  - Enhanced Google Places API with better error handling using cURL instead of file_get_contents()
  - Added comprehensive debug logging for venue search troubleshooting
  - Restricted all venue searches to USA by default with user location biasing
  - Improved search queries to include user's city, state, and zip code when available
  - Added location context to search results for better relevance

## [3.58.0] - 2025-01-27

### Fixed
- **Google Maps Marker Modernization**: Updated deprecated Google Maps markers to modern implementation
  - **Issue**: Google Maps showing deprecation warning for `google.maps.Marker` (deprecated Feb 21, 2024)
  - **Issue**: Google Maps not respecting marker type settings from `/calendar/mapSettings`
  - **Issue**: AdvancedMarkerElement event system requires 'gmp-click' instead of 'click'
  - **Solution**: Migrated to `google.maps.marker.AdvancedMarkerElement` API with proper event handling
  - Added support for all marker types: default (circle), pin, and custom image
  - Added support for marker size, colors, and border settings
  - Updated Google Maps API loading to include marker library
  - Added map ID for AdvancedMarkerElement compatibility
  - Made Google Maps implementation consistent with OpenStreetMap marker settings

- **Map Usability Issues**: Resolved critical navigation and interaction problems
  - **Issue**: No way to reset map view except refreshing the page
  - **Issue**: Mouse wheel zoom not working on Google Maps and other providers
  - **Solution**: Added "Fit to Events" button and enabled scroll wheel zoom
  - Added smart view tracking that resets to current filtered view (not system defaults)
  - Enabled scroll wheel zoom for all map providers (Google Maps, OpenStreetMap, Mapbox, HERE)
  - Button removes search markers and closes info windows for clean reset
  - Automatically captures filtered view after events load for intelligent reset behavior

### Enhanced
- **Map Settings Consistency**: Google Maps now respects all marker customization options
  - Marker type selection (circle, pin, custom image)
  - Marker size configuration
  - Calendar color usage toggle
  - Default marker color setting
  - Border display and styling options
  - Custom marker image URL support

- **Map Navigation Experience**: Improved user interaction and control
  - "Fit to Events" button in map header for easy view reset
  - Mouse wheel zoom enabled across all map providers
  - Smart reset logic that fits to current events rather than system defaults
  - Enhanced zoom controls including double-click, touch, box, and keyboard zoom
  - Automatic view capture after applying filters for better reset behavior

- **Map Performance Optimization**: Eliminated forced reflows and improved rendering performance
  - **Issue**: Browser console showing "Forced reflow while executing JavaScript took 33ms"
  - **Solution**: Implemented batch DOM operations and frame-aligned updates
  - Used DocumentFragment for batch event list creation (single reflow vs multiple)
  - Applied requestAnimationFrame for style changes and scroll operations
  - Replaced innerHTML clearing with efficient replaceChildren() method
  - Optimized timing for map view capture and DOM updates
  - Reduced CPU usage and improved UI responsiveness during event loading

## [3.57.0] - 2025-01-27

### Fixed
- **Google Maps Tile Loading Issues**: Resolved map tiles not loading while pins were visible
  - **Root Cause**: CSRF token injection in `main.js` was interfering with Google Maps API requests
  - **Solution**: Excluded `googleapis.com` and `gstatic.com` domains from CSRF token injection
  - Modified `XMLHttpRequest.prototype.open` to skip CSRF tokens for Google Maps URLs
  - Modified `window.fetch` override to skip CSRF tokens for Google Maps URLs
  - This allows Google Maps internal API calls to work without authentication interference

### Technical
- Enhanced CSRF protection to be more selective about which requests receive tokens
- Improved compatibility between site security and third-party API integrations
- Added URL filtering logic to prevent interference with external API services

## [3.56.0] - 2025-01-27

### Added
- **Calendar Event Venue Modal Integration**: Extended venue creation modal to calendar event pages
  - Implemented venue modal popup on `/calendar/createEvent` page
  - Implemented venue modal popup on `/calendar/editEvent/{id}` page
  - Replaced "Create New Venue" links that opened in new tabs with modal buttons
  - Automatic venue selection after creation with full address population
  - Consistent user experience across all venue creation workflows
  - Reused existing venue modal CSS and JavaScript components
  - Integrated with existing venue search and selection functionality

### Technical
- Added venue modal CSS and JS includes to calendar event forms
- Implemented `initializeVenueModal()` function for calendar pages
- Enhanced venue selection callback to populate all address fields
- Maintained consistency with existing show form implementations

## [3.55.0] - 2025-01-27

### Added
- **Venue Creation Modal**: Implemented full-screen overlay modal for creating venues inline
  - Full browser viewport modal with smooth animations
  - Complete venue form with all fields (name, address, city, state, zip, country, capacity, coordinates, contact info, notes)
  - Automatic venue selection after creation
  - Real-time field validation with red borders and error messages
  - AJAX submission to new API endpoint `/calendar/createVenueApi`
  - Coordinate lookup functionality integrated
  - Mobile-responsive design with accessibility features
  - Replaces "Create New Venue" links that opened new tabs
  - Implemented across all show forms (User Create, Coordinator Edit, Admin Create/Edit)

### Fixed
- **Single Venue Selection**: Enforced single venue selection across all show forms
  - Enhanced venue selection logic to clear previous selections
  - Added centralized `clearVenueSelection()` function
  - Updated help text to clarify single selection behavior
  - Search input clears after venue selection
  - Consistent behavior across all forms

### Technical
- Added `VenueModal` JavaScript class component
- Added venue modal CSS with full-screen overlay styling
- Created API endpoint `CalendarController::createVenueApi()` with JSON responses
- Enhanced venue selection JavaScript with better state management
- Added CSRF protection for venue creation API

## [3.54.0] - 2025-01-27

### Added
- **Venue Search Integration**: Implemented real-time venue search functionality across all show forms
  - Added venue search with autocomplete functionality
  - Integrated with existing calendar venue system
  - Real-time search with debounced AJAX requests
  - Visual venue selection with location details display
  - Backward compatibility with existing location field
  - Added venue creation link for new venues
  - Implemented across User Create, Coordinator Create/Edit, and Admin Create/Edit forms

- **Club Search Integration**: Implemented real-time club search functionality across all show forms
  - Added club search with autocomplete functionality
  - Single club selection mode for show association
  - Real-time search with debounced AJAX requests
  - Visual club selection with description display
  - Integration with existing calendar club system
  - Added club creation functionality via AJAX
  - Implemented across User Create, Coordinator Create/Edit, and Admin Create/Edit forms

### Enhanced
- **CalendarController**: Added new methods for venue and club search functionality
  - Added `getClubById()` method for loading existing club data
  - Enhanced venue and club search with proper error handling
  - Added CSRF protection for all AJAX requests
  - Improved search result formatting and display

- **ClubSearchManager**: Enhanced JavaScript class for better show integration
  - Added single selection mode support
  - Configurable hidden field names
  - Better existing data loading support
  - Improved error handling and user feedback

### Technical
- **Form Template System**: Enhanced to handle special field types (venue/club search)
  - Added special handling for 'location' field in all template forms
  - Added special handling for 'club' field in all template forms
  - Maintained backward compatibility with existing forms
  - Added proper CSS and JavaScript includes for search functionality

## [3.53.2] - 2025-01-27

### Changed
- **Coordinator Edit Show Form**: Made listing fee field hidden in coordinator edit show form
  - Listing fee field is now hidden from coordinators but still submitted with form
  - Registration fee field expanded to full width for better visual balance
  - Preserved all functionality while simplifying coordinator interface
  - Enhanced user experience with cleaner form layout

### Fixed
- **Form Template System**: Added support for hidden field type in coordinator edit show form
  - Fixed "Unknown field type: hidden" error in form template processing
  - Added hidden field case to form field type switch statement
  - Improved form template compatibility with hidden fields

## [3.52.1] - 2024-12-30

### Fixed
- **Payment Admin DataTable**: Fixed JavaScript error "DataTable is not a function" on payment admin dashboard
  - Added required DataTables CSS and JavaScript libraries
  - Included DataTables Bootstrap 5 integration
  - Added export functionality dependencies (Excel, PDF, CSV, Print)
  - Enabled full table functionality: sorting, searching, pagination
  - Configured for mobile-first responsive design

## [3.53.0] - 2024-12-30

### Added
- **Manual Payment Processing**: ✅ **COMPLETE** - Admin tools for manual payment processing
  - Manual payment processing modal in payment details page
  - Support for cash, check, money order, bank transfer, and phone payments
  - Comprehensive payment form with amount, reference, date, and notes fields
  - Automatic payment record updates with admin audit trail
  - Integration with existing payment approval workflow
  - Manual payment buttons in dashboard and pending payments pages
  - Auto-opening modal via URL hash for quick access
  - Proper show status updates for show listing payments

- **Fix Pending Shows Tool**: ✅ **COMPLETE** - Admin utility for stuck payments
  - Admin tool at `/admin/fixPendingShowPayments` to fix shows stuck in payment_pending status
  - Automatic detection of shows missing payment records
  - Bulk creation of missing payment records for admin approval
  - Integration with admin settings panel via "Fix Pending Shows" card
  - Comprehensive reporting of fixed vs existing payments
  - Safe operation that only creates missing records

### Enhanced
- **Payment Dashboard**: Enhanced with manual payment processing capabilities
  - Added manual payment button to pending payment actions
  - Quick access to manual payment processing from dashboard table
  - Improved payment details page with comprehensive action options

## [3.52.0] - 2024-12-30

### Added
- **Admin Payment Management Dashboard**: ✅ **COMPLETE** - Comprehensive payment management system
  - New admin payment dashboard at `/payment/admin` with real-time statistics
  - Payment statistics cards showing total, completed, pending, and rejected payments
  - Payment type breakdown (registration vs show listing payments)
  - Advanced sortable and filterable payments table with DataTables integration
  - Export functionality for payment data (Excel, CSV, PDF formats)
  - Payment details view with comprehensive information display
  - Quick approve/reject actions directly from dashboard
  - Integration with admin settings panel for easy access
  - Mobile-first responsive design throughout all payment interfaces

- **Enhanced Payment Model**: Extended PaymentModel with new methods
  - `getAllPaymentsWithUserInfo()` - Enhanced payment data with user and related information
  - `getPaymentStatistics()` - Real-time payment statistics for dashboard
  - `getRecentPayments()` - Recent payment activity tracking
  - `getPendingPaymentsCount()` - Quick pending payment count for badges
  - `getPaymentDetailsById()` - Comprehensive payment details with related data

- **Admin Settings Integration**: Payment management integrated into admin settings
  - Payment Dashboard card added to admin settings panel
  - Payment Methods configuration card
  - Listing Fee Management card for complete payment ecosystem
  - Consistent design language with existing admin interface

### Enhanced
- **PaymentController**: Added admin-focused methods
  - `admin()` method for comprehensive payment dashboard
  - `details()` method for detailed payment information view
  - Enhanced security with role-based access control
  - Coordinator access limited to their own show payments

### Fixed
- **Admin Interface Layout**: Removed deprecated sidebar references
  - Updated payment dashboard to use container-fluid layout
  - Consistent header structure with other admin pages
  - Proper navigation breadcrumbs and back buttons

## [3.51.2] - 2024-12-30

### Added
- **Registration Detail View**: ✅ **COMPLETE** - Added comprehensive registration viewing functionality
  - New public `viewRegistration` method in RegistrationController for displaying registration details
  - Created responsive registration view template with complete information display
  - Access control system (registration owner, show coordinator, or admin only)
  - Comprehensive data display including registration, show, vehicle, and owner information
  - Vehicle image gallery supporting both old and new image systems
  - Judging results and awards display for completed shows
  - QR code display when available
  - Mobile-first responsive design with Bootstrap styling

### Fixed
- **Registration Controller Routing**: Fixed fatal error when accessing `/registration/view/{id}` URLs
  - Added missing public `viewRegistration` method to RegistrationController
  - Added smart public `view` method that handles both view template loading and registration viewing
  - Implemented same pattern as ShowController to avoid method signature conflicts
  - Enhanced App.php method visibility checking to only allow public methods
  - Resolved "cannot access protected method" error in App.php routing system

## [3.51.1] - 2024-12-30

### Fixed
- **Admin Impersonation Display**: Fixed issue where impersonation banner showed "Unknown User" instead of the actual impersonated user's name
- **Session Management**: Improved session variable handling during user impersonation to properly set user_name and user_email
- **Admin Session Restoration**: Fixed proper restoration of admin session variables when ending impersonation

## [3.50.8] - 2024-12-19

### Added
- **Club Ownership Verification System**: ✅ **COMPLETE** - Comprehensive system for managing club ownership
  - Users can request ownership of clubs without current owners
  - Admin verification panel with approval/denial workflow
  - File upload support for verification documents (PDF, images, Word docs)
  - Email notifications for request submissions and admin decisions
  - Club ownership status display with verification indicators
  - Request ownership buttons on clubs without owners
  - Admin statistics and pending request counts

### Fixed
- **Club Owner Display**: Fixed undefined property errors in manage club members page
  - Added proper owner_id field to calendar_clubs table
  - Updated club queries to include owner information
  - Enhanced club member display with owner identification

### Enhanced
- **Admin Settings Panel**: Added Club Ownership Verification card with pending request count
- **Club Management**: Enhanced club listing with owner information and verification status
- **Database Schema**: Added club ownership verification tables and fields

## [3.50.7] - 2024-12-19

### Fixed
- **Mobile Event Popup Navigation**: ✅ **COMPLETE** - Fixed "Form not found" error when clicking "View Details" button on mobile devices
  - Added missing popup action buttons HTML elements that were referenced in JavaScript
  - Enhanced modal "View Details" button with explicit event handling and navigation
  - Added defensive programming with null checks for popup elements
  - Improved mobile device detection and debugging
  - Prevented JavaScript event handler interference with link navigation
  - Added proper error handling and logging for troubleshooting

### Technical Improvements
- **Event Popup Architecture**: Enhanced popup and modal button handling with consistent event management
- **Mobile UX**: Improved mobile user experience with reliable event detail navigation
- **Error Handling**: Added comprehensive error handling and debugging for popup interactions

## [3.50.6] - 2024-12-19

### Enhanced
- **Mobile-First Responsive Navigation**: ✅ **COMPLETE** - Redesigned top navigation buttons for optimal mobile experience
  - Implemented mobile-first responsive design with proper breakpoints
  - Added minimum 44px touch targets for mobile accessibility
  - Created flexible button layouts that adapt to screen size
  - Enhanced button styling with hover effects and smooth transitions
  - Improved text visibility with responsive text sizing
  - Added proper focus states for keyboard navigation accessibility

### Technical Improvements
- **Responsive CSS Architecture**: Mobile-first CSS with progressive enhancement for larger screens
- **Touch-Friendly Interface**: Proper touch target sizes and spacing for mobile devices
- **Accessibility Enhancements**: Improved focus states and ARIA labels for better screen reader support
- **Performance Optimization**: Efficient CSS transitions and hover effects

## [3.50.5] - 2024-12-19

### Fixed
- **Pin Numbering Synchronization**: ✅ **COMPLETE** - Fixed pin numbering mismatch issue
  - Pin numbers now correctly match event list numbers when "Show all pins" is unchecked
  - Maintained proper global numbering when "Show all pins" is enabled
  - Added intelligent event-to-pin mapping logic for accurate numbering
  - Enhanced debug logging for pin numbering troubleshooting

### Technical Improvements
- **Smart Numbering Algorithm**: Implemented context-aware pin numbering based on pagination mode
- **Event Mapping Logic**: Enhanced event-to-list position mapping for accurate pin numbers
- **Debug Enhancement**: Added comprehensive logging for pin numbering operations

## [3.50.4] - 2024-12-19

### Fixed
- **Map View UI Issues**: ✅ **COMPLETE** - Resolved multiple user interface problems
  - Fixed overlapping between event count indicator and "Show all pins" toggle switch
  - Corrected white text on white background issue when events are selected/highlighted
  - Improved spacing and visual hierarchy in map view header
  - Enhanced readability with proper color contrast for selected events

### Added
- **Dual Pagination Controls**: Added pagination controls at both top and bottom of event list
- **Page Size Persistence**: Implemented localStorage to remember user's preferred page size setting
- **Synchronized Controls**: Both top and bottom pagination controls stay in sync automatically
- **Enhanced Visual Feedback**: Improved event highlighting with better color contrast

### Enhanced
- **User Experience**: Better accessibility with pagination controls at both ends of the list
- **Mobile Responsiveness**: Improved mobile layout with better spacing and touch targets
- **State Management**: User preferences are now saved and restored across page visits
- **Visual Design**: Better spacing, contrast, and visual hierarchy throughout the map view

### Technical Improvements
- **DOM Manipulation**: Efficient handling of dual pagination controls
- **Local Storage Integration**: Robust preference saving and restoration
- **CSS Improvements**: Better styling for highlighted events and form controls
- **Event Handling**: Synchronized event listeners for multiple control sets

## [3.50.3] - 2024-12-19

### Added
- **Calendar Map View Pagination**: ✅ **COMPLETE** - Comprehensive pagination system for calendar map view
  - Added configurable page sizes (10, 25, 50, 100 events per page)
  - Implemented toggle between 'Show all pins' and 'Current page pins' modes
  - Added smart pin navigation - clicking map pins jumps to correct page and highlights event
  - Created professional pagination controls with page numbers and navigation buttons
  - Added event highlighting with smooth scrolling animation
  - Enhanced user experience with seamless map-to-list navigation

### Enhanced
- **Map View User Experience**: Improved navigation and interaction with large event datasets
- **Performance Optimization**: Efficient client-side pagination for better performance with many events
- **Cross-Provider Support**: Pagination works with all supported map providers
- **Mobile Responsiveness**: Mobile-first pagination design for all device sizes
- **Debug Support**: Comprehensive logging for pagination operations and troubleshooting

### Technical Improvements
- **Promise-Based Loading**: Enhanced event loading with proper promise handling
- **State Management**: Robust pagination state management across viewing modes
- **Event Tracking**: Accurate event-to-marker mapping for precise navigation
- **JavaScript Architecture**: Modular pagination functions with clean separation of concerns

## [3.49.16] - 2024-12-19

### Fixed
- **Cron Heartbeat System**: ✅ **COMPLETE** - Fixed cron heartbeat not updating for notification processing
  - Fixed undefined $results variable when no pending notifications exist
  - Added automatic logs directory creation with proper permissions
  - Enhanced heartbeat file writing with comprehensive error checking
  - Added detailed logging for heartbeat operations and file verification
  - Improved error handling in both success and failure scenarios
  - Created test_cron_heartbeat.php for manual verification and troubleshooting

### Enhanced
- **Cron System Reliability**: Improved notification processing cron job stability and monitoring
- **Debug Logging**: Enhanced logging capabilities for better troubleshooting of cron operations

## [3.49.15] - 2024-12-19

### Fixed
- **Notification Settings Error**: ✅ **COMPLETE** - Fixed "Undefined variable $global_settings" error on notification preferences page
  - Enhanced error handling in NotificationModel for missing notification_settings table
  - Added automatic initialization of default notification settings
  - Updated UserController to properly handle global notification settings
  - Added graceful fallback when notification system tables don't exist
  - Created fix_notification_settings.php utility script for manual table creation
  - Improved error logging and debugging capabilities

### Enhanced
- **Notification System Robustness**: Improved notification system reliability and error handling
  - Added isNotificationSystemInstalled() method to check system status
  - Added initializeDefaultSettings() method for automatic setup
  - Enhanced getNotificationSettings() with try-catch error handling
  - Default notification methods enabled when settings unavailable
  - Backward compatible with existing installations

### Technical Improvements
- **Database Error Handling**: Enhanced database operation error handling
  - Proper exception handling for missing tables
  - Automatic creation of missing notification settings
  - Graceful degradation when notification system not fully installed
  - Debug logging for troubleshooting notification issues

## [3.49.14] - 2024-12-19

### Enhanced
- **Notification Preferences - Admin Control**: ✅ **COMPLETE** - Enhanced notification preferences to respect global admin settings
  - Added global notification method control - admins can now disable notification types system-wide
  - Users only see notification options that are enabled by administrators
  - Added conditional display for email, SMS, push, and toast notification settings
  - Enhanced form validation to prevent users from enabling globally disabled methods
  - Added proper messaging when no notification methods are available
  - Updated help section to dynamically show only available notification methods
  - Improved user experience with clean interface showing only usable options

- **Event Title Links**: ✅ **COMPLETE** - Made event titles clickable in notification preferences
  - Event titles in "Active Event Subscriptions" table are now clickable links
  - Smart URL routing automatically determines correct URL based on event type
  - Calendar events link to `/calendar/event/{id}`, car shows link to `/show/view/{id}`
  - Added accessible styling with hover and focus states
  - Maintained existing table structure and Bootstrap classes
  - Enhanced user navigation flow from notification preferences to event details

### Technical Improvements
- **Global Settings Integration**: Added retrieval and validation of global notification settings
  - Controller now checks `email_enabled`, `sms_enabled`, `push_enabled`, `toast_enabled` settings
  - Server-side validation prevents bypassing of global restrictions
  - Graceful fallback behavior when global settings are unavailable
  - Backward compatible with existing notification preferences system

- **Dynamic UI Generation**: Enhanced view to conditionally render based on admin settings
  - Form elements only appear when corresponding notification method is globally enabled
  - Alert messages dynamically generated based on available methods
  - Save button only appears when notification methods are available
  - Clean "no methods available" state with proper user messaging

## [3.49.16] - 2024-12-19

### Fixed
- **Mobile Modal Conflict**: ✅ **COMPLETE** - Fixed mobile notification fixes interfering with event popup modals
  - Removed aggressive global event handlers that were interfering with other modals
  - Made mobile enhancements specific to notification modal only (#notificationModal)
  - Fixed "form not found" error when clicking "View Details" button on events
  - Simplified mobile JavaScript to only target notification-specific elements
  - Removed complex touch event overrides that were breaking other modal functionality

### Enhanced
- **Mobile JavaScript Safety**: Improved mobile enhancement targeting and safety
  - Updated mobile-notifications-fix.js to v1.0.2 with safer, more targeted enhancements
  - Removed global modal button overrides that affected all modals
  - Enhanced CSS specificity to target only notification modal elements
  - Added proper modal ID checking to prevent interference with other modals
  - Maintained mobile notification functionality while preserving other modal operations

### Technical Improvements
- **Modal Isolation**: Enhanced modal-specific targeting to prevent cross-modal interference
  - Updated mobile-notifications-fix.css to v1.0.2 with more specific selectors
  - Removed global touch event handlers that were too broad in scope
  - Added backup of conflicting version for reference
  - Simplified mobile enhancement approach to focus only on notification modal styling
  - Preserved all mobile notification improvements while fixing modal conflicts

## [3.49.15] - 2024-12-19

### Fixed
- **Mobile Checkbox Spacing**: ✅ **COMPLETE** - Fixed checkboxes appearing too close to modal edges on mobile devices
  - Added proper padding and margins to all form checkboxes in notification modal
  - Enhanced spacing for notification time selection checkboxes
  - Fixed registration deadline container spacing and positioning
  - Added background styling to checkboxes for better visual separation
  - Improved touch targets with proper spacing from modal edges
  - Enhanced responsive design for extra small screens (576px and below)

### Enhanced
- **Mobile Form Layout**: Comprehensive spacing improvements for mobile notification modal
  - Added 0.75rem margins around all form elements to prevent edge contact
  - Enhanced checkbox containers with proper width calculations (calc(100% - 1.5rem))
  - Improved visual hierarchy with subtle background colors and borders
  - Added consistent spacing for alerts and notification containers
  - Better touch experience with larger, properly spaced interactive elements

### Technical Improvements
- **CSS Responsive Design**: Enhanced mobile-first approach for notification forms
  - Updated mobile-notifications-fix.css to v1.0.1 with comprehensive spacing fixes
  - Updated notifications.css to v3.49.15 with additional mobile form enhancements
  - Added proper box-sizing and width calculations for mobile containers
  - Enhanced grid system with proper padding and margin management
  - Improved visual consistency across all mobile screen sizes

## [3.49.14] - 2024-12-19

### Fixed
- **Mobile Notification Modal**: ✅ **COMPLETE** - Fixed subscribe and unsubscribe buttons not working on mobile devices
  - Fixed notification subscription modal buttons not responding to touch events on mobile
  - Enhanced mobile-first responsive design for notification subscription modal
  - Fixed modal layout elements hanging outside borders on mobile devices
  - Added comprehensive touch event support and mobile-specific event handling
  - Improved modal sizing and positioning for all mobile screen sizes
  - Added visual feedback for button interactions on touch devices

### Enhanced
- **Mobile User Experience**: Comprehensive mobile notification system improvements
  - Added mobile-specific CSS file (mobile-notifications-fix.css) for enhanced mobile layout
  - Added mobile-specific JavaScript file (mobile-notifications-fix.js) for touch event handling
  - Implemented fallback event handlers for better mobile compatibility
  - Enhanced modal buttons with better touch targets (48px minimum height)
  - Added ripple effects and visual feedback for mobile button interactions
  - Improved form controls with larger touch targets and better spacing
  - Added mobile device detection and mobile-specific enhancements

### Technical Improvements
- **Mobile-First Design**: Enhanced notification system for mobile devices
  - Updated notifications.css to v3.49.14 with comprehensive mobile responsive design
  - Updated notifications.js to v3.49.14 with mobile device detection and touch event support
  - Added mobile-notifications-fix.css v1.0.0 for additional mobile enhancements
  - Added mobile-notifications-fix.js v1.0.0 for mobile-specific functionality
  - Enhanced modal layout with proper viewport handling and overflow management
  - Added touch event listeners with proper passive/active event handling
  - Implemented mobile debugging and fallback mechanisms
  - Added visual feedback animations and touch state management

## [3.50.2] - 2024-12-19

### Enhanced
- **Notification Modal Registration Deadline Alert Styling**: ✅ **COMPLETE** - Expanded registration deadline container to match alert styling
  - Updated `#notify_registration_end_container` to use Bootstrap alert styling
  - Applied consistent width, padding (0.75rem 1rem), and margin (1rem bottom) with alert elements
  - Added alert-style background color (rgba(13, 202, 240, 0.1)) and border styling
  - Enhanced visual consistency throughout the notification subscription modal
  - Professional appearance matching other alert elements in the interface

### Technical Improvements
- **CSS Alert Integration**: Enhanced registration deadline container styling
  - Replaced simple positioning with full alert-style formatting
  - Added border: 1px solid transparent with border-radius: 0.375rem
  - Applied info-style color scheme (blue tones) for visual consistency
  - Added flexbox layout (display: flex; justify-content: space-between; align-items: center)
  - Positioned toggle at the end while keeping it in original position
  - Label takes available space with proper margin spacing
  - Updated notification CSS version to v3.49.4
  - Removed previous margin-left positioning in favor of full-width alert styling

## [3.49.12] - 2025-06-23

### Fixed
- **Notification Button DOM Error**: ✅ **COMPLETE** - Fixed JavaScript error when clicking notification button multiple times
  - Fixed "TypeError: Cannot set properties of null (setting 'innerHTML')" error
  - Added automatic page reload after successful subscription/unsubscription
  - Prevents DOM element reference issues when button is clicked repeatedly
  - Users can now subscribe/unsubscribe multiple times without JavaScript errors
  - Improved user experience with consistent page state after notification changes

### Enhanced
- **Notification System Stability**: Improved reliability of notification subscription interface
  - Page automatically refreshes after subscription changes to ensure consistent state
  - Success messages are shown before page reload (1.5 second delay)
  - Eliminates need for manual page refresh after notification changes
  - Better error prevention in notification button interactions

### Technical Improvements
- **JavaScript Error Prevention**: Enhanced DOM element handling in notifications.js
  - Updated notifications.js to v3.49.12
  - Added page reload after successful subscription (line ~295)
  - Added page reload after successful unsubscription (line ~358)
  - Maintained existing success message functionality
  - Created backup documentation in autobackup/notification_button_reload_fix/

## [3.49.11] - 2025-06-23

### Fixed
- **Notification Subscription Database Error**: ✅ **COMPLETE** - Fixed critical database column error in notification system
  - Fixed "Unknown column 'scheduled_time' in 'field list'" error when subscribing to show notifications
  - Changed incorrect column name from 'scheduled_time' to 'scheduled_for' in NotificationModel.php
  - Updated scheduleNotification method to use correct database schema
  - Users can now successfully subscribe to show notifications with registration deadlines
  - Notification scheduling now works properly without database errors

### Enhanced
- **Notification System Reliability**: Improved stability of notification subscription process
  - Event reminder notifications can now be scheduled correctly
  - Registration deadline notifications work as expected
  - Better error handling in notification scheduling
  - Consistent database column usage throughout notification system

### Technical Improvements
- **Database Schema Compliance**: Fixed column name mismatch in NotificationModel
  - Updated INSERT query in scheduleNotification method (line ~315)
  - Changed parameter binding from ':scheduled_time' to ':scheduled_for'
  - Added comprehensive test script for verifying the fix
  - Created backup documentation in autobackup/notification_scheduled_time_fix/
  - Version bumped to 3.49.11 for notification system compatibility

## [3.50.1] - 2024-12-19

### Fixed
- **Notification Modal Registration Deadline Positioning**: ✅ **COMPLETE** - Fixed positioning of registration deadline toggle in notification subscription modal
  - Moved "Also notify me about registration deadline" line 10 pixels to the right from modal border
  - Added specific ID targeting for precise positioning control
  - Enhanced visual spacing between modal border and toggle controls
  - Improved professional appearance of notification subscription modal
  - Maintained mobile-first responsive design compatibility

### Enhanced
- **Modal Layout Consistency**: Improved visual alignment in notification subscription modal
  - Registration deadline toggle no longer touches the left edge of modal window
  - Better visual flow and readability in modal interface
  - Consistent spacing throughout notification modal components

### Technical Improvements
- **CSS Targeting**: Added specific ID selector for registration deadline container
  - Added `id="notify_registration_end_container"` to modal HTML structure
  - Implemented `#notify_registration_end_container { margin-left: 10px; }` CSS rule
  - Updated notification CSS version to v3.49.3

## [3.50.0] - 2025-01-27

### Fixed
- **Notification Settings Toggle Layout**: ✅ **COMPLETE** - Improved toggle switch positioning in admin notification settings
  - Moved all toggle switches to the end of their text labels for better visual alignment
  - Applied consistent layout styling across all notification setting toggles
  - Enhanced text-to-toggle relationship with proper spacing using flexbox layout
  - Improved visual hierarchy and readability in notification settings interface
  - Better visual association between labels and their corresponding controls

### Enhanced
- **User Interface Consistency**: Standardized toggle positioning throughout notification settings
  - Email notifications toggle repositioned for better alignment
  - SMS notifications toggle repositioned for better alignment
  - Push notifications toggle repositioned for better alignment
  - Site notifications toggle repositioned for better alignment
  - SMS provider active status toggle repositioned for better alignment
  - SMS provider default status toggle repositioned for better alignment

### Technical Improvements
- **CSS Layout**: Enhanced form-check styling with flexbox for proper element distribution
  - Added `d-flex justify-content-between align-items-center` classes for consistent spacing
  - Maintained Bootstrap form-switch functionality while improving visual layout
  - Preserved all existing functionality while enhancing user experience

## [3.49.9] - 2025-01-27

### Fixed
- **Notification Modal Layout Issues**: ✅ **COMPLETE** - Fixed layout problems in Subscribe to Notifications popup window
  - Fixed left side elements being too close to the edge of the modal window
  - Resolved items hanging off the screen in notification subscription modal
  - Enhanced modal body padding and spacing for better content containment
  - Improved form element spacing and visual hierarchy
  - Added proper container fluid with controlled padding for better content flow

### Enhanced
- **Mobile Responsiveness**: Improved mobile-first responsive design for notification modal
  - Enhanced column layout with proper gutters and responsive behavior
  - Better touch targets for checkboxes and labels on mobile devices
  - Improved modal sizing and spacing for small screens (576px and below)
  - Added responsive button layout in modal footer for mobile devices
  - Enhanced form check styling with better hover effects and visual feedback

### Technical Improvements
- **CSS Architecture**: Updated notification system CSS to v3.49.1
  - Added `.notification-modal-body` class with proper spacing controls
  - Enhanced `.notification-times-column` styling with background and borders
  - Improved form check styling with better padding and hover effects
  - Added comprehensive responsive breakpoints for mobile optimization
  - Enhanced modal dialog sizing and positioning for small screens

## [3.49.8] - 2025-01-27

### Fixed
- **Notification Modal JavaScript Errors**: ✅ **COMPLETE** - Fixed JavaScript errors preventing notification modal functionality
  - Fixed "originalText is not defined" ReferenceError when clicking notify me button
  - Fixed "subscribeToEventModal is not defined" ReferenceError when clicking subscribe button in modal
  - Moved variable declarations outside try blocks to ensure proper scope in finally blocks
  - Added global modal functions setup during NotificationManager initialization
  - Improved script execution for dynamically loaded modal content
  - Enhanced error handling and button state management in modal operations

### Enhanced
- **Modal Function Availability**: Improved modal functionality reliability
  - Global functions (subscribeToEventModal, unsubscribeFromEventModal, updateNotificationButtonState) now available immediately
  - Better handling of dynamically loaded modal content and script execution
  - Enhanced debugging and error reporting for modal operations
  - Improved user experience with proper loading states and error messages

## [3.49.7] - 2025-01-27

### Fixed
- **Notification Modal Undefined Variables**: ✅ **COMPLETE** - Fixed undefined variable warnings in notification subscription modal
  - Fixed `$is_subscribed` undefined variable on lines 4 and 140
  - Fixed `$event_type` undefined variable on lines 19 and 84
  - Enhanced variable passing from NotificationController to modal template
  - Added fallback variable definitions to prevent undefined variable warnings
  - Added debug logging to track variable availability in modal template
  - All notification modal variables now properly defined and accessible

### Enhanced
- **Error Prevention**: Improved robustness of notification modal system
  - Added comprehensive variable existence checks with fallback values
  - Enhanced debug logging for troubleshooting variable scope issues
  - Improved error handling in modal template rendering

## [3.49.6] - 2025-01-27

### Fixed
- **Notification Queue Last Attempt Issue**: ✅ **COMPLETE** - Fixed "Never" showing in Last Attempt column
  - Enhanced `markNotificationSent` method with better error handling and debugging
  - Added verification checks to ensure notification exists before updating
  - Added detailed logging to track update success and row counts
  - Added `fixNullLastAttemptValues` method to repair existing NULL last_attempt values
  - Added auto-fix functionality that runs when viewing the notification queue
  - Fixed issue where sent notifications had NULL last_attempt values causing "Never" display

### Enhanced
- **Notification System Debugging**: Improved debugging and error tracking
  - Added comprehensive logging to track notification update process
  - Added verification of database updates with before/after state logging
  - Added automatic detection and repair of inconsistent notification states
  - Enhanced error messages with stack traces for better troubleshooting

## [3.49.5] - 2025-01-27

### Fixed
- **Notification System JSON Errors**: ✅ **COMPLETE** - Fixed JSON parsing errors in notification endpoints
  - Fixed missing authentication checks causing PHP errors to be returned as HTML instead of JSON
  - Added proper error handling and try-catch blocks to all notification endpoints
  - Added missing `return` statements after JSON responses to prevent code execution continuation
  - Added missing `getUnread` and `markRead` methods to NotificationController
  - Added missing `getUnreadNotifications` and `markNotificationsAsRead` methods to NotificationModel
  - Fixed authentication flow to handle non-logged-in users gracefully with JSON responses
  - All notification endpoints now return proper JSON responses instead of HTML error messages

### Enhanced
- **Error Handling**: Comprehensive error handling for all notification system endpoints
  - All endpoints now have try-catch blocks with proper error logging
  - Debug logging added for troubleshooting when DEBUG_MODE is enabled
  - Graceful handling of authentication failures with appropriate JSON responses

## [3.49.4] - 2025-01-27

### Added
- **Notification System Integration**: ✅ **COMPLETE** - Integrated notification buttons into event and car show pages
  - Added notification buttons to calendar event pages with subscribe/unsubscribe functionality
  - Added notification buttons to car show pages with subscribe/unsubscribe functionality
  - Added notification modals for configuring notification preferences
  - Added JavaScript handlers for notification button interactions
  - Added missing controller methods: `checkSubscription`, `subscribe`, `unsubscribe`
  - Fixed response handling for JSON responses in notification modals
  - Users can now click notification buttons on events/shows to enable/disable notifications
  - Notification preferences are configurable with custom timing and delivery methods

### Enhanced
- **User Experience**: Notification system now fully accessible from event and show pages
  - Seamless integration with existing event and show viewing experience
  - Real-time subscription status updates on notification buttons
  - Responsive notification modals work on all device sizes

## [3.48.9] - 2025-01-27

### Fixed
- **Test Notification Queue**: ✅ **COMPLETE** - Fixed test notification queuing functionality
  - Fixed column name mismatch between database (`scheduled_for`) and code (`scheduled_time`)
  - Fixed SQL parameter binding issues in `queueTestNotification` method
  - Fixed display issues in admin notification queue view
  - Test notifications now properly queue and display in admin interface
  - Enhanced debug logging for troubleshooting queue issues

### Enhanced
- **Debug Tools**: Added comprehensive debugging tools for notification queue
  - Added `debug_test_notification_queue.php` for testing queue functionality
  - Enhanced error logging in NotificationModel with detailed SQL debugging
  - Added table structure verification tools

## [3.48.8] - 2025-01-27

### Fixed
- **SMS Provider Display Issue**: ✅ **COMPLETE** - Fixed SMS providers not showing in admin settings
  - Fixed variable name inconsistency between controller and view ($smsProviders vs $sms_providers)
  - Added table existence check before loading SMS providers
  - Enhanced error handling for database connection issues
  - Added diagnostic tools for troubleshooting SMS provider issues

### Added
- **SMS Provider Diagnostics**: Added comprehensive diagnostic tools
  - Added `checkSmsProviders()` method to verify and create missing providers
  - Added debug information display when DEBUG_MODE is enabled
  - Added direct link to diagnostic tool from notification settings
  - Enhanced error logging for SMS provider loading issues

### Enhanced
- **Error Handling**: Improved robustness of SMS provider management
  - Added try-catch blocks around SMS provider loading
  - Enhanced error messages and logging
  - Added fallback behavior when tables don't exist

## [3.48.7] - 2025-01-27

### Fixed
- **Installation Path Issue**: ✅ **COMPLETE** - Fixed notification system installation path error
  - Fixed "Failed to open stream" error when accessing notification installation
  - Added fallback direct installation method when external installer file is missing
  - Enhanced error handling with proper file existence checks
  - Installation now works both with external installer file and built-in method

### Enhanced
- **Installation Robustness**: Improved installation system reliability
  - Added built-in installation method as fallback
  - Enhanced error reporting and progress feedback
  - Improved path resolution for different server environments

## [3.48.6] - 2025-01-27

### Added
- **SMS Provider Configuration**: ✅ **COMPLETE** - Added comprehensive SMS provider management
  - Fixed missing `updateSmsProvider()` method in AdminController
  - Enhanced NotificationModel to properly handle SMS provider configuration updates
  - Added installation system for notification tables and default providers
  - Created admin interface for installing notification system components
  - Added support for Twilio, TextMagic, Nexmo/Vonage, ClickSend, and Plivo SMS providers
  - Each provider has custom configuration fields based on their API requirements
  - Added provider activation and default selection functionality
  - Integrated installation link in admin settings page

### Enhanced
- **Notification System**: Improved notification system management
  - Added comprehensive installation script for notification database tables
  - Enhanced admin interface with installation status and configuration options
  - Added proper error handling and success feedback for SMS provider updates
  - Improved notification statistics display in admin settings

### Fixed
- **Admin Settings Access**: Fixed SMS provider configuration accessibility
  - SMS provider settings are now properly accessible from admin settings page
  - Fixed form submission handling for individual SMS provider configurations
  - Resolved database table creation issues for notification system

## [3.48.5] - 2025-01-27

### Fixed
- **WYSIWYG Content Display**: ✅ **COMPLETE** - Fixed HTML content not displaying properly in event view
  - Root cause: `strip_tags()` was removing style attributes from HTML tags, losing font sizes and colors
  - Replaced `strip_tags()` with regex-based sanitization that preserves style attributes
  - Now removes dangerous tags (script, iframe, object, embed, form) while preserving formatting
  - Removes dangerous attributes (onclick, javascript:) while keeping style, class, and formatting attributes
  - Font sizes, colors, and all WYSIWYG formatting now display correctly in event view
  - Maintains security by removing XSS vectors while preserving legitimate HTML formatting

### Security
- **Enhanced HTML Sanitization**: Improved security approach for WYSIWYG content
  - Uses targeted regex patterns to remove specific dangerous elements
  - Preserves legitimate formatting while blocking XSS attempts
  - More sophisticated than strip_tags() approach

## [3.48.4] - 2025-01-27

### Reverted
- **Overcomplicated Changes**: Reverted unnecessary changes to WYSIWYG editor JavaScript
  - Issue was in display logic, not editor functionality
  - Simplified approach focusing on the actual problem

## [3.48.3] - 2025-01-27

### Fixed
- **WYSIWYG Editor Delete Button Issue**: ✅ **COMPLETE** - Fixed delete buttons being saved with images
  - Delete buttons (red circle with "x") now only appear during editing
  - Added `getCleanContent()` method to strip editor UI elements before saving
  - Updated `updateOriginalElement()` to use cleaned content
  - Resize handles and editor wrappers are now removed from saved content
  - Added `restoreEditorFunctionality()` to re-add editor UI when loading existing content
  - Images now save as clean HTML without editor-specific elements

### Enhanced
- **Content Cleaning**: Improved content sanitization for WYSIWYG editor
  - Editor UI elements are automatically stripped before saving
  - Clean content is preserved while maintaining editing functionality
  - Backward compatibility with existing content maintained

## [3.48.2] - 2025-01-27

### Fixed
- **Event Description Display**: ✅ **COMPLETE** - Fixed WYSIWYG content rendering in event view
  - Fixed event descriptions showing HTML tags and base64 text instead of rendered content
  - Updated event.php view to properly handle HTML content from WYSIWYG editor
  - Added conditional logic to detect HTML vs plain text descriptions
  - Maintained security by using strip_tags() with allowed HTML tags
  - Added comprehensive CSS styles for proper WYSIWYG content display
  - Preserved backward compatibility with plain text descriptions

### Enhanced
- **WYSIWYG Content Styling**: Added complete CSS styling for rendered WYSIWYG content
  - Proper paragraph, heading, and list formatting
  - Image display with responsive sizing and shadows
  - Code block and inline code styling
  - Link styling with hover effects
  - Blockquote styling with left border and background

## [3.48.1] - 2024-12-20

### Fixed
- **WYSIWYG Image Save Issue**: ✅ **COMPLETE** - Fixed images not saving in event descriptions
  - Fixed CalendarController sanitization that was stripping HTML content and base64 images
  - Added proper WYSIWYG editor initialization to edit_event.php page
  - Replaced deprecated FILTER_SANITIZE_STRING with FILTER_SANITIZE_FULL_SPECIAL_CHARS
  - Preserved description field content during form processing to maintain rich text formatting
  - Added missing CalendarModel methods for event image settings and management
  - Fixed description field output escaping in both create and edit event forms

### Security
- **Input Sanitization**: Updated all CalendarController methods to use non-deprecated sanitization filters
  - Replaced FILTER_SANITIZE_STRING (deprecated in PHP 8.1) with FILTER_SANITIZE_FULL_SPECIAL_CHARS
  - Maintained security while preserving WYSIWYG content functionality

## [3.48.0] - 2024-12-20

### Added
- **WYSIWYG Editor for Event Descriptions**: ✅ **COMPLETE** - Self-hosted secure rich text editor
  - Comprehensive formatting toolbar with bold, italic, underline, strikethrough
  - Font size selection and text alignment options
  - Bullet and numbered lists with indentation controls
  - Text and background color pickers
  - Table insertion with customizable rows and columns
  - Image upload with Base64 storage and configurable limits
  - Content sanitization to prevent XSS attacks
  - Mobile-responsive design with touch-friendly controls
  - Admin settings for image configuration (max images, file size, allowed types)
- **Club Search and Management System**: ✅ **COMPLETE** - Advanced club selection interface
  - Real-time club search with autocomplete functionality
  - Quick club creation directly from search interface
  - Multiple club selection with visual feedback and removal controls
  - AJAX-powered search with debouncing and error handling
  - Mobile-responsive design with accessibility features
  - Integration with event creation and editing forms

### Enhanced
- **Event Creation/Editing**: Rich text descriptions with embedded images and formatting
- **Club Management**: Streamlined club selection process with search and create capabilities
- **Admin Controls**: Configurable image settings for events in calendar settings page
- **User Experience**: Modern, intuitive interfaces for content creation and club management

### Technical Improvements
- Self-hosted WYSIWYG editor eliminates external dependencies
- Base64 image storage ensures data portability and security
- Comprehensive input sanitization and validation
- Mobile-first responsive design principles
- CSRF protection for all AJAX operations
- Debounced search to optimize server performance
- Error handling and loading states for better UX

## [3.47.7] - 2024-12-20

### Added
- **Numbered Event Badges**: ✅ **COMPLETE** - Sequential numbering system for events in map view
  - Events in list display numbered badges (1, 2, 3, etc.) for easy reference
  - Corresponding map markers show the same numbers for cross-reference
  - Automatic event sorting by start date with soonest events first
  - Support for all map providers (Google Maps, OpenStreetMap, Mapbox, HERE Maps)

### Enhanced
- **Event Organization**: Visual clarity with numbered system for matching list items with map markers
- **User Experience**: Easy identification and location of specific events on the map
- **Cross-Platform Support**: Numbered markers work consistently across all supported map providers

### Technical Improvements
- Updated marker creation functions for all map providers to include event numbers
- Added CSS styling for numbered badges with responsive design
- Implemented event sorting algorithm based on start date
- Enhanced marker customization with embedded number display

## [3.47.6] - 2024-12-20

### Enhanced
- **Map View Layout**: ✅ **COMPLETE** - Advanced filters moved above map for consistency with event view
  - Filters relocated from sidebar to above map container
  - Full-width layout for better filter visibility and usability
  - Improved mobile responsiveness and accessibility
  - Consistent design language across event and map views
  - Better space utilization with streamlined interface

### Technical Improvements
- Layout restructuring from sidebar (col-md-3) + main content (col-md-9) to full-width (col-12)
- Enhanced mobile-first responsive design
- Improved user experience consistency across calendar views

## [3.47.5] - 2024-12-20

### Added
- **Enhanced Facebook Sharing**: ✅ **COMPLETE** - Proper Open Graph meta tags for rich social media previews
  - Open Graph meta tags (og:type, og:title, og:description, og:image, og:url)
  - Event-specific meta tags (event:start_time, event:end_time, event:location)
  - Twitter Card support for enhanced Twitter sharing
  - LinkedIn-specific meta tags for professional sharing
  - SEO-friendly meta descriptions and canonical URLs
  - Facebook sharing popup window for better user experience

### Enhanced
- **Social Media Integration**: Facebook, Twitter, and LinkedIn now display proper event information
- **SEO Optimization**: Added proper meta descriptions and canonical URLs for better search engine visibility
- **Debug Features**: Debug mode now shows Open Graph tag information for troubleshooting

### Technical Improvements
- Dynamic meta tag generation based on event data
- Cross-platform social media optimization
- Proper HTML escaping for security
- Enhanced header template with flexible meta tag support

## [3.47.4] - 2024-12-20

### Added
- **Guest Access to Public Events**: ✅ **COMPLETE** - Public events can now be viewed without login
  - Privacy-aware access control with different levels for public, member, and private events
  - Smart redirects and messaging for different user authentication states
  - Login prompts for guests wanting to access additional features
  - Contextual navigation that adapts based on user login status
  - Social sharing remains available to guests for public events

### Enhanced
- **Event Privacy System**: Enhanced privacy handling with proper guest access controls
- **User Experience**: Improved UX for both logged-in users and guests
- **Navigation**: Context-aware back buttons and feature access

### Technical Improvements
- Modified CalendarController constructor to allow guest access to event method
- Added getCurrentMethod() helper function for method-specific authentication
- Updated event view templates to handle non-authenticated users properly
- Implemented proper session checking throughout event display logic

## [3.47.3] - 2024-12-20

### Added
- **Social Sharing for Calendar Events**: ✅ **COMPLETE** - Full social media integration for event sharing
  - Facebook sharing with event details and location
  - Twitter sharing with hashtags and event information  
  - LinkedIn sharing with professional formatting
  - Email sharing with formatted event details and link
  - Copy link functionality with visual feedback
  - Mobile-first responsive design for all social buttons
  - Privacy-aware sharing (only available for public events)
  - Smart content generation with event title, date, and location
  - Modern Clipboard API with fallback support for older browsers
  - Bootstrap tooltip integration for enhanced UX
  - Debug mode integration for troubleshooting

### Enhanced
- **Event Detail Page**: Social sharing buttons positioned near export button for better UX
- **Mobile Responsiveness**: Social buttons adapt to screen size with appropriate text/icon display
- **User Experience**: Hover effects and success/error states for all sharing actions

### Technical Improvements
- Cross-browser compatibility with graceful degradation
- SEO-friendly URL encoding for social media platforms
- Proper error handling for clipboard operations
- Debug logging integration when DEBUG_MODE is enabled

## [3.47.2] - 2024-12-20

### Completed
- **Event Spanner Bar Colors**: ✅ **WORKING** - Event colors now display correctly with attractive gradient/fade effect
  - Cleaned up debug logging after successful implementation
  - Maintained robust multi-layered color system for reliability
  - Event bars properly show individual colors from database with nice visual styling
  - Final implementation is clean and performant

### Technical Improvements
- Removed extensive debug logging while maintaining core functionality
- Streamlined color application code for production use
- Preserved CSS custom properties and fallback mechanisms for reliability

## [3.47.1] - 2024-12-20

### Enhanced
- **Event Spanner Bar Colors**: Enhanced color system with multiple fallback mechanisms
  - Added CSS custom properties (`--event-color`) as reliable fallback for event colors
  - Implemented multiple color-setting approaches: inline styles + CSS variables + data attributes
  - Added `.dynamic-color` CSS class with `!important` rule using CSS variables
  - Enhanced debugging with computed style analysis to identify CSS override issues
  - Comprehensive color application ensures colors display regardless of CSS conflicts

### Technical Improvements
- Multiple JavaScript approaches: `setProperty()`, direct style assignment, CSS custom properties
- Enhanced CSS with `var(--event-color, #3788d8) !important` for reliable color display
- Added computed style debugging to troubleshoot browser rendering issues
- Improved color system resilience against CSS framework conflicts

## [3.47.0] - 2024-12-20

### Fixed
- **Event Spanner Bar Colors**: Fixed issue where event spanner bars were not displaying individual event colors
  - Removed default background-color from CSS `.event-event-bar` class that was overriding dynamic colors
  - Enhanced JavaScript color handling with `!important` override to ensure colors are applied
  - Added comprehensive debug logging for color troubleshooting
  - Event bars now properly display their assigned colors from the database

### Technical Improvements
- Enhanced `createEventBar()` method with better color handling and debugging
- Added detailed debug logging in CalendarController for color value tracking
- Improved CSS specificity to prevent color override conflicts
- Updated version numbers across JavaScript and CSS files

## [3.46.3] - 2024-12-20

### Fixed
- **Mobile Loading Message**: Removed "Loading Mobile View... Preparing event cards" message that was still appearing on mobile devices
  - Removed mobile HTML section from calendar template
  - Updated JavaScript initialization to remove mobile container parameter
  - Completely eliminated all mobile-related UI elements

### Technical Improvements
- Cleaned up calendar template by removing unused mobile Event chart HTML structure
- Updated MonthlyEventChart constructor call to match new single-parameter signature
- Ensured consistent desktop-only experience across all devices

## [3.46.1] - 2024-12-20

### Fixed
- **Navigation Filter Application**: Fixed issue where next/previous month and today buttons were not applying current filters
- **Event Loading**: Navigation buttons now properly reload events with current filter parameters when changing months
- **User Experience**: Seamless filtering experience when navigating between months

### Technical Improvements
- Added `loadEventsFromAPI()` calls to all navigation methods (`previousMonth()`, `nextMonth()`, `goToToday()`)
- Enhanced debug logging for navigation actions
- Improved filter persistence across month changes

## [3.46.0] - 2024-12-20

### Major Enhancement
- **Filter System Integration**: Complete integration between advanced filter system and Monthly Event Chart

### Critical Fix
- **Infinite Loop Resolution**: Fixed infinite loop between filter system and Event chart when applying filters
  - **Problem**: Filter system called `refetchEvents()` → Event chart called back to `applyFilters()` → infinite loop
  - **Solution**: Event chart's `refetchEvents()` now loads events directly using current filter parameters
  - **Result**: No callback to filter system, avoiding infinite loop while maintaining full filter functionality

### Added
- **refetchEvents() Method**: Added for filter system compatibility with infinite loop prevention
- **clearEvents() Method**: Added for proper event clearing when filters change
- **getEvents() Method**: Added for filter system to access current events
- **loadEventsFromAPI() Method**: Enhanced fallback method for direct API loading with filter parameters
- **Enhanced Detection**: Improved filter system detection of Event chart capabilities
- **Comprehensive Logging**: Added detailed debug logging throughout filter integration process

### Fixed
- **Filter Communication**: Fixed communication issues between filter system and Event chart
- **Event Loading**: Fixed events not loading on page load due to infinite loop prevention
- **Calendar Instance Detection**: Improved detection of calendar instance in filter system
- **Filter Application**: All advanced filters now work seamlessly with Event chart

### Filter Support
- **Calendar Selection**: Filter by specific calendars
- **Date Ranges**: Filter by custom date ranges
- **Location Filters**: Filter by state, city, and radius
- **Categories & Tags**: Filter by event categories and tags
- **Price Ranges**: Filter by event price ranges
- **Combined Filters**: All filters work together seamlessly

### Technical Improvements
- Enhanced filter system to properly detect and work with Monthly Event Chart
- Added multiple compatibility methods for seamless filter integration
- Improved error handling in filter system for Event chart operations
- Prevented circular dependencies between filter system and Event chart
- Updated version numbers and debug logging across both systems

## [3.42.0] - 2024-12-19

### Removed
- **Alternating Row Colors**: Removed all alternating color modifications as requested
- **Date/Time Sections**: Removed all date/time and location sections from timeline areas
- **Timeline Container**: Reverted to simple timeline structure without additional containers
- **Event Details**: Removed event details placement attempts

### Reverted
- **Event Row Structure**: Back to original simple structure without alternating colors
- **Timeline Layout**: Simplified timeline without additional detail sections
- **CSS Cleanup**: Removed all related alternating color and event detail styles

### Technical Changes
- Removed `.event-event-row-alternate` class and styling
- Removed event details container and related CSS
- Simplified timeline structure back to original design
- Cleaned up unused date/time formatting code

## [3.41.0] - 2024-12-19

### Fixed
- **Event Details Placement**: Correctly placed date/time and city/state BELOW the timeline in the white area
- **Timeline Structure**: Restructured to use timeline container with separate details section below
- **Event Color Debug**: Added debug logging to troubleshoot event color issues
- **CSRF Token**: Reverted to use proper generateCsrfToken() function for drag and drop
- **CSS Structure**: Updated weekly timeline styles to match new container structure

### Enhanced
- **Event Details Layout**: Proper placement in white area below timeline bars
- **Debug Information**: Added color debugging for troubleshooting event colors
- **Timeline Container**: Better separation between timeline and event details

### Technical Improvements
- Restructured timeline HTML to use container with separate details div
- Enhanced CSS for proper event details positioning
- Added debug logging for event color troubleshooting
- Fixed CSRF token generation for drag and drop operations

## [3.40.0] - 2024-12-19

### Fixed
- **Event Details Placement**: Moved date/time and city/state to timeline area (white space below spanner bars)
- **Today Line Indicator**: Fixed inverted logic - now properly follows checkbox settings
- **Event Colors**: Fixed spanner bars to use 'color' field from calendar_events table
- **Row Alternating**: Fixed to apply light blue background to entire event rows (not just timeline area)
- **Event Interaction**: Fixed hover and click functionality for event names in left column
- **Badge Sizing**: Reduced badge sizes by a few pixels for better visual balance
- **CSRF Token**: Fixed drag and drop CSRF token issue for proper authentication

### Enhanced
- **Event Details Layout**: Date/time (left justified) and city/state (right justified) in timeline area
- **Badge System**: Smaller, more refined badges for better visual hierarchy
- **Row Colors**: Light blue alternating rows matching week header style
- **Event Interaction**: Full hover and click support for event names

### Technical Improvements
- Enhanced timeline event details with proper positioning
- Fixed event color priority: event.color > event.backgroundColor > default
- Improved CSRF token handling for drag and drop operations
- Refined CSS for better visual consistency

## [3.39.0] - 2024-12-19

### Enhanced
- **Monthly Event Chart UI/UX Improvements**: Major visual and functional enhancements
  - **Colored Week Headers**: Added rotating color backgrounds (blue, purple, green, orange, pink) for better visual separation
  - **Event Badge System**: Implemented numbered badges (1, 2, 3...) for both spanner bars and event names
  - **Alternating Row Colors**: Added alternating white/light gray backgrounds for improved readability
  - **Enhanced Event Details**: Added start/end date/time (left justified) and city/state (right justified) below event names
  - **Clickable Event Names**: Event names now open event popups just like spanner bars
  - **Fixed Today Line Logic**: Corrected inverted logic - now properly shows/hides based on settings
  - **Full Drag & Drop**: Implemented complete drag and drop functionality with visual feedback and drop zones
  - **Proper Color Integration**: Spanner bars now correctly use event background colors

### Technical Improvements
- Enhanced `createWeekHeader()` method with color rotation system
- Implemented `getEventBadgeNumber()` for consistent badge numbering
- Added `addDropZoneListeners()` and `handleEventDrop()` for drag and drop
- Fixed `updateTodayLines()` logic (was showing opposite of settings)
- Enhanced CSS with badge styles, alternating rows, and drag feedback
- Improved mobile touch support and accessibility

### Files Modified
- `public/js/monthly-event-chart.js` - Core functionality enhancements
- `public/css/monthly-event-chart.css` - Visual styling improvements
- `features.md` - Updated feature documentation
- `EVENT_CHART_DEVELOPMENT_SUMMARY.md` - Added latest milestone

## [3.35.68] - 2024-12-19

### Fixed
- **Calendar Map View**: Fixed `Uncaught ReferenceError: activeFilters is not defined` error
  - Added `getActiveFilters()` helper function for consistent filter object access
  - Updated all direct `activeFilters` references throughout `map.php`
  - Maintained backward compatibility with fallback mechanism
  - Fixed filter integration between calendar and map views
  - Resolved JavaScript errors in `updateCalendarFilters()` function

### Technical Details
- **Root Cause**: Direct `activeFilters` variable references instead of proper global object access
- **Solution**: Implemented centralized helper function with fallback support
- **Files Modified**: `views/calendar/map.php`
- **Backup Created**: `autobackup/calendar_map_activefilters_fix/`

## [3.38.0] - 2024-12-19

### Added
- **Monthly Event Chart View**: Revolutionary new calendar visualization with unlimited event rows
- **Mobile-First Responsive Design**: No horizontal scrolling, smart card layout for mobile devices
- **Enhanced Event Visualization**: Event titles in left column with city/venue details, color-coded timeline bars
- **Advanced User Permissions**: Role-based editing (Admin: all events, Coordinator: owned + coordinated shows, Users: owned events)
- **Enhanced Hover Popup System**: Mouse-centered positioning with smooth animations and permission-based action buttons
- **Today Indicator Line**: Dynamic red line spanning entire chart with animated marker
- **Touch Support**: Full mobile touch interaction support for modern devices
- **Subtle Animations**: Hover states, selection feedback, and smooth transitions throughout

### Enhanced
- **Calendar Navigation**: Updated to "Event View" with month navigation controls
- **Event Display**: Timeline bars show start/end times with intelligent formatting
- **Mobile Experience**: Card-based layout with timeline visualization for small screens
- **Performance**: Optimized rendering for large numbers of events
- **Accessibility**: High contrast support, reduced motion preferences, keyboard navigation

### Technical Implementation
- **New Files**: `monthly-event-chart.css`, `monthly-event-chart.js`, `monthly-event-debug.js`
- **Updated Views**: `custom_index_fixed.php` completely redesigned for Event chart
- **JavaScript Class**: `MonthlyEventChart` with full API compatibility
- **CSS Grid Layout**: Responsive grid system with mobile breakpoints
- **Event Positioning**: Mathematical calculation for precise timeline positioning
- **Debug Tools**: Comprehensive debugging utilities for development

### User Experience
- **No Horizontal Scrolling**: Smart responsive design prevents horizontal scroll on any device
- **Intuitive Navigation**: Month navigation with "Today" quick access
- **Visual Hierarchy**: Clear event organization with color coding and typography
- **Interactive Elements**: Hover effects, click actions, and touch gestures
- **Permission Awareness**: UI adapts based on user role and event ownership

## [3.37.0] - 2024-12-19

### Added
- **Comprehensive Calendar Improvements**: Major enhancement package for calendar functionality
- **Event Bar Spacing Fix**: Added proper spacing between time and event title with `&nbsp;&nbsp;`
- **Continuous Scrolling Animation**: Implemented right-to-left continuous loop scrolling for long event titles
- **All-Day Event Positioning**: All-day events now positioned at top of day cells with full width
- **Time-Based Event Positioning**: Event bars positioned based on start/end times within day blocks
- **Event Table Below Calendar**: Comprehensive event listing with Title, City, State, Start/End Date/Time, Venue
- **AJAX Hover Popup**: Event preview popup with 500ms show delay, 300ms hide delay
- **Equal Day Cell Sizing**: Fixed calendar day cells to maintain equal 120px height proportions

### Enhanced
- **Scrolling Animation**: Changed from partial scroll to continuous right-to-left loop animation
- **Event Rendering**: Separated all-day and timed events into distinct sections within day cells
- **Event Table Integration**: Automatic population of event table when calendar events are loaded
- **Hover Functionality**: Added hover popup to all event types (single-day, multi-day, spanning)
- **Mobile Responsiveness**: Enhanced table and popup responsiveness for mobile devices

### Technical Details
- Updated CSS animation from `scroll-text` to `scroll-text-continuous` with 6s duration
- Added `.calendar-all-day-events` and `.calendar-timed-events` CSS classes
- Added `.all-day` and `.timed` event classes for proper styling
- Implemented `triggerEventTableUpdate()` method for event table synchronization
- Added `addHoverHandlers()` method for consistent hover functionality
- Enhanced day cell structure with separate sections for different event types
- Version bumped to 3.36.0 for calendar JavaScript component

## [3.35.62] - 2024-12-19

### Fixed
- **Multi-Day Event Spanning**: Implemented proper visual spanning of multi-day events across calendar cells
- Events now display as continuous bars spanning multiple days instead of repeating in each cell
- Fixed event text duplication issue in multi-day events
- Enhanced visual continuity for events spanning multiple weeks

### Enhanced
- Multi-day events now render as single spanning elements using CSS Grid positioning
- Separated single-day and multi-day event rendering logic for better performance
- Added intelligent text truncation based on event span width
- Implemented proper layering (z-index) for overlapping multi-day events
- Added segment styling for events spanning multiple rows (start, middle, end)

### Technical Details
- Added `renderMultiDayEvents()` method for spanning event rendering
- Added `createSpanningEventElement()` method for CSS Grid positioning
- Added `createSingleDayEventElement()` method for regular events
- Enhanced CSS with `.calendar-spanning-event` and segment classes
- Version bumped to 3.35.56 for calendar JavaScript component

## [3.35.61] - 2024-12-19

### Fixed
- **Calendar Day Cell Proportions**: Fixed calendar day cells getting resized out of square proportion when multi-day events are displayed
- Enhanced multi-day event rendering with visual continuation indicators
- Improved event title truncation to prevent horizontal cell expansion
- Added better overflow handling for event-heavy days
- Implemented multi-day event detection and styling (start, middle, end segments)

### Enhanced
- Multi-day events now show subtle visual indicators (◀ ▶) to indicate continuation
- Event titles are intelligently truncated based on event type (shorter for multi-day events)
- Time display only shows on the start day of multi-day events
- Better CSS constraints to maintain calendar grid proportions

### Technical Details
- Updated `createEventElement()` function to accept current day parameter for multi-day logic
- Added `isMultiDayEvent()` helper function for better event type detection
- Enhanced CSS with `.multi-day`, `.start`, `.middle`, `.end` classes for visual styling
- Improved text truncation and padding adjustments for multi-day event indicators
- Version bumped to 3.35.55 for calendar JavaScript component

## [3.35.60] - 2025-06-19

### Fixed
- **CRITICAL**: Fixed keyword search functionality - SQL parameter binding error resolved
- Fixed duplicate parameter names in SQL query causing PDO errors
- Changed calendar filter from multiple IN parameters to FIND_IN_SET for better compatibility
- Separated keyword search parameters for title and description (`:keyword_title` and `:keyword_desc`)
- Enhanced error logging and debugging for SQL parameter issues

### Technical Details
- Root cause: PDO parameter binding error due to duplicate `:keyword` parameter in SQL query
- Solution: Used unique parameter names for each keyword search condition
- Simplified calendar ID filtering using FIND_IN_SET instead of multiple placeholders
- Added comprehensive SQL query and parameter logging for debugging
- Restored debug logging to only occur when DEBUG_MODE is enabled

## [3.35.53] - 2025-06-19

### Fixed
- **CRITICAL**: Fixed calendar filter reset issue - resetting filters to "All" now properly refreshes calendar
- Implemented forced calendar refresh when applyFilters() is called
- Added filter reset detection with enhanced debugging
- Fixed calendar not showing all events when location filters are cleared

### Technical Details
- Root cause: Calendar system wasn't making new requests when filters were reset to empty values
- Solution: Always clear and recreate event sources when applyFilters() is called
- Added filter reset detection: `isFilterReset` boolean to track when state/city filters are empty
- Enhanced debugging with "FILTER RESET DETECTION" console messages
- Improved timestamp-based cache busting to prevent stale data
- Version 3.35.53 of calendar-filters.js includes the complete fix

### Testing
- Load calendar → Apply location filters → Reset to "All" → Click Apply
- Expected: Calendar refreshes and shows all events from selected calendars
- Debug: Console shows filter reset detection messages

## [3.35.50] - 2025-01-27

### Fixed
- **CRITICAL**: Fixed calendar not refreshing when filters are reset to "All"
- Added timestamp parameter to force calendar refresh on every filter application
- Enhanced debug logging to track filter application and event source calls
- Added comprehensive logging for state/city filter value changes

### Technical Details
- The issue was that when filters were reset to "All" (empty values), the calendar system didn't make new requests
- Added `_t` timestamp parameter to ensure unique URLs and force refresh
- Added debug logging to track: applyFilters calls, event source function calls, filter values
- This ensures that resetting filters to "All" will properly reload all events

## [3.35.49] - 2025-01-27

### Fixed
- **CRITICAL**: Fixed SQL parameter binding error in calendar filters
- Fixed "SQLSTATE[HY093]: Invalid parameter number" error when applying city/state filters
- State and city filters now use unique parameter names to avoid PDO binding conflicts
- Enhanced debug logging in CalendarController for filter troubleshooting

### Technical Details
- The issue was caused by using the same parameter name multiple times in SQL queries
- PDO requires unique parameter names even when the same value is used multiple times
- Changed `:state` to `:state_e` and `:state_v` for event and venue table filtering
- Changed `:city` to `:city_e` and `:city_v` for event and venue table filtering

## [3.35.48] - 2025-01-27

### Fixed
- Fixed advanced calendar filter not working with city selection
- Added missing city filter implementation in CalendarModel::getEvents method
- Made city and state filters case-insensitive for better matching
- Added missing venue filters (single and multiple venue IDs)
- Added missing club filters (single and multiple club IDs) 
- Added missing keyword search filter for title and description
- Added missing show ID filter
- Added missing price range filters (min/max)
- Enhanced debug logging for filter troubleshooting

### Technical Details
- The main getEvents method was missing several filter implementations that existed in other methods
- City filter was completely missing, causing filtered results to return empty
- Case sensitivity issues prevented proper matching of city names like "China Grove" vs "china grove"
- Added comprehensive filter support to match the filter UI capabilities

## [3.35.55] - 2025-01-27

### Fixed
- Fixed calendar event duplication issue when unchecking/rechecking calendar filters
- Enhanced clearEvents mechanism to thoroughly remove all event DOM elements
- Removed remaining quick calendar toggle references from filter system
- Improved loading indicators and event clearing feedback
- Added comprehensive DOM element cleanup when events are cleared

### Changed
- Removed redundant quick calendar toggles from sidebar
- Streamlined calendar interface to full-width layout
- Moved "Create Calendar" button to main navigation dropdown
- Unified event loading and clearing across all calendar operations

## [3.35.54] - 2025-01-27

### Changed
- Removed quick calendar toggle sidebar section
- Implemented full-width calendar layout
- Moved create calendar button to navigation dropdown
- Cleaned up JavaScript code related to quick calendar toggles
- Synchronized quick calendar toggles with advanced filter system
- Added loading indicator when applying filters
- Fixed issue where unchecking all calendars still showed events
- Improved filter application to immediately fetch and display filtered events

## [3.35.54] - 2025-06-23

### Fixed
- Fixed event filtering on calendar not working properly - all events were always displayed
- Added proper support for category and tag filtering in the CalendarModel
- Improved handling of non-existent category and tag tables in the database
- Disabled category and tag filters in the UI when no categories or tags are available
- Enhanced error handling for category and tag filtering
- Added debug logging for SQL queries when DEBUG_MODE is enabled

## [3.35.53] - 2025-06-22

### Fixed
- Fixed calendar filtering system where unchecking all calendars still showed events
- Fixed upcoming events sidebar to respect calendar filter selections
- Enhanced server-side handling of empty calendar selections in both main events and upcoming events
- Improved client-side filtering to properly handle the case when no calendars are selected
- Modified calendar initialization to include calendar filters in the initial request
- Added better debugging and logging for calendar filter operations
- Fixed real-time filtering when toggling calendar checkboxes
- Fixed custom calendar implementation to properly handle empty calendar selections
- Enhanced updateVisibleCalendars function to clear events when no calendars are selected
- Fixed "addEvents received non-array: undefined" error in custom-calendar.js
- Fixed "successCallback is not a function" error in event source handling
- Improved error handling in custom calendar event loading
- Enhanced event source functions to work with both callback and promise patterns

## [3.35.52] - 2025-06-21

### Fixed
- Fixed form_selector.php to use proper CDN references for Bootstrap and Font Awesome
- Updated image selector to use the correct file paths for JavaScript and CSS resources
- Fixed placeholder image references in the image selector

## [3.35.51] - 2025-06-20

### Fixed
- Fixed JavaScript error "Uncaught TypeError: calendar.refetchEvents is not a function" by using the correct method name loadEvents() for CustomCalendar class
- Fixed issue with calendar filters not working correctly due to method name mismatch

## [3.35.50] - 2025-06-19

### Added
- Added missing getCategories() and getTags() methods to CalendarModel
- Added automatic table creation for event categories and tags if they don't exist
- Added improved error handling in calendar-filters.js for API responses

### Fixed
- Fixed issue with categories and tags not loading in calendar filters
- Improved error handling when API endpoints return non-JSON responses

## [3.35.49] - 2025-06-18

### Added
- Added marker customization options for map view
- Added ability to customize marker type (default circle, pin, or custom image)
- Added settings for marker size, color, and border options
- Added marker preview in map settings page
- Added validateCsrfToken function to CSRF helper

### Fixed
- Fixed database query execution in CalendarModel
- Fixed CSRF token validation in map settings form
- Improved error handling in map settings controller

## [3.35.48] - 2025-06-17

### Fixed
- Fixed critical geocoding issue in CalendarController where coordinates weren't being saved to database
- Added missing lat/lng fields to SQL statements in CalendarModel's updateEvent and createEvent methods
- Fixed inconsistency between admin batch geocoding and individual event geocoding
- Ensured geocoding data is properly formatted before processing
- Standardized address data preparation across all geocoding operations

### Improved
- Enhanced geocoding consistency between batch operations and individual event editing
- Improved address data handling with proper defaults for all fields
- Added more robust error checking for geocoding results
- Updated event creation and editing to match admin batch tool's geocoding approach
- Enhanced data synchronization between shows and events with proper coordinate handling

## [3.35.47] - 2024-08-26

### Added
- Enhanced geocoding system with improved address handling and multiple provider support
- Added new admin interface for batch geocoding events with missing coordinates
- Created geocoding_helper.php v1.2 with address simplification and retry logic
- Added support for Google Maps, Mapbox, HERE Maps, and OpenStreetMap geocoding
- Implemented command-line scripts for batch geocoding of events

### Improved
- Enhanced geocoding reliability with fallback to multiple providers
- Added address simplification to improve geocoding success rates
- Implemented retry logic for geocoding to handle temporary failures
- Enhanced event creation and editing with automatic coordinate generation
- Added comprehensive debug logging for geocoding operations when DEBUG_MODE is enabled
- Integrated geocoding tools into admin settings interface

### Fixed
- Fixed issues with missing coordinates in event map views
- Improved handling of incomplete or ambiguous addresses
- Added better error handling for geocoding failures
- Resolved potential JavaScript errors related to Google Maps API loading

## [3.37.2] - 2025-08-25

### Added
- Implemented state-specific calendars for car shows
- Added automatic creation of state calendars when shows are created
- Enhanced show-to-calendar integration with improved address synchronization

### Improved
- Shows now automatically create events in their state's calendar
- Enhanced address data synchronization using custom field values
- Improved coordinator attribution for calendar events
- Added proper handling of address components (address1, address2, city, state, zipcode)

## [3.37.1] - 2024-09-04

### Fixed
- Fixed custom calendar implementation to properly initialize and display events
- Resolved issues with event loading and display in custom calendar
- Fixed event filtering with multiple calendars
- Corrected event modal display and interaction
- Fixed drag-and-drop functionality for events
- Resolved issues with upcoming events sidebar

### Improved
- Enhanced custom calendar initialization with better error handling
- Improved event data processing for more reliable display
- Optimized calendar rendering for better performance
- Enhanced mobile responsiveness with improved layout adjustments
- Added better debugging and error logging for calendar operations

## [3.37.0] - 2024-09-03

### Added
- Implemented custom-built calendar system to replace FullCalendar integration
- Created responsive, mobile-first calendar with month, week, day, and list views
- Added custom CSS for enhanced calendar styling and mobile responsiveness
- Implemented custom JavaScript calendar class with advanced event handling
- Added support for multiple calendar selection and filtering
- Enhanced calendar with upcoming events sidebar
- Implemented drag-and-drop event management
- Added support for event details modal

### Improved
- Enhanced mobile responsiveness for all calendar views
- Improved calendar performance with optimized rendering
- Added support for multiple calendar IDs in event filtering
- Enhanced event display with color coding and better formatting
- Improved calendar navigation with intuitive controls

## [3.36.1] - 2024-09-02

### Fixed
- Fixed deprecated FILTER_SANITIZE_STRING usage in CalendarController.php
- Fixed undefined method commitTransaction() in CalendarModel.php
- Fixed FullCalendar plugin loading issue by adding required plugins (dayGrid, timeGrid, list, interaction)
- Resolved "viewType 'month' is not available" error in calendar view

### Improved
- Updated CalendarController.php to use FILTER_SANITIZE_FULL_SPECIAL_CHARS for better security
- Updated CalendarModel.php to use correct transaction method names (commit and rollBack)
- Enhanced FullCalendar initialization with explicit plugin registration

## [3.36.0] - 2024-09-01

### Added
- Added comprehensive Calendar System with event management
- Implemented multiple calendar support with color coding and visibility settings
- Added venue management for event locations
- Added club/group management with membership functionality
- Implemented event privacy settings and recurring events
- Added calendar import/export functionality (iCal, Facebook, Shows)
- Created interactive calendar views (month, week, day, list)
- Added calendar settings for customizing display options
- Integrated calendar system with existing show management

### Improved
- Enhanced show management with calendar integration
- Improved event scheduling with recurring event support
- Added venue management for better location tracking
- Enhanced user experience with interactive calendar interface

## [3.35.45] - 2024-08-30

### Fixed
- Fixed Facebook login redirect URI issue by removing the `.php` extension
- Updated configuration to match the URI format expected by Facebook Developer Console
- Ensured consistent redirect URI format across the application

## [3.35.44] - 2024-08-29

### Fixed
- Fixed session timeout issues with enhanced session lifetime management
- Improved session cookie handling for both modern and legacy PHP versions
- Added session refresh mechanism to prevent premature session expiration
- Enhanced session security with periodic session ID regeneration
- Fixed Facebook session handling to properly maintain long-term sessions

### Improved
- Added detailed session debugging for troubleshooting
- Enhanced session expiration message with helpful suggestions
- Improved session cookie security settings (httponly, secure, samesite)
- Added automatic session refresh during active user browsing

## [3.36.0] - 2024-08-28

### Added
- Added ability to set primary/featured images for shows
- Added ability to set banner images for shows
- Added visual indicators for primary and banner images in the show image editor
- Added new methods to ShowModel: updateFeaturedImage and updateShowBannerImage

### Fixed
- Fixed error in ImageEditorController when setting primary images for shows
- Added proper database initialization in ImageEditorController
- Enhanced error handling for image updates

### Improved
- Enhanced show image editor UI with clearer image role indicators
- Improved dropdown menus for image actions with active state indicators

## [3.35.0] - 2024-08-26

### Added
- Added time component to show date fields (start_date, end_date, registration_start, registration_end)
- Updated form templates to use datetime inputs instead of date inputs
- Enhanced date handling in show creation and editing forms

### Changed
- Modified database schema to use DATETIME instead of DATE for show time fields
- Updated form templates to display and edit time components
- Improved date/time formatting in views

### Fixed
- Fixed registration period validation to consider time component
- Updated datetime field handling in add_with_template.php and edit_with_template.php
- Ensured proper datetime format for HTML datetime-local inputs

## [3.35.43] - 2024-08-28

### Fixed
- Fixed state and city dropdowns displaying incorrect values
- Added special handling for USA locations
- Improved state detection in various address formats
- Enhanced city extraction from complex location strings
- Fixed default state value when no state is found in the location

### Improved
- Better display of state names in dropdown menus
- Enhanced location parsing with more robust pattern matching
- Added detailed debug logging for location parsing process
- Improved filtering accuracy for locations with partial information

## [3.35.42] - 2024-08-28

### Improved
- Enhanced location parsing for show filtering with more accurate city and state extraction
- Improved state and city filtering to handle various address formats
- Added better matching for state abbreviations and full state names
- Enhanced city dropdown to dynamically update based on selected state
- Added detailed debug logging for location parsing and filtering

### Fixed
- Fixed issues with state and city filtering not matching correctly
- Improved address parsing to handle various formats including street addresses
- Enhanced filtering logic to match partial location information

## [3.35.41] - 2024-08-27

### Fixed
- Fixed session cookie lifetime to properly respect configured session lifetime settings
- Improved session handling to maintain login state across browser sessions
- Enhanced session security with proper cookie parameters
- Added fallback for session lifetime when constant is not defined in config

## [3.35.40] - 2024-08-25

### Added
- Added configurable session lifetime settings in Developer Tools
- Implemented "Remember Me" functionality for persistent login
- Added session expiration handling with automatic logout
- Created remember_tokens table for secure persistent login storage

### Fixed
- Fixed session management to respect configured session lifetime
- Enhanced security by properly handling session expiration
- Improved Facebook login session handling with separate lifetime setting
- Fixed "Remember Me" checkbox functionality to properly store login tokens

### Improved
- Added session lifetime configuration in admin developer settings
- Enhanced user experience with persistent login option
- Added clear notification when session expires for security reasons
- Implemented secure token storage for "Remember Me" functionality

## [3.35.39] - 2024-08-24

### Fixed
- Fixed critical issue with Facebook login automatically re-syncing deleted profile images
- Modified Auth class to respect user's choice to use default image
- Prevented automatic profile image download during Facebook login
- Added explicit check for default image before downloading from Facebook

### Improved
- Enhanced Facebook authentication process to respect user preferences
- Added detailed logging for Facebook image download decisions
- Optimized login flow to maintain consistent user experience
- Improved handling of user preferences across login sessions

## [3.35.38] - 2024-08-24

### Fixed
- Fixed persistent issue with Facebook images re-syncing after logout/login
- Enhanced image selection logic to prioritize user preferences
- Improved detection of existing images in the database
- Prevented automatic Facebook image downloads for users with deleted images

### Improved
- Added database check for existing images before attempting Facebook download
- Enhanced image source selection logic for better user experience
- Optimized Facebook image helper to respect user choices consistently
- Improved logging for better debugging of image selection process

## [3.35.37] - 2024-08-24

### Improved
- Enhanced UserModel to include facebook_id in user data
- Optimized profile view to use existing user data without extra queries
- Improved data consistency across user-related methods
- Streamlined Facebook integration throughout the application

### Fixed
- Fixed issue with Facebook sync button not displaying on profile page
- Prevented automatic re-syncing of Facebook images after user deletion
- Respected user's choice to use default image instead of Facebook image
- Eliminated unnecessary database queries in profile view

## [3.35.34] - 2024-08-24

### Fixed
- Fixed profile image deletion to properly handle Facebook images
- Enhanced deleteProfileImage method to clear Facebook download cache
- Updated default profile image path to use existing image
- Prevented Facebook fallback after image deletion

### Improved
- Added comprehensive debugging to profile image deletion process
- Implemented proper cleanup of all image-related caches
- Enhanced user experience by showing default image after deletion
- Added fallback to default image when profile image is deleted

## [3.35.33] - 2024-08-24

### Fixed
- Fixed critical issue with Facebook image file storage
- Enhanced ImageEditorModel to handle both uploaded and local files
- Fixed file path handling in image upload process
- Resolved issue with move_uploaded_file failing for local files

### Improved
- Added comprehensive debugging to ImageEditorModel
- Implemented proper path handling for both relative and absolute paths
- Enhanced error logging for image processing
- Added fallback methods for file copying when move_uploaded_file fails

## [3.35.32] - 2024-08-24

### Fixed
- Fixed Facebook image download and storage functionality
- Enhanced image download process with multiple fallback methods
- Added extensive debugging to troubleshoot image download issues
- Fixed file path handling for proper image storage

### Improved
- Significantly enhanced error logging for Facebook image downloads
- Reduced cache timeout for download attempts from 24 hours to 1 hour
- Increased HTTP request timeouts for better reliability
- Added multiple download methods with proper fallbacks

## [3.35.31] - 2024-08-24

### Added
- Implemented local storage for Facebook profile images
- Added functionality to download and store Facebook images on the server
- Integrated with ImageEditorModel for proper image processing and storage

### Improved
- Significantly enhanced Facebook image loading performance
- Reduced external dependencies by storing images locally
- Added caching mechanism to prevent repeated download attempts
- Implemented proper error handling and logging for image downloads

### Fixed
- Fixed performance issues with Facebook profile image loading
- Addressed site slowdown caused by external Facebook image requests
- Improved reliability by storing images locally instead of relying on Facebook

## [3.35.30] - 2024-08-24

### Improved
- Significantly optimized Facebook image helper for better performance
- Reduced database queries by using a single optimized query
- Implemented in-memory caching to prevent repeated database calls
- Eliminated data URI conversion for Facebook images to reduce payload size
- Reduced HTTP request timeouts to prevent page load delays

### Fixed
- Fixed performance issues with Facebook profile image loading
- Addressed site slowdown caused by Facebook image processing
- Improved overall page load times across the application

## [3.35.29] - 2024-08-24

### Fixed
- Fixed "Attempt to read property 'id' on false" error in user profile page
- Added proper null checking for profile image objects
- Improved error handling in profile image display

## [3.35.28] - 2024-08-24

### Added
- Updated header menu to use the Facebook image helper
- Updated admin shows manage_staff.php to use the Facebook image helper

### Changed
- Improved profile image display in the header navigation menu
- Enhanced profile image display in the user profile page
- Ensured consistent profile image display across the entire application

### Fixed
- Fixed profile image display in the header navigation menu
- Fixed profile image display in admin shows staff management view
- Ensured all profile images display correctly throughout the application

## [3.35.27] - 2024-08-24

### Added
- Updated coordinator's manage_staff.php to use the Facebook image helper

### Changed
- Removed manual Facebook sync button from user profile page
- Simplified user interface by automating Facebook image synchronization
- Improved user experience with automatic profile image handling

### Fixed
- Fixed profile image display in coordinator's staff management view
- Streamlined profile image handling across the application

## [3.35.26] - 2024-08-24

### Added
- Integrated Facebook profile image solution directly into the application
- Updated all profile image displays to use the new helper functions
- Implemented robust error handling for Facebook image helper

### Fixed
- Fixed dependency issues with Facebook image helper
- Made helper functions self-contained without external model dependencies
- Added fallback mechanisms for when helper files are missing
- Improved error handling for Facebook profile image display

## [3.35.25] - 2024-08-24

### Added
- Created FacebookImageModel for handling Facebook profile images
- Added facebook_image_helper.php with helper functions for displaying profile images
- Implemented data URI conversion for Facebook profile images
- Added getUserProfileImageUrl function to get profile images as data URIs

### Fixed
- Fixed issue with Facebook profile images displaying as generic silhouettes
- Implemented solution using data URIs to display actual profile images
- Added fallback mechanisms for profile image display
- Created a complete solution for Facebook profile image integration

## [3.35.24] - 2024-08-24

### Added
- Created Facebook Image Viewer tool (test/facebook_image_viewer.php) to diagnose image display issues
- Added data URI conversion functionality to display Facebook profile images correctly
- Implemented detailed image comparison and analysis tools
- Added code examples for implementing Facebook profile image display solutions

### Fixed
- Fixed issue with Facebook profile images displaying as generic silhouettes
- Implemented solution using data URIs to display actual profile images
- Added detailed diagnostics for image content types and dimensions
- Enhanced test scripts with links to the new image viewer tool

## [3.35.23] - 2024-08-24

### Added
- Created simplified Facebook image test script (test/simple_facebook_image_test.php) with no dependencies
- Added dependency handling and error recovery to test scripts
- Enhanced test scripts with better error reporting and visual styling

### Fixed
- Fixed dependency issues in test scripts by adding proper includes
- Added fallback mechanisms when dependencies are missing
- Improved error handling for missing classes and failed includes
- Enhanced test scripts to work in various server environments

## [3.35.22] - 2024-08-24

### Added
- Added Facebook ID finder tool (test/find_facebook_id.php) to help users find their Facebook ID
- Created diagnostic tool (test/script_runner_diagnostic.php) to troubleshoot Admin Script Runner issues
- Enhanced Admin Script Runner with multiple directory scanning methods for improved reliability
- Added detailed error logging and user feedback for script runner issues

### Fixed
- Fixed Admin Script Runner to properly detect and list scripts in the test directory
- Improved error handling and feedback when no scripts are found
- Enhanced directory scanning with multiple fallback methods
- Updated documentation for Facebook profile image sync with new test tools

## [3.35.21] - 2024-08-24

### Added
- Added comprehensive debugging for Facebook image synchronization
- Created test script (test/test_facebook_image_download.php) to diagnose Facebook image download issues
- Added multiple fallback methods for image downloading with detailed error logging
- Enhanced error reporting for file system operations

### Fixed
- Fixed path handling in Facebook profile image synchronization to use absolute paths
- Improved error handling for file system operations in image processing
- Added directory existence and permission checks before file operations
- Disabled SSL verification for compatibility with various server configurations
- Added detailed logging throughout the image download and processing workflow

## [3.35.20] - 2024-08-24

### Added
- Added new `processDownloadedImage` method to ImageEditorModel to handle images downloaded from URLs
- Enhanced image handling capabilities to support both form uploads and direct URL downloads

### Fixed
- Fixed Facebook profile image synchronization by implementing direct image processing
- Implemented direct Facebook Graph API profile picture URL construction using Facebook ID
- Added robust image download functionality with multiple fallback methods
- Improved error handling and detailed logging for Facebook profile image synchronization
- Removed dependency on the Auth class's private downloadFacebookProfileImage method
- Simplified the synchronization process to be more reliable and maintainable

## [3.35.18] - 2024-08-23

### Fixed
- Fixed Facebook profile image sync functionality to use consistent storage approach
- Standardized profile image storage to use the images table instead of the profile_image column in users table
- Modified Auth.php to clear the profile_image field after successfully downloading Facebook profile images
- Updated UserController.php to ensure consistent image storage during manual Facebook profile sync
- Created migration script to move existing Facebook profile images from users table to images table
- Improved error handling and logging for Facebook profile image synchronization
- Created backup of modified files in autobackup/facebook_profile_sync directory

## [3.35.18] - 2024-08-23

### Fixed
- Fixed Facebook profile image sync functionality to use consistent storage approach
- Standardized profile image storage to use the images table instead of the profile_image column in users table
- Modified Auth.php to clear the profile_image field after successfully downloading Facebook profile images
- Updated UserController.php to ensure consistent image storage during manual Facebook profile sync
- Created migration script to move existing Facebook profile images from users table to images table
- Improved error handling and logging for Facebook profile image synchronization
- Created comprehensive documentation about the storage approach in autobackup/facebook_profile_sync directory

### Added
- Added script placement guidelines documentation in docs/script_placement_guidelines.md
- Created SYSTEM_INIT.md guide for properly initializing the system in scripts and applications
- Updated structure.md to document the script directory structure and placement rules
- Established clear rules for placing scripts in /test/, /scripts/, or core directories based on their purpose and lifecycle
- Fixed initialization approach in migrate_facebook_profile_images.php script to use APPROOT for all file includes
- Fixed path to uploads directory in migrate_facebook_profile_images.php to use APPROOT
- Corrected system initialization documentation to use proper path handling with APPROOT
- Added documentation about script access control for administrative scripts
- Updated security requirements in script_placement_guidelines.md to enforce admin-only access
- Integrated script runner functionality into the admin dashboard at /admin/runScripts
- Added Developer Tools section to the admin dashboard with links to maintenance scripts
- Fixed admin access control to use hasRole('admin') instead of isAdmin() method
- Documented that our hosting environment doesn't provide SSH access, requiring all scripts to be executed via web browser
- Updated script examples to reflect web-only execution environment
- Removed CLI-specific code from script examples and documentation

## [3.35.17] - 2024-08-22

### Fixed
- Updated Facebook SDK requirement in composer.json from `^5.7` to `joelbutcher/facebook-graph-sdk: ^6.0` to support PHP 8.4.8
- Fixed compatibility issue between Facebook SDK 5.7.0 and PHP 8.4.8
- Created backup of original composer.json in autobackup/facebook_sdk_update directory
- Switched to joelbutcher/facebook-graph-sdk which is a fork of the original Facebook SDK that supports PHP 8.0 or greater

## [3.35.16] - 2024-08-21

### Added
- Added ability to synchronize user profile images with Facebook profile images
- Implemented "Sync with Facebook" button on user profile page for users with Facebook accounts
- Added "Use Facebook Profile Image" button in the profile image upload section
- Created syncFacebookImage method in UserController to handle the synchronization process
- Added facebook_token column to users table for API access
- Created SQL update script for database changes
- Created update_facebook_profile_sync.php script for easy deployment
- Added comprehensive error handling for Facebook API interactions
- Created backups of modified files in autobackup/facebook_profile_sync directory
- Updated README.md and CHANGELOG.md with information about the new feature

## [3.35.13] - 2024-08-20

### Fixed
- Enhanced phone number search to work with partial area codes and phone numbers
- Added special handling for numeric-only searches to prioritize phone number matching
- Implemented direct phone number query for numeric searches
- Added phone number format detection to better understand database storage formats
- Improved phone number cleaning to handle various formatting styles
- Added additional debug logging for phone number formats in the database
- Fixed issue where searching for partial phone numbers returned no results

## [3.35.12] - 2024-08-20

### Fixed
- Completely redesigned user search functionality to fix issues with partial name searches
- Added new advancedUserSearch method with improved matching capabilities
- Implemented case-insensitive search for names and emails
- Added special handling for exact email matches
- Improved phone number search to handle various formats
- Added result ranking to prioritize exact matches and starts-with matches
- Enhanced debug information to show total user count in the system
- Added fallback search methods when primary search returns no results
- Fixed issue where searching for partial names returned no results

## [3.35.11] - 2024-08-20

### Fixed
- Enhanced user search functionality to handle different phone number formats
- Added support for searching phone numbers with or without special characters
- Improved search query to be more flexible with phone number formats
- Added detailed debug information to help troubleshoot search issues
- Enhanced error messages to provide more helpful information to users
- Added fallback to direct email lookup when search by email returns no results
- Added comprehensive logging for database queries to aid in troubleshooting

## [3.35.10] - 2024-08-20

### Fixed
- Fixed "Error processing search results" issue in staff registration user search
- Corrected method name from isAjaxRequest to isAjax in StaffController
- Added proper X-Requested-With header to AJAX requests
- Enhanced error handling and debugging in search functionality
- Improved JSON response handling with explicit content type headers
- Added robust error handling in UserModel searchUsers method
- Fixed data formatting in search results to ensure consistent JSON structure

## [3.35.9] - 2024-08-20

### Fixed
- Fixed issue with user search returning "undefined" in staff registration interface
- Added missing searchUsers method to UserModel
- Implemented live search functionality that triggers as the user types (with debounce)
- Improved error handling for JSON parsing in search results
- Enhanced user feedback during search operations
- Created backup of UserModel.php in autobackup/staff_registration_fix directory

## [3.35.14] - 2024-08-19

### Fixed
- Fixed issue with vehicle dropdown not populating in staff registration form
- Added getVehiclesByOwner method to VehicleModel as an alias to getUserVehicles for backward compatibility
- Fixed duplicate HTML IDs in create_registration.php template
- Enhanced form_helper.php to support both 'vehicles' and 'user_vehicles' data keys
- Improved error handling in VehicleModel's loadModel method

### Added
- Added persistent user indicator at the top of the staff registration form
- Implemented mobile-friendly design for the user indicator
- Added "Change User" button for easy navigation back to user selection
- Removed redundant user information alerts from each step
- Created backups of modified files in autobackup/staff_registration_fix directory

## [3.35.13] - 2024-08-19

### Fixed
- Fixed issue with hidden search data not appearing in the HTML source
- Made hidden search column visible during debugging to verify data
- Added comprehensive debug output to troubleshoot search functionality
- Enhanced search data collection to ensure all fields are properly included
- Improved error handling for missing or undefined registration properties
- Added property_exists checks to safely access registration object properties
- Added detailed debug information in HTML comments for easier troubleshooting
- Created backups of modified files in autobackup/registration_search_enhanced directory

## [3.35.12] - 2024-08-19

### Added
- Added live search functionality to the registrations table in staff view
- Implemented real-time filtering as users type in the search box
- Added ability to search by display number, owner name, email, phone, license plate, make, model, and year
- Created a dedicated registration-search.js file for better code organization
- Added clear button for easy search reset
- Updated RegistrationModel to include email, phone, and license plate data in query results
- Created backups of modified files in autobackup directory

### Fixed
- Fixed DataTables not loading by adding the required CSS and JS files directly to the registrations page
- Fixed incorrect path to registration-search.js file (changed from /js/ to /public/js/)
- Fixed backup file path to use a static directory name instead of PHP code
- Fixed "toLowerCase is not a function" error by simplifying the search functionality
- Implemented hidden column with searchable data (email, phone, license plate, make, model, year)
- Configured DataTables to include hidden column in search results
- Replaced custom search implementation with DataTables' built-in search functionality
- Added debug logging to help troubleshoot search functionality issues
- Improved overall reliability and performance of the search feature

## [3.35.11] - 2024-08-18

### Changed
- Simplified display number badges by removing the word "Display" for cleaner presentation
- Changed column header from "Display #" to "Car #" in registrations table
- Maintained the # symbol before the number for clarity

## [3.35.10] - 2024-08-18

### Added
- Added display number badge to registration details page header for better visibility
- Replaced ID column with display number in registrations table
- Added fallback to show registration ID when display number is not available
- Updated DataTable configuration to properly sort the display number column
- Created backups of modified files in autobackup/2024-08-18 directory

## [3.35.9] - 2024-08-18

### Fixed
- Fixed "The requested view 'staff/edit_registration' could not be found" error when clicking edit registration
- Created missing edit_registration.php view file in the staff directory
- Fixed QR code image not displaying by adding the path /uploads/qrcodes/ to the image filename
- Created backups of modified files in autobackup/2024-08-18 directory

## [3.35.8] - 2024-08-18

### Fixed
- Fixed "Call to undefined method RegistrationModel::checkInVehicle()" error in StaffController.php
- Added missing checkInVehicle() and undoCheckInVehicle() methods to RegistrationModel
- Implemented proper error handling and logging for vehicle check-in operations
- Created backup of added methods in autobackup/2024-08-18/RegistrationModel_checkInVehicle.php
- Ensured compatibility with existing check-in functionality in AdminController

## [3.35.7] - 2024-08-18

### Fixed
- Fixed "SQLSTATE[HY093]: Invalid parameter number" error in RegistrationModel::updatePayment method
- Modified SQL query to use a unique parameter name (:payment_status_case) for the duplicate parameter in the CASE statement
- Added binding for the new parameter while maintaining the same value
- Created backup of original RegistrationModel.php in autobackup/2024-08-18 directory
- Ensured SQL query execution works correctly with proper parameter binding

## [3.35.6] - 2024-08-18

### Fixed
- Fixed "Call to undefined method RegistrationModel::updatePaymentStatus()" error in StaffController.php
- Updated StaffController to use the existing updatePayment() method instead of the non-existent updatePaymentStatus() method
- Added proper parameter structure for the updatePayment method with all required fields
- Created backup of original StaffController.php in autobackup/2024-08-18 directory
- Ensured payment status is correctly updated after manual payment processing

## [3.35.5] - 2024-08-18

### Fixed
- Fixed "Undefined array key" warnings for user_id, payment_type, and related_id in PaymentModel.php
- Added null coalescing operators to safely handle missing array keys in processManualPayment method
- Updated StaffController to properly set all required payment data fields
- Created backups of original files in autobackup/2024-08-18 directory
- Maintained backward compatibility with existing payment processing workflows

## [3.35.4] - 2024-08-17

### Fixed
- Fixed "Undefined property: stdClass::$vehicle_year/vehicle_make/vehicle_model" warnings in staff/process_payment.php
- Updated property names to match the actual field names returned by the database query (year, make, model)
- Created backup of original process_payment.php in autobackup directory
- Fixed "Invalid request" error when clicking process payment button

## [3.35.3] - 2024-08-16

### Fixed
- Fixed "Undefined property: stdClass::$vehicle_year/vehicle_make/vehicle_model" warnings in staff/registrations.php
- Updated property names to match the actual field names returned by the database query (year, make, model)
- Created backup of original registrations.php in autobackup directory

## [3.35.2] - 2024-08-16

### Fixed
- Fixed "Call to undefined method RegistrationModel::getRegistrationsByShow()" error in StaffController.php
- Updated method call to use the correct method name getShowRegistrations()
- Created backup of original StaffController.php in autobackup directory

## [3.35.1] - 2024-08-16

### Fixed
- Fixed "Undefined property: stdClass::$category_id" warning in staff/show.php by adding category_id field to the countRegistrationsByCategory query results
- Created backup of original RegistrationModel.php in autobackup directory
- Updated SQL queries in RegistrationModel to include category_id in the result set

## [3.35.0] - 2024-08-16

### Added
- Added new "Staff" role to the role hierarchy system
- Created StaffModel for managing staff assignments to shows
- Created StaffController with dashboard and functionality for staff members
- Added staff assignment system to link staff to specific shows
- Added ability for Coordinators and Admins to assign staff to shows
- Created staff views for dashboard, show details, registrations, and payment processing
- Added staff management interfaces for Admins
- Updated Auth class to include the new staff role
- Created SQL script for staff_assignments table with and without foreign key constraints
- Added enhanced error handling in StaffModel for database operations
- Created detailed documentation for staff role database setup
- Updated documentation to reflect the new role system
- Added staff role to the admin role management interface
- Updated role descriptions to include staff capabilities

### Improved
- Redesigned the admin role management interface with mobile-first responsive design
- Added card-based layout for role overview on all screen sizes
- Implemented accordion-style role descriptions for mobile devices
- Created card-based user lists for mobile view and table-based lists for desktop
- Enhanced visual hierarchy with consistent color coding for roles
- Improved spacing and typography for better readability on all devices
- Added Staff Dashboard link to the navigation menu for both admin and staff users

## [3.34.55] - 2024-08-15

### Added
- Added restriction preventing coordinators from editing shows or changing show status when listing fee is unpaid
- Added automatic redirection to payment page when coordinators attempt to edit shows with unpaid listing fees
- Added isListingFeePaid method to ShowModel to check payment status of listing fees
- Created automatic backup of CoordinatorController.php in autobackup directory

## [3.34.54] - 2024-08-15

### Fixed
- Fixed file path error in use_code.php - corrected path from '/views/inc/header.php' to '/views/includes/header.php'
- Fixed file path error in admin/migrations/payment_settings.php - corrected path from '/views/inc/header.php' to '/views/includes/header.php'
- Created automatic backups of original files in autobackup directory
- Fixed "Failed to open stream: No such file or directory" error in payment code redemption page and admin migration page

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.53] - 2024-08-14

### Fixed
- Fixed coordinator field visibility in the add show form - coordinators no longer see the coordinator field and dropdown
- Ensured coordinator ID is automatically set to the current user when a coordinator creates a show

## [3.34.52] - 2024-08-13

### Fixed
- Fixed PHP deprecation warning in financial_report.php by adding null checks to number_format() calls
- Added null coalescing operator (??) to handle null values in revenue data
- Created backup of original file in autobackup directory

## [3.34.51] - 2024-08-09

### Fixed
- Fixed "Manual Payment" link not showing in the actions dropdown menu
- Added debug output to troubleshoot the is_free value
- Removed the is_free condition to ensure the manual payment option is always available
- Ensured the manual payment option appears for all pending/unpaid registrations

## [3.34.50] - 2024-08-09

### Changed
- Moved "Manual Payment" link from registration details page to actions dropdown menu in registrations table
- Improved user experience by placing the manual payment option in a more logical location
- Removed debug output that was added for troubleshooting
- Maintained the same conditional logic for displaying the manual payment option

## [3.34.49] - 2024-08-09

### Fixed
- Fixed "Manual Payment" button not showing by adding debug output and correcting the condition
- Updated payment status check to include both 'pending' and 'unpaid' statuses
- Added explicit integer comparison for is_free value (== 1 instead of == '1')
- Improved visibility of manual payment option for free shows

## [3.34.48] - 2024-08-09

### Changed
- Modified the display condition for the "Manual Payment" button to only show for free shows
- Updated the logic to check for is_free='1' in the shows table
- Refined the user interface to better match business requirements

## [3.34.47] - 2024-08-09

### Added
- Added "Manual Payment" button to admin registration view for unpaid registrations
- Improved accessibility of manual payment functionality for administrators
- Enhanced user experience by providing direct access to manual payment processing

## [3.34.46] - 2024-08-09

### Added
- Created comprehensive project documentation files for better portability
- Added structure.md with detailed project structure information
- Added features.md listing all implemented features and their status
- Added task_pending.md with features planned for future development
- Added docs/manual_payment_system.md with detailed documentation of the manual payment system

## [3.34.45] - 2024-08-09

### Fixed
- Fixed "Call to undefined function generateCsrfInput()" error in admin listing views
- Added missing generateCsrfInput() function to csrf_helper.php
- Created backup of original csrf_helper.php in autobackup directory

## [3.34.44] - 2024-08-09

### Changed
- Replaced "Customize Registration Form" card with "Configure Show Settings" card in the admin Next Steps section
- Added role-based visibility for the Next Steps section - now only visible to administrators, hidden for coordinators
- Improved UI clarity and user experience for different user roles

## [3.34.43] - 2024-08-08

### Fixed
- Fixed fatal error: "Cannot redeclare ShowModel::$systemFields" in ShowModel.php
- Removed duplicate declarations of $systemFields and $standardFields properties
- Merged system fields from both declarations to maintain functionality
- Created backup of original file in autobackup directory

## [3.34.42] - 2024-08-08

### Fixed
- Fixed missing category column in settings table in create_listing_fee_tables.php script
- Added check for category column existence and dynamic SQL generation
- Added ALTER TABLE statement to add category column if needed
- Created final backup of fully fixed script in autobackup directory

## [3.34.41] - 2024-08-08

### Fixed
- Fixed missing config include in create_listing_fee_tables.php script
- Added require_once for config.php to ensure database constants are defined
- Created additional backup of fixed script in autobackup directory

## [3.34.40] - 2024-08-08

### Fixed
- Fixed path error in create_listing_fee_tables.php script (changed from /libraries/Database.php to /core/Database.php)
- Created backup of original script in autobackup directory

## [3.34.39] - 2024-08-07

### Added
- Enhanced coordinator's show creation with template-based form system
- Added listing fee display and payment integration to show creation
- Added automatic coordinator assignment for shows created by coordinators
- Added mobile-first responsive design to show creation form
- Added improved date handling with automatic date suggestions
- Added "Next Steps" section to guide coordinators after show creation
- Added support for custom fields in show creation form

### Fixed
- Fixed field naming inconsistencies between registration_open_date and registration_start
- Fixed undefined array key errors in create_with_template.php
- Improved error handling in form submission
- Enhanced mobile responsiveness of the show creation form
- Fixed redirect after show creation to handle listing fee payment when needed

## [3.34.38] - 2024-08-07

### Added
- Implemented complete reports functionality in the coordinator section
- Added registration report with detailed statistics and category breakdowns
- Added financial report with revenue tracking by category and payment method
- Added judging report with winners display and judge performance metrics
- Added show report with comprehensive show statistics and recent registrations
- Added data export functionality for all reports
- Created mobile-responsive report views with interactive elements
- Added SQL update script to ensure show_categories table exists with proper structure
- Added phone and address fields to user profiles
- Enhanced user registration and profile forms with contact information fields
- Added SQL update script to add phone, address, city, state, and zip fields to users table

### Fixed
- Fixed non-functioning reports view in the coordinator section
- Implemented missing report controller methods (registrationReport, financialReport, judgingReport, showReport)
- Fixed database table references to use show_categories instead of categories
- Fixed user name field references to use the single name column instead of first_name/last_name
- Added proper data retrieval and processing for all report types
- Enhanced database queries to include registration counts and financial data
- Improved show data display with proper formatting for dates and currency values
- Ensured proper access control for all report views
- Fixed CSV export to use the correct column names
- Fixed "Unknown column 'u.phone'" error in export registrations functionality

## [3.34.37] - 2024-08-07

### Added
- Added missing createShow method to CoordinatorController
- Added missing reports method to CoordinatorController
- Created new views for show creation and reports functionality
- Implemented proper form validation for show creation

### Fixed
- Fixed non-functioning "Create New Show" button in coordinator dashboard
- Fixed non-functioning "Reports" button in coordinator dashboard
- Removed redundant Payment Settings section from dashboard (already available in Quick Links)
- Maintained all essential navigation options in Quick Links section
- Improved mobile responsiveness with properly sized buttons

## [3.34.36] - 2024-08-05

### Added
- Separated admin and coordinator payment settings
- Added is_admin flag to payment_settings and show_payment_settings tables
- Enhanced coordinator payment settings interface
- Added clear separation between admin listing fee settings and coordinator registration fee settings

### Fixed
- Fixed security issue with payment credentials visibility
- Improved payment settings management for both admins and coordinators
- Enhanced payment security with proper credential isolation

## [3.34.33] - 2024-08-01

### Added
- Added show-specific payment methods and settings
- Added support for Cash and Free payment methods
- Added manual payment processing capabilities
- Added payment approval workflow for coordinators and admins
- Added format_helper.php with currency, date, and number formatting functions

### Fixed
- Fixed SQL update scripts to be compatible with MySQL 8.0.41
- Fixed SQL syntax issues in database update scripts
- Improved database update process with better error handling
- Created fixed versions of all SQL update scripts
- Fixed "Call to undefined function formatCurrency()" error in coordinator views

## [3.34.32] - 2024-07-10

### Changed
- Updated button text from "QR Codes" to "Dash Card" in the fan votes page while maintaining the same icon

## [3.34.31] - 2024-07-28

### Changed
- Enhanced user and admin registrations pages to display vehicle primary images instead of generic fa-car icons
- Implemented consistent image display across both user and admin interfaces
- Improved visual representation of vehicles in all registration lists
- Added multi-source image retrieval (direct vehicle_image and database images table)
- Prioritized thumbnail images for better performance and faster page loading
- Added proper path handling for image URLs with leading slash management
- Added file existence verification to ensure images are displayed only when files exist
- Added fallback to fa-car icon when no vehicle image is available
- Added debug mode information for troubleshooting image display issues

### Fixed
- Fixed syntax error in user registrations page that was causing an extra "?>" to appear between vehicle image and name

## [3.34.30] - 2024-07-27

### Added
- Implemented comprehensive profile image management for users
- Added ability to upload profile images directly from the user profile page
- Added profile image deletion functionality with confirmation dialog
- Integrated with the Image Manager for advanced image editing capabilities
- Added "Use Advanced Image Uploader" option for more control over image uploads
- Created dedicated uploads/users directory with proper security settings

### Changed
- Enhanced user profile page with improved image display and management options
- Updated UserController with new methods for profile image handling
- Leveraged existing images table with entity_type='user' for profile images
- Enforced one-image-only policy for user profile images
- Automatically removes old profile image when a new one is uploaded
- Added profile image display in user profile header
- Added profile image to navigation dropdown menu
- Added profile image to user dashboard profile card
- Improved mobile responsiveness of the profile image section
- Updated README.md to document the new profile image features

### Fixed
- Fixed .htaccess configuration to properly allow image display from uploads directory
- Fixed undefined variable error in dashboard.php by using session user ID
- Ensured proper error handling for image upload and deletion operations
- Added CSRF protection to all image management operations
- Ensured consistent use of the Image Manager system for all image operations

## [3.34.29] - 2024-07-26

### Fixed
- Fixed vehicle image not displaying in the vehicle details table on the left side of the image editor page
- Updated image_editor/vehicle.php to properly display the vehicle's primary image from the images array
- Improved image display logic to first check for primary image in the images array, then fall back to vehicle.primary_image field
- Enhanced mobile responsiveness of the vehicle details card in the image editor

## [3.34.28] - 2024-07-25

### Fixed
- Fixed "Delete All" button functionality in the image editor for vehicles and shows
- Fixed error "Model file not found: ImageModel.php" when trying to delete all images
- Updated ImageEditorController to use the existing ImageEditorModel instead of trying to load a non-existent ImageModel
- Added missing verifyCsrfToken method to ImageEditorController with correct access level (protected)
- Fixed fatal error related to access level of verifyCsrfToken method in ImageEditorController
- Updated verifyCsrfToken method signature to match parent class with $source parameter
- Corrected file paths in deleteAll method to use /uploads instead of /public/uploads
- Fixed file name references in deleteAll method to use file_name instead of filename

## [3.34.27] - 2024-07-24

### Added
- Implemented permission-based image browsing functionality
- Added entity-specific image browsing for shows and vehicles
- Added direct links from show and vehicle pages to their specific image collections
- Created database update script to ensure proper table structure
- Added optimizeImage method to ImageEditorModel for batch image optimization

### Changed
- Modified ImageEditorController to filter images based on user permissions
- Updated ImageEditorModel to support entity-specific image queries
- Enhanced browse view to include entity information in links and pagination
- Updated show and vehicle views with links to entity-specific image browsers

### Fixed
- Fixed "Optimize All" button functionality in vehicle and show views
- Fixed batch image optimization to use the correct model and file paths
- Enhanced batch optimization to respect user permissions, only optimizing images the user has access to
- Improved permission checks for show coordinators when optimizing show images
- Fixed syntax error in ImageEditorModel.php (removed extra closing brace)
- Resolved method redeclaration error by using the existing optimizeImage method
- Updated existing vehicle method in ImageEditorController with improved permission checks
- Added detailed debug logging to help diagnose image optimization issues
- Fixed parameter handling in optimize method to correctly process batch optimization URLs
- Added flash message display to vehicle and show views to show optimization success message
- Fixed image counting issue in batch optimization to only include entity-specific images

### Enhanced
- Added loading state to "Optimize All" button with spinner icon and disabled state
- Added toast notification during image optimization process
- Improved user feedback during batch optimization operations
- Disabled "Optimize All" and "Delete All" buttons when no images are available
- Added tooltips to disabled buttons explaining why they're unavailable
- Fixed "Return to previous page after upload" functionality to properly redirect users
- Added enhanced visual feedback during image upload with progress bar and status messages
- Improved HTTP referer tracking for better navigation after upload operations

## [3.34.26] - 2024-07-23

### Changed
- Removed the Show Management table from the edit show template page (edit_with_template.php) to improve UI clarity and mobile responsiveness

## [3.34.25] - 2024-07-22

### Fixed
- Fixed "Too few arguments" error in AdminController::fanVotes() by making the showId parameter optional
- Improved error handling when accessing the fanVotes page without a show ID

## [3.34.24] - 2024-07-21

### Changed
- Ensured background execution works for all controllers
- Verified that the CoordinatorController's editShow method uses the same background execution
- Improved documentation for universal background execution

### Fixed
- Fixed potential issues with background execution in different interfaces
- Ensured consistent behavior across admin and coordinator interfaces
- Verified that scoring scripts run in the background regardless of how a show is marked as completed

## [3.34.23] - 2024-07-21

### Changed
- Completely rewrote the script execution method to use true background execution
- Implemented shell command execution (exec, shell_exec, system, passthru) for background processing
- Scripts now run independently of the web UI with no output displayed to users
- Improved task status handling to ensure tasks are properly reset after execution

### Fixed
- Fixed critical issue with script output being displayed in the web UI
- Fixed issue with task status getting stuck on 'running'
- Implemented proper background execution that doesn't affect the web UI
- Added multiple shell execution methods for maximum compatibility

## [3.34.22] - 2024-07-21

### Fixed
- Added output buffering to capture script output instead of displaying it
- Fixed APPROOT constant redefinition warning in run_all_scoring.php
- Improved user experience by running scoring silently in the background
- Enhanced logging by capturing script output in the error log
- Fixed syntax error in ShowModel.php

### Changed
- Modified ShowModel.executeCompletionScripts to use output buffering
- Updated run_all_scoring.php to check if APPROOT is already defined
- Updated version number to 3.34.22

## [3.34.21] - 2024-07-21

### Changed
- Modified ShowModel.executeCompletionScripts to directly run the scoring script
- Updated ShowModel.updateShow to always run completion scripts when status is 'completed'
- Bypassed the scheduled task system for maximum reliability
- Ensured scoring runs from all places where show status can be changed

### Fixed
- Fixed critical issue with scoring not running when a show is marked as completed
- Implemented direct execution of scoring scripts that bypasses the task system
- Added comprehensive error handling and logging for script execution
- Ensured scoring runs even if the status was already 'completed'

## [3.34.20] - 2024-07-21

### Added
- Added timeout handling for scheduled tasks
- Added auto_reset_tasks.php script to automatically reset stuck tasks
- Added timeout_seconds column to scheduled_tasks table
- Added execution time tracking for tasks
- Added process identification for running tasks

### Changed
- Modified run_scheduled_task.php to handle timeouts properly
- Updated SQL scripts to add timeout_seconds column
- Updated version number to 3.34.20 in config.php and README.md
- Updated README.md with information about automatic task recovery

### Fixed
- Fixed issue with tasks getting stuck in 'running' status
- Implemented permanent solution for stuck tasks
- Added automatic reset of tasks that have been running too long
- Added detailed logging for task execution time

## [3.34.19] - 2024-07-21

### Changed
- Modified ShowModel.executeCompletionScripts to use direct script inclusion
- Removed multiple execution methods in favor of direct inclusion
- Implemented verified working method based on test_cli.php results
- Enhanced task status management with proper updates after execution
- Updated version number to 3.34.19 in config.php and README.md
- Updated README.md with information about direct script execution

### Fixed
- Fixed issue with scheduled tasks not executing when a show's status is changed to "completed"
- Implemented direct script inclusion which was proven to work in testing
- Added proper error handling for script execution
- Added detailed logging for script execution

## [3.34.18] - 2024-07-21

### Added
- Added check_permissions.php script to check file and directory permissions
- Added check_tasks_detail.php script for detailed analysis of scheduled tasks
- Enhanced direct_run_task.php script for better task execution
- Added comprehensive logging for all diagnostic scripts

### Changed
- Removed date correction code as dates are correct for the server's time zone
- Updated version number to 3.34.18 in config.php and README.md
- Updated README.md with information about the enhanced diagnostic tools
- Improved error handling and reporting in all scripts

## [3.34.17] - 2024-07-21

### Added
- Added test_cli.php script to diagnose PHP CLI functionality
- Added direct_run_task.php script for direct execution of scheduled tasks
- Added fix_task_dates.php script to correct future dates in scheduled tasks
- Added detailed logging for better troubleshooting

### Fixed
- Fixed issue with future dates in scheduled tasks
- Added automatic reset of tasks stuck in 'running' or 'error' state
- Enhanced error handling and reporting

### Changed
- Updated version number to 3.34.17 in config.php and README.md
- Updated README.md with information about the new diagnostic tools
- Improved logging format for better readability

## [3.34.16] - 2024-07-21

### Fixed
- Optimized scheduled tasks execution specifically for LightSpeed webserver
- Implemented multiple Linux-specific methods to ensure tasks run
- Added nohup for reliable background execution on Linux
- Implemented multiple fallback execution methods
- Added direct execution option for critical tasks

### Changed
- Removed Windows-specific code and replaced with Linux-optimized solutions
- Updated EventTrigger.php to use Linux-specific command execution methods
- Modified ShowModel.executeCompletionScripts to use multiple Linux execution methods
- Updated version number to 3.34.16 in config.php and README.md
- Updated README.md with information about LightSpeed webserver optimizations

## [3.34.15] - 2024-07-21

### Fixed
- Fixed scheduled tasks not executing on Windows systems
- Implemented multiple methods to ensure tasks run on Windows
- Enhanced Windows command execution with popen
- Added direct script inclusion as a fallback method
- Modified run_scheduled_task.php to accept task IDs via global variables

### Changed
- Updated EventTrigger.php to use more reliable methods for Windows
- Modified ShowModel.executeCompletionScripts to use direct script inclusion
- Updated version number to 3.34.15 in config.php and README.md
- Updated README.md with information about Windows compatibility improvements

## [3.34.14] - 2024-07-21

### Changed
- Removed direct script fallback from executeCompletionScripts method
- Now only executes tasks that are properly configured in the scheduled_tasks table
- Updated version number to 3.34.14 in config.php and README.md
- Updated README.md with information about the refined scheduled tasks execution

## [3.34.13] - 2024-07-21

### Fixed
- Fixed issue with scheduled tasks not being executed when a show's status is changed to "completed"
- Added direct script execution for show completion tasks
- Improved detection of show status changes with detailed logging
- Enhanced task status update verification in run_scheduled_task.php
- Implemented multiple fallback mechanisms for task execution

### Added
- Added executeCompletionScripts method to ShowModel for direct script execution
- Added detailed logging for task execution and status updates
- Added verification of task status updates after execution
- Added fallback to direct script execution when event triggering fails

### Changed
- Updated version number to 3.34.13 in config.php and README.md
- Updated README.md with information about the scheduled tasks fixes
- Modified ShowModel to prioritize direct script execution over event triggering
- Enhanced run_scheduled_task.php to provide better feedback on task execution

## [3.34.12] - 2024-07-21

### Fixed
- Fixed issue where the page remains locked/greyed out after closing the edit task modal
- Added comprehensive cleanup for modal artifacts when dialogs are closed
- Enhanced cancel button handling to ensure proper modal dismissal
- Added global escape key handler to properly close modals and clean up artifacts
- Fixed form submission handling to ensure modals are properly closed after submission

### Added
- Added cleanupModalArtifacts function to handle modal cleanup in a centralized way
- Added explicit event handlers for all modal cancel buttons
- Added detailed console logging for modal events to aid in debugging
- Added timeout to ensure form submission completes before closing modals

### Changed
- Updated version number to 3.34.12 in config.php and README.md
- Updated README.md with information about the modal dialog fixes
- Improved code organization in scheduled_tasks.php for better maintainability

## [3.34.11] - 2024-07-21

### Fixed
- Enhanced EventTrigger to better handle Windows environments
- Added platform-specific command execution for background tasks
- Improved logging in EventTrigger class

### Changed
- Updated version number to 3.34.11 in config.php

## [3.34.10] - 2024-07-21

### Fixed
- Fixed case sensitivity issues with event names in scheduled tasks
- Modified EventTrigger to use case-insensitive SQL queries for event name matching
- Enhanced ShowModel to handle case-insensitive status comparisons
- Added support for multiple event name formats ("show_completed" and "Show Completed")
- Fixed issue where tasks with capitalized event names weren't being triggered

### Added
- Added detailed logging throughout the event triggering process
- Created SQL commands to update existing tasks to work with the new system
- Added fallback event triggering with multiple name formats for maximum compatibility
- Added automatic EventTrigger initialization if not available

### Changed
- Updated version number to 3.34.10 in config.php and README.md
- Updated README.md with information about case-insensitive event handling
- Modified SQL update script to handle both lowercase and capitalized event names

## [3.34.9] - 2024-07-21

### Fixed
- Fixed issue where changing a show's status to "Completed" in the admin interface didn't trigger scheduled tasks
- Modified ShowModel.updateShow() to check for status changes and trigger appropriate events
- Added proper event triggering when a show is marked as completed through the edit form
- Enhanced error handling and logging for event triggering

### Added
- Created SQL update script (sql/update_v3.34.9.sql) to ensure all required tables exist
- Added detailed logging for event triggering to help diagnose issues
- Added status change detection in the updateShow method

### Changed
- Updated version number to 3.34.9 in config.php and README.md
- Updated README.md with information about the event triggering fix
- Improved code organization in ShowModel for better maintainability

## [3.34.8] - 2024-07-20

### Fixed
- Fixed issue with the "Save Changes" button not working when editing a scheduled task
- Added explicit form submission handling for the edit task form
- Enhanced JavaScript error handling and debugging for task operations
- Added detailed console logging to help diagnose AJAX issues
- Improved database initialization in the AdminController.getTask() method
- Added proper error handling for database operations in the scheduled tasks system

### Added
- Created SQL update script (sql/update_v3.34.8.sql) to ensure scheduled_tasks table is properly set up
- Added additional event listener for the Save Changes button to ensure form submission
- Added form ID to the edit task form for easier JavaScript targeting

### Changed
- Updated version number to 3.34.8 in config.php and README.md
- Updated README.md with information about the scheduled tasks system fix
- Enhanced error logging throughout the scheduled tasks system

## [3.34.7] - 2024-07-19

### Fixed
- Fixed issue with the "Save Changes" button not working when editing a scheduled task
- Fixed database initialization in the AdminController.getTask() method
- Updated form action URLs in scheduled_tasks.php to ensure proper form submission
- Fixed AJAX URL in scheduled tasks edit functionality to use BASE_URL instead of URLROOT

### Added
- Created SQL update script (sql/update_v3.34.7.sql) to ensure scheduled_tasks table is properly set up
- Added proper error handling for database operations in the scheduled tasks system

### Changed
- Updated version number to 3.34.7 in config.php and README.md
- Updated README.md with information about the scheduled tasks system fix

## [3.34.6] - 2024-07-18

### Added
- Enhanced scheduled tasks system with dynamic parameter support:
  - Added ability to pass event parameters to scheduled tasks using {event_parameter} placeholder
  - Added current_parameters column to scheduled_tasks table to store runtime parameters
  - Updated EventTrigger class to handle parameter substitution
  - Added SQL update script (sql/update_scheduled_tasks_v2.sql) for database updates

### Fixed
- Fixed PHP 8.4 deprecation warning for dynamic properties:
  - Added proper declaration for $eventTrigger property in ShowModel class
  - Updated documentation to reflect changes

### Changed
- Improved scheduled tasks UI with better parameter documentation
- Updated default scheduled tasks to use parameter placeholders
- Updated README and documentation to explain dynamic parameter usage

## [3.34.9] - 2024-07-17

### Added
- Updated existing scripts to use VehicleScoringModel for centralized scoring operations:
  - generate_judge_scores.php (v2.0) - Now uses VehicleScoringModel.generateJudgeScores()
  - calculate_show_winners.php (v2.0) - Now uses VehicleScoringModel.calculateCategoryWinners()
- Updated run_all_scoring.php (v2.0) to execute the updated scripts in sequence
- Added SQL update script (sql/update_3.34.9.sql) for database table compatibility
- Added internal cron system for scheduled tasks:
  - New admin interface for managing scheduled tasks
  - Support for event-triggered, time-based, and interval-based tasks
  - Integration with show status changes to trigger scoring automatically
  - Added SQL update script (sql/update_scheduled_tasks.sql) for scheduled tasks tables

### Changed
- Simplified scoring script architecture by centralizing logic in VehicleScoringModel
- Improved maintainability by reducing code duplication across scripts
- Enhanced error handling and logging in scoring operations
- Implemented a more integrated approach to score calculation and storage

## [3.34.8] - 2024-07-16

### Fixed
- Fixed issue where vehicles could receive a perfect score of 100 without having perfect scores on all metrics
- Modified normalization logic to only allow scores of 100 for vehicles with perfect scores on all judging metrics
- Updated both VehicleScoringModel.php methods (calculateVehicleScore and getDetailedVehicleScore) for consistency
- Added detailed logging to help diagnose scoring issues
- Enhanced test_scoring_consistency.php script to provide more detailed information about scores
- Fixed inconsistent scores across different pages by using pre-calculated scores from the database
- Modified getVehicleTotalScore() to retrieve scores from the database instead of calculating them on-the-fly
- Ensured consistent scoring across all pages that display vehicle scores

### Changed
- Updated scoring logic to cap scores at 99.99 for vehicles with excellent but not perfect scores
- Improved the fairness of the scoring system by ensuring only truly perfect entries can achieve a score of 100
- Enhanced the test_scoring_consistency.php script with a new examineVehicleScores function for detailed analysis
- Implemented a "single source of truth" approach for vehicle scores by using the pre-calculated scores in the database

## [3.34.7] - 2024-07-15

### Fixed
- Fixed inconsistent scoring between VehicleScoringModel.php and generate_vehicle_scores.php
- Modified generate_vehicle_scores.php to directly use VehicleScoringModel for score calculations
- Ensured consistent scoring results across all parts of the application
- Maintained detailed database updates in generate_vehicle_scores.php while using the model's scoring algorithm
- Updated test_scoring_consistency.php script to properly test both implementations
- Fixed database table references in test_scoring_consistency.php to work with both scores and judge_metric_scores tables
- Fixed "Undefined property: stdClass::$metric_id" error in test_scoring_consistency.php
- Fixed "Unknown column 'weight' in 'field list'" error in age_weights table query
- Fixed "Invalid parameter number" error in age_weights queries
- Updated age weight calculation to correctly use the multiplier column from age_weights table
- Added multiple fallback queries for age weight calculation to ensure compatibility
- Added robust error handling for missing properties in test_scoring_consistency.php
- Added detailed debugging output to help diagnose scoring inconsistencies

### Changed
- Updated generate_vehicle_scores.php to version 1.3
- Updated test_scoring_consistency.php to version 1.1
- Improved code organization to reduce duplication and maintenance burden
- Enhanced documentation to clarify the relationship between scoring components
- Improved test output with detailed diagnostics and troubleshooting information
- Added debugObject function to test_scoring_consistency.php for better diagnostics

## [3.34.6] - 2024-07-15

### Fixed
- Fixed database table references in test_scoring_consistency.php and cron_check_scoring_consistency.php
- Updated scripts to use judge_metric_scores and judge_total_scores tables instead of scores table
- Fixed deprecated PHP reset() function call on objects in test_scoring_consistency.php
- Improved compatibility with PHP 8.4.6 by updating code to use modern array handling

## [3.34.5] - 2024-07-10

### Fixed
- Fixed score normalization logic to properly respect the normalize_scores setting
- Fixed issue where scores could exceed 100 points when normalize_scores was enabled
- Ensured consistent scoring behavior between web application and scripts

### Added
- Added comprehensive documentation for the scoring system in docs/SCORING_SYSTEM.md
- Added test_scoring_consistency.php script to verify consistent scoring between implementations
- Added cron_check_scoring_consistency.php script for automated consistency checking

### Changed
- Updated VehicleScoringModel.php with improved normalization logic
- Enhanced code structure for easier maintenance and future updates
- Updated admin interface description for normalize_scores setting

## [3.34.4] - 2023-08-24

### Added
- Added protection to prevent editing scores for shows with "completed" status
- Added server-side validation in the AdminScoringController to block score editing for completed shows
- Added conditional display of "Edit Score" button based on show status

## [3.34.3] - 2023-08-23

### Fixed
- Fixed fatal error: "Call to undefined method VehicleScoringModel::recalculateVehicleScore()"
- Updated AdminScoringController to use the correct method calculateVehicleScore() instead
- Fixed parameter order for the calculateVehicleScore() method call

## [3.34.2] - 2023-08-22

### Fixed
- Fixed issue with judge score editing where existing scores and comments were not loading
- Updated queries to use 'raw_score' column instead of 'score' in judge_metric_scores table
- Improved compatibility with the existing database structure for judge scores

## [3.34.1] - 2023-08-21

### Fixed
- Fixed SQL error "Unknown column 'u.first_name' in 'field list'" in AdminScoringController
- Updated queries to use 'u.name' instead of 'CONCAT(u.first_name, ' ', u.last_name)'
- Fixed reference to 'categories' table, changed to 'show_categories' in category winners query
- Improved database compatibility with existing table structures

## [3.34.0] - 2023-08-20

### Added
- Added ability to edit judge scores from the admin interface
- Created new editJudgeScore method in AdminScoringController
- Added edit_judge_score.php view for editing individual judge scores
- Added "Edit Score" button to each judge score in the manage_scores.php view
- Implemented automatic recalculation of vehicle total scores after editing judge scores
- Added client-side validation for score inputs to ensure they stay within valid ranges
- Updated README.md with information about the new judge score editing feature

### Changed
- Updated version number to 3.34.0 in README.md
- Updated CHANGELOG.md with details of the new feature

## [3.33.3] - 2023-08-17

### Improved
- Added clear warning and confirmation dialog for destructive score clearing actions
- Enhanced the scoring settings UI with grouped options and visual indicators for dangerous operations
- Changed button color and text for destructive actions to improve user awareness
- Added detailed warning messages explaining the consequences of clearing scores

## [3.33.2] - 2023-08-16

### Fixed
- Fixed scoring calculation script execution issues with improved command-line argument handling
- Added detailed error logging for script execution to help diagnose issues
- Updated scripts to properly handle both web and command-line execution modes
- Improved shell command escaping for better security and reliability

### Added
- Added navigation links to improve user experience in admin scoring pages
- Added "Back to Previous Page" button on scoring settings and manage scores pages
- Added cross-navigation between Scoring Settings and Manage Scores pages

## [3.33.1] - 2023-08-15

### Fixed
- Fixed deprecated FILTER_SANITIZE_STRING usage in AdminController and AdminScoringController
- Fixed layout issues in Category Winners tab on the Manage Scores page
- Fixed "Unknown column 'vehicle_id' in 'field list'" error in scores table queries

## [3.33.0] - 2023-08-12

### Fixed
- Fixed normalization logic in generate_judge_scores.php to ensure scores are properly scaled to 1-100
- Fixed normalization logic in generate_vehicle_scores.php to ensure scores are properly scaled to 1-100
- Fixed category winner calculation in calculate_show_winners.php to properly update when scores change
- Improved the order of operations in score normalization to apply age weights before normalization

### Added
- Added admin/scoring_settings.php page for managing scoring settings for shows
- Added admin/manage_scores.php page for viewing and managing calculated scores
- Added scripts/run_all_scoring.php master script to run all scoring calculations in the correct order
- Added admin sidebar navigation with dedicated section for judging and scoring
- Added ability to clear specific score types (judge scores, vehicle scores, category winners)
- Added comprehensive score summary view with statistics on judging completion
- Added detailed views for judge scores, vehicle scores, and category winners
- Added DataTables integration for better sorting and filtering of score data

### Changed
- Modified calculate_show_winners.php to delete existing winners before inserting new ones
- Updated README.md and CHANGELOG.md with information about the new scoring system
- Enhanced the scoring process to ensure proper sequence of calculations
- Improved the display of normalized scores to clearly show the 1-100 scale

## [3.32.0] - 2023-08-05

### Fixed
- Fixed judge selection in coordinator/judges route to include administrators and coordinators
- Updated UserModel integration to use getUsersByRoles instead of getJudges for more inclusive selection
- Improved judge assignment functionality to allow admins and coordinators to serve as judges
- Updated ShowController to use Auth class for role checking instead of direct session access
- Enhanced unpublished show access control to properly respect hierarchical roles

### Added
- Created update_v3.32.0.sql script to document version update

## [3.31.0] - 2023-07-29

### Changed
- Implemented hierarchical role access system where higher roles inherit access to lower role features
- Modified Auth class to support role hierarchy in access control
- Updated hasRole method to check for role hierarchy when determining access
- Enhanced role-based access control to be more intuitive and user-friendly

### Added
- Added getRoleHierarchyLevel method to determine role position in hierarchy
- Created update_v3.29.0.sql script to document version update
- Added detailed documentation for the hierarchical role system

## [3.30.0] - 2023-07-22

### Changed
- Modified calculate_show_winners.php to use total_score from vehicle_total_scores table directly
- Updated winner determination to display raw scores instead of percentages
- Removed dependency on VehicleScoringModel for winner calculation
- Improved database queries for better performance when determining winners
- Enhanced error handling in show winners calculation

### Added
- Added ensureVehicleTotalScoresTableExists function to ensure database structure
- Created update_show_winners_calculation.sql script for database updates
- Added detailed logging for the winner calculation process

## [3.29.0] - 2023-07-15

### Changed
- Modified score display to always show scores as normalized percentages
- Updated VehicleScoringModel to always normalize scores regardless of settings
- Added percentage sign (%) to all score displays for clarity
- Added score display to category winner cards
- Updated default scoring settings to use normalized scores

### Fixed
- Fixed inconsistent score display format across different views
- Ensured all scores are displayed in the same normalized percentage format

## [3.31.0] - 2027-03-05

### Added
- Added security check in ShowController to prevent access to vehicle scores for shows that are not completed
- Implemented redirect with error message for unauthorized access attempts

### Fixed
- Fixed fatal error in ShowController by replacing undefined flash() method with setFlashMessage()

### Changed
- Modified the Results tab in user/registrations/view.php:
  - Removed embedded results content from the registration view page
  - Converted Results tab from a Bootstrap tab to a standard navigation link
  - Changed Results tab to load the dedicated /show/vehicleScore/ page with show ID and vehicle ID parameters
  - Updated link format to: /show/vehicleScore/{show_id}/{vehicle_id}
  - Removed tab-related attributes to ensure proper navigation
  - Improved user experience by providing a more detailed and focused view of judging results
  - Eliminated duplicate code by leveraging the existing vehicle score page
  - Simplified maintenance by centralizing score display logic

## [3.30.0] - 2027-03-01

### Fixed
- Fixed PHP warnings in user/registrations/view.php related to accessing array properties as objects:
  - Updated code to properly access judging_results as an array instead of an object
  - Added proper checks for array keys before accessing them
  - Fixed display of scores, category rank, and age multiplier in the results tab
  - Improved error handling to prevent "Attempt to read property on array" warnings
  - Enhanced code to handle both array and object data formats for backward compatibility

## [3.29.0] - 2027-02-25

### Fixed
- Fixed issue with judges seeing all judges' final scores in the viewScores page:
  - Modified JudgeController to calculate final score based only on the current judge's scores
  - Updated view_scores.php to clearly indicate that the displayed final score is only for the current judge
  - Added "Your Final Score" label to make it clear that the score is judge-specific
  - Improved score calculation to use the same formula but only with the current judge's input
  - Maintained compatibility with existing scoring formulas and age weights

## [3.28.0] - 2027-02-20

### Added
- Created new generate_judge_scores.php script for displaying scores by judge
  - Added tabbed interface to display detailed scores by each judge
  - Implemented average score calculation across all judges
  - Displays detailed metric scores for each judge
  - Mobile-responsive design for all device sizes
  - Uses scores table directly instead of vehicle_total_scores
- Enhanced score calculation to match the judge/viewScores method

## [3.27.0] - 2027-02-15

### Added
- Enhanced the vehicle score display in the ShowController with judge-specific tabs
- Added tabbed interface to display detailed scores by each judge
- Created SQL script to ensure show_scoring_settings table exists

### Fixed
- Fixed database table reference from scoring_settings to show_scoring_settings
- Updated controllers to use the correct table name for scoring settings

## [3.26.0] - 2027-02-05

### Changed
- Modified the judge/viewScores page to retrieve scores from database tables instead of calculating on page load
  - Now retrieves scores, metrics, totals, weights, weighted scores, age weighted scores, and final normalized scores from vehicle_total_scores and vehicle_metric_scores tables
  - Improved performance by eliminating redundant calculations
  - Maintained backward compatibility with fallback to calculation if stored scores are not available
  - Kept the "Detailed Scores by Judge" section unchanged

## [3.25.0] - 2027-01-30

### Fixed
- Fixed issue with information boxes disappearing after 5 seconds on the judge/viewScores page
  - Added alert-permanent class to prevent auto-dismissal of important information
  - Made the following elements permanent:
    - Formula Name and details
    - Understanding Weighted Scores explanation
    - Age Weight Applied information
    - No scores warnings
  - Ensures judges can always see all relevant scoring information while reviewing scores

## [3.24.0] - 2027-01-29

### Fixed
- Fixed issue with judge/show page incorrectly marking vehicles as completed when only draft scores exist
  - Modified RegistrationModel to check for non-draft scores (is_draft = 0) when determining judging completion status
  - This ensures judges can only see "Completed" status for vehicles they have fully submitted scores for
- Fixed "Undefined property: stdClass::$filename" warnings in judge/judge.php
  - Added proper property checks and fallbacks for image filenames
  - Implemented consistent handling of different image property naming conventions
  - Updated fallback image path to use default.png instead of default.jpg

## [3.23.0] - 2027-01-22

### Fixed
- Fixed fatal error in UserController by implementing missing methods in JudgingModel:
  - Added getJudgingResultsByRegistration() method to retrieve judging results for a registration
  - Added getAwardsByRegistration() method to retrieve awards for a registration
  - Added proper error handling for both methods with fallbacks for missing tables

## [3.22.0] - 2027-01-15

### Fixed
- Fixed function name conflict in generate_vehicle_scores.php by renaming logMessage to generateScoreLogMessage
- Improved duplicate prevention in vehicle_metric_scores table with additional checks
- Enhanced error handling for metric score insertion

## [3.21.0] - 2027-01-12

### Added
- Added new script for automated vehicle score generation:
  - Created generate_vehicle_scores.php script to calculate scores for all vehicles in a show
  - Added support for processing all registered vehicles in a single operation
  - Implemented detailed reporting of processed vehicles and their scores
  - Added automatic execution of calculate_show_winners.php after score generation
- Enhanced vehicle_total_scores table with additional columns:
  - Added raw_score column to store unweighted scores
  - Added weighted_score column to store scores with metric weights applied
  - Added normalized_score column to store scores normalized to 100-point scale
  - Added formula_id and formula_name columns to track which formula was used
  - Added weight_multiplier column to store the weight multiplier used in calculations
  - Added display_number column to store the vehicle's display number
- Added new vehicle_metric_scores table to store detailed metric-level scores:
  - Tracks individual scores for each judging metric
  - Stores raw scores, weights, weighted scores, and age-weighted scores per metric
  - Links to the vehicle_total_scores table for easy retrieval
- Added SQL update script to modify the vehicle_total_scores table structure and create the vehicle_metric_scores table
- Added comprehensive error handling and reporting for the score generation process
- Implemented mobile-responsive design for the score generation script

### Improved
- Enhanced score calculation with more detailed tracking of intermediate values
- Improved database structure to store more comprehensive scoring data
- Added detailed logging of the score calculation process
- Enhanced error checking to ensure all vehicles are properly processed
- Improved user interface for the score generation script with detailed reporting
- Added display of vehicle display numbers in the score generation report
- Enhanced metric tracking to show how many metrics were scored for each vehicle

## [3.20.0] - 2027-01-05

### Fixed
- Fixed fan voting page not displaying properly due to multiple issues:
  - Updated VehicleModel to use the images table instead of the non-existent vehicle_images table
  - Added automatic creation of fan_votes table if it doesn't exist in the database
  - Fixed image path handling in vote.php to correctly use file_path or thumbnail_path
  - Updated ShowController to use ImageEditorModel for retrieving vehicle images
  - Changed registration number display to use display_number when available
  - Fixed duplicate path in image URLs in vote_vehicle.php
- Fixed show results page error due to missing table:
  - Created SQL script to ensure the category_winners table exists with proper structure
  - Updated JudgingModel to use the category_winners table directly
  - Added automatic table creation in ShowController
  - Fixed syntax error in ShowController results method (missing closing brace)
- Enhanced error handling for all fan voting and show results operations
- Updated JudgingModel to check for table existence before performing operations
- Added SQL scripts to create required tables and views with proper structure
- Fixed image retrieval in getShowVehiclesWithImages method to use the correct table
- Fixed PHP 8.4 deprecation warnings about dynamic property creation in UserController
- Fixed undefined property error for image_path in vote.php

### Added
- Added createFanVotesTable method to JudgingModel for automatic table creation
- Added table existence checks to all fan voting methods
- Enhanced error logging for fan voting and show results operations
- Added fallback for empty vehicle images to prevent errors in the view
- Added proper property declarations in UserController for PHP 8.4 compatibility
- Added display_number field to vehicle queries for fan voting pages
- Added direct access to category_winners table for show results
- Added try-catch blocks to handle database errors gracefully

### Improved
- Enhanced UI with prominent display number badges for better visibility
- Centered the Quick Vote section in vote_vehicle.php for better user experience
- Improved mobile responsiveness of voting pages
- Reorganized voting options layout for better visual hierarchy
- Increased vehicle image size and improved image container styling
- Added modern card design with shadow effects and rounded corners
- Enhanced button styling with modern aesthetics and better spacing
- Improved overall layout balance and visual appeal
- Added visual hierarchy to vehicle details for better readability
- Switched to full-size images instead of thumbnails for better quality
- Implemented responsive image scaling to fit container width
- Added viewport-aware layouts that adjust to browser window size
- Converted fixed containers to fluid containers for better space utilization
- Improved card grid system to be responsive across all device sizes
- Used aspect ratio for image containers to maintain proportions

## [3.19.0] - 2026-12-29

### Added
- Added vehicle owner score viewing page to allow owners to see their judging scores
- Added public results page for completed shows displaying 1st, 2nd, and 3rd place winners
- Created category_winners table to store show winners for each category
- Added cron script to automatically calculate winners when shows are marked as completed
- Added trophy display with visual indicators for 1st, 2nd, and 3rd place
- Added mobile-responsive design for all new pages

### Fixed
- Ensured is_draft column exists in scores table and is properly used
- Fixed score display to only show completed (non-draft) scores to vehicle owners

## [3.18.0] - 2026-12-22

### Fixed
- Fixed issue with age weights not affecting final scores and weighted scores
- Corrected the application of age weights in the VehicleScoringModel
- Fixed normalization calculation to properly apply age weights to normalized scores
- Fixed display of weighted scores in the judging interface
- Fixed weight calculation to use weights exactly as stored in the database without conversion
- Updated weight display to show the actual weight values from the judging_metrics table
- Ensured scoring formulas work as configured in the admin/scoring/formulas section
- Added better explanation of how weighted scores are calculated
- Improved error logging for score calculations

### Added
- Added SQL update script to ensure age_weights table has description column
- Added more detailed logging for score calculations
- Enhanced display of age weight information in the judging interface

### Changed
- Updated JudgingModel's getAgeMultiplier method for better consistency
- Improved the calculateMetricScore method with better error handling
- Enhanced the view_scores.php template to show more detailed weight information

## [3.17.0] - 2026-12-15

### Added
- Added configurable scoring system with customizable formulas:
  - New admin interface for creating and managing scoring formulas
  - Show-specific scoring settings with custom formulas
  - Support for formula variables (rawScore, metricWeight, ageWeight, etc.)
  - Math function support (Math.pow, Math.sqrt, Math.min, Math.max)
  - Score normalization option
  - Weight multipliers for fine-tuning scoring
  - Live preview of formula changes
- Added new database tables:
  - `scoring_formulas` for storing formula definitions
  - `show_scoring_settings` for show-specific scoring configuration
- Added update script for easy database migration

### Changed
- Enhanced VehicleScoringModel to use configurable formulas
- Updated vehicle score display to show formula details
- Improved score calculation with better error handling

## [3.11.0] - 2026-12-02

### Fixed
- Fixed issue with default age weights not being correctly added to shows:
  - Modified DefaultAgeWeightModel to directly use year values without conversion
  - Added compatibility check for description column in age_weights table
  - Added SQL script to add description column to age_weights table
  - Improved error handling for age weight operations

## [3.10.0] - 2026-11-25

### Added
- Added vehicle scoring system with age weight multipliers:
  - New VehicleScoringModel for calculating and storing vehicle scores
  - Automatic score calculation based on judging metrics and age weights
  - Score caching for improved performance
  - Support for retrieving all vehicle scores for a show
- Added new database table `vehicle_total_scores` to store calculated scores

## [3.9.0] - 2026-11-16

### Fixed
- Fixed PHP warnings in judge/view_scores.php related to undefined properties:
  - Added proper check for image->filename property before accessing it
  - Updated JudgingModel::getScores() to include metric_description field
- Improved error handling in vehicle image display to prevent PHP warnings
- Enhanced score display to properly handle missing metric descriptions
- Fixed image display in judge/view_scores.php:
  - Added support for multiple image property formats (filename, file_name, image_path)
  - Updated image paths to use BASE_URL instead of URLROOT for consistency
  - Added fallback handling for different image property structures

## [3.8.0] - 2026-11-09

### Fixed
- Fixed issue with judges not seeing vehicles to judge despite having assignments
- Updated judge_assignments table to allow NULL values for category_id
- Fixed incorrect table references in RegistrationModel:
  - Updated references from vehicle_images to images table
  - Fixed scores table queries to remove non-existent is_draft column
- Enhanced error logging for judge assignments to aid in troubleshooting
- Added SQL script to fix existing judge_assignments table structure
- Fixed discrepancy between schema.sql and update_judge_assignments.sql for category_id field

## [3.7.0] - 2026-11-02

### Fixed
- Fixed issue with radio type custom fields not displaying in the `/admin/editShow/` section
- Fixed issue with checkbox groups not displaying multiple options correctly
- Fixed issue with select/dropdown fields not properly displaying options from JSON strings
- Fixed issue with coordinator dropdown not displaying coordinators after field handling improvements
- Fixed syntax error in edit_with_template.php causing parse error
- Enhanced field handling in edit_with_template.php to support various option formats (arrays, strings, and JSON strings) for all field types
- Fixed debug/radio_fields.php to properly handle non-array options in radio fields
- Added detailed error logging for field processing to aid in troubleshooting
- Improved radio field display with fallback to default Yes/No options when no options are defined
- Enhanced form designer interface with comprehensive guidance for all field types
- Added detailed help text with examples and tips for each field type
- Added automatic JSON formatting for field options in the form designer
- Improved field type handling in form designer to show/hide options field based on field type
- Added automatic conversion of line-based options to JSON format for select, radio, and checkbox fields
- Added proper handling for checkbox groups in form submission
- Improved special field handling for system fields like coordinator_id and status

## [3.6.0] - 2026-10-26

### Fixed
- Enhanced CustomFieldRetriever to properly retrieve and map radio field values from the custom_field_values table
- Improved detectFieldType method in ShowModel to properly identify radio type fields
- Added field type detection from field_mappings table for more accurate field type identification
- Enhanced error logging for custom field operations to aid in troubleshooting

## [3.5.0] - 2026-10-19

### Changed
- Modernized custom fields system to use only the `custom_field_values` table for unlimited custom fields
- Completely removed legacy `custom_field_X` columns from the shows table
- Migrated any existing data from legacy columns to the `custom_field_values` table
- Updated form field synchronization to use the modern approach for all custom fields
- Simplified custom field processing in the ShowModel

### Fixed
- Fixed issue with custom fields not updating in the `/admin/editShow/` section
- Improved handling of null and empty values in custom field processing
- Enhanced error logging for custom field operations to aid in troubleshooting
- Added proper transaction handling for custom field value updates
- Ensured custom field tables are created if they don't exist before attempting to save values

## [3.4.0] - 2026-10-12

### Fixed
- Fixed issue in form designer where "add new field" popup incorrectly disabled all fields and showed system field warning
- Fixed form field modal not properly resetting disabled state when adding new fields

## [3.3.0] - 2026-10-05

### Added
- Added "Return to Previous Page" functionality after updating a show in admin/editShow
- Added success message confirmation when a show is updated
- Added comprehensive cache busting system to ensure changes are visible immediately:
  - URL parameter cache buster for page reloads
  - HTML meta tags to prevent browser caching
  - JavaScript-based cache busting for CSS and JS files
  - Session-based cache busting that only applies after updates

### Fixed
- Fixed select boxes in admin/editShow not automatically populating with the correct database values
- Fixed status dropdown always showing "Draft" instead of the actual database value "Published"
- Fixed SQL errors in FormFieldSynchronizer and CustomFieldRetriever due to duplicate parameter names in database queries
- Fixed parameter binding issues in multiple database operations
- Improved handling of boolean values in select fields, particularly for the "fan_voting_enabled" field
- Enhanced field value retrieval to ensure proper type conversion for boolean fields
- Fixed issue with form fields not loading data from the database correctly
- Improved data extraction from database objects to ensure all fields are properly populated
- Added comprehensive debugging to help identify and resolve field value issues
- Fixed "Unknown field type: hidden" error by adding support for hidden form fields
- Added support for additional field types: time, datetime, color, radio, file, and range inputs
- Enhanced string comparison for select fields to handle potential whitespace and case sensitivity issues
- Added direct debug output for administrators to help troubleshoot field value issues
- Added special case handling for "published" status to ensure correct selection

### Added
- Added support for multiple form field types in the admin/editShow template
- Implemented proper handling for file uploads with current file display
- Added interactive range slider with real-time value display

### Improved
- Enhanced admin navigation flow when editing shows
- Improved user experience by maintaining context after form submissions
- Optimized cache control to prevent stale content after updates
- Added detailed logging for troubleshooting form field values

## [3.2.9] - 2026-09-29

### Fixed
- Fixed "Browse Available Shows" button in user/payments and user/registrations pages to correctly point to /show instead of /shows

## [3.2.8] - 2026-09-28

### Fixed
- Fixed issue with duplicate thumbnails appearing in vehicle image galleries
- Improved image tracking to prevent duplicates in both user/registrations/view and show/vehicle pages
- Enhanced thumbnail display consistency across all vehicle views

## [3.2.7] - 2026-09-27

### Fixed
- Fixed "View Complete Show Details" button link in registration view to correctly point to /show/view/ instead of /shows/view/

## [3.2.6] - 2026-09-26

### Added
- Added Payment Receipt functionality for user registrations
- Created a dedicated receipt view with printable layout
- Implemented receipt() method in UserController to handle payment receipt display
- Added getPaymentByRegistration() method to PaymentModel to retrieve payment details

### Fixed
- Fixed "View Receipt" button in the payment tab of registration view that was previously not working
- Updated registrations() method in UserController to properly handle the receipt action
- Fixed routing issue that was causing the receipt page to show "not found" error

## [3.2.5] - 2026-09-25

### Added
- Added Payment History page for users to view their payment records
- Created a dedicated user/payments view with consistent navigation and styling
- Implemented payments() method in UserController to handle the payment history functionality

### Fixed
- Fixed "Payment History" link in user/registrations that was previously not working

## [3.2.4] - 2026-09-24

### Fixed
- Fixed "Show Details" link on admin/registrations page to point to show/view/{id} instead of admin/editShow/{id}

## [3.2.3] - 2026-09-23

### Changed
- Updated fan vote rankings to display vehicle display number instead of registration number

## [3.2.2] - 2026-09-22

### Fixed
- Fixed "Table 'rowaneli62_events.vehicle_images' doesn't exist" error in admin/shows view votes
- Updated JudgingModel::getFanVoteRankings to use the new images table instead of vehicle_images
- Fixed references to vehicle_images table in AdminController and CoordinatorController
- Updated getShowResults and getCategoryResults methods to use the consolidated image system

## [3.2.1] - 2026-09-21

### Fixed
- Fixed issue with user dashboard and vehicles page not showing any vehicles after image system consolidation
- Updated getUserVehicles method to use the new unified image system
- Fixed vehicle display in user/vehicles/index.php to properly handle the consolidated image system
- Ensured backward compatibility with existing image references
- Fixed "Class ImageEditorModel not found" error by adding proper model loading in VehicleModel

## [3.2.0] - 2026-09-20

### Added
- Added SQL script to safely remove deprecated image tables

### Changed
- Consolidated all image storage to use the unified `images` table
- Updated VehicleModel to use the ImageEditorModel for all image operations
- Fixed image deletion functionality to properly remove both original and thumbnail images

### Removed
- Removed deprecated `vehicle_images` and `show_images` tables
- Removed references to deprecated image tables from SQL schema files

## [3.1.0] - 2026-09-15

### Added
- Added thumbnail gallery below the main vehicle image on the vehicle detail page
- Implemented functionality to change the main image when a thumbnail is clicked
- Added support for retrieving images from both old and new image systems

### Changed
- Replaced "Back to Show" button with "Return to Previous Page" for better navigation flow
- Improved image handling to display all available vehicle images
- Enhanced thumbnail display with better visual feedback for selected images

## [3.0.0] - 2026-08-28

### Added
- Created a public vehicle view page for showcasing vehicles to all users
- Implemented responsive image gallery with lightbox functionality for vehicle photos
- Added show appearances section to display all events where a vehicle has participated
- Created a sidebar with current and upcoming shows featuring the vehicle
- Added special management controls for vehicle owners when viewing their own vehicles
- Implemented breadcrumb navigation for improved user experience
- Added mobile-first responsive design for optimal viewing on all devices
- Updated user dashboard to link vehicles directly to their public view pages
- Enhanced vehicle cards in user/vehicles page with direct links to public view
- Added "View" button to vehicle management interface for quick access to public view

### Fixed
- Fixed missing view file for the ShowController::vehicle() method
- Improved image path handling to ensure vehicle images display correctly
- Enhanced vehicle data retrieval with proper error handling
- Improved user experience by providing consistent navigation between vehicle management and public view
- Fixed deprecated dynamic property creation in ShowController by properly declaring and initializing the database property
- Fixed "Undefined property: stdClass::$registration_date" error in vehicle view
- Fixed deprecated strtotime() null parameter warnings by adding proper null checks
- Added fallback for missing date values in vehicle registrations display

## [2.99.0] - 2026-08-25

### Added
- Improved multi-select functionality with more reliable element selection and enhanced visual feedback
- Added multi-select functionality to the template designer (hold Shift or Ctrl key to select multiple elements)
- Added alignment tools for selected elements (left, center, right, top, middle, bottom)
- Added distribution tools to evenly space elements horizontally or vertically
- Added customizable spacing control for element distribution
- Added selection box functionality for selecting multiple elements by dragging
- Added visual indicators for multi-selected elements

### Fixed
- Fixed vertical distribution spacing to ensure all elements are properly spaced with consistent gaps
- Improved element positioning in distribution tools for more reliable results

## [2.98.0] - 2026-08-22

### Fixed
- Fixed text alignment (left, center, right) functionality in the printable template editor
- Improved alignment handling to properly update both text-align and justify-content properties
- Enhanced text element positioning to respect the selected alignment option
- Fixed multiple instances where text alignment was being forced to center
- Ensured text alignment is preserved during element resizing and dragging
- Corrected default text alignment for new text elements
- Fixed template loading to properly respect saved text alignment settings
- Added !important flags to text alignment styles to prevent overrides
- Added data attributes to help CSS selectors target elements with specific alignments
- Enhanced debugging with console logs to track text alignment changes
- Added explicit CSS rules to enforce text alignment based on style attributes
- Fixed conflicting alignment settings in template ID 19
- Added backward compatibility for class-based and data-attribute-based alignment
- Created comprehensive SQL update script to fix alignment issues in all templates
- Added multiple SQL queries to handle various alignment conflict scenarios
- Ensured consistency between class names, data attributes, and inline styles

## [2.97.9] - 2026-08-20

### Added
- Added Display Number Badge element to the printable template editor
- Implemented customizable badge color and text color properties for the display number badge
- Added support for the {{DN}} placeholder in printable templates
- Enhanced PrintableTemplateModel to properly render display numbers in templates
- Added CSS styling for display number badges in printed templates
- Added database checks for registration_number and display_number columns in registrations table
- Created indexes on registration_number and display_number columns for improved performance
- Added automatic population of registration_number field if it's missing

### Changed
- Updated template designer to support resizable display number badges
- Improved mobile responsiveness of display number badges in printed templates
- Enhanced RegistrationController to correctly use registration_number from the database
- Fixed {{registration_number}} placeholder to use the actual registration_number field instead of ID

## [2.97.8] - 2026-08-18

### Added
- Added category dropdown filter to the judge/show page for filtering vehicles by category
- Implemented display of category prefixes alongside category names in the filter dropdown
- Added real-time JavaScript filtering of vehicles without page reload
- Created filter status indicator showing count of filtered vehicles and current selection
- Added data attributes to vehicle rows for efficient category-based filtering

### Changed
- Enhanced category display in vehicle listings to show category prefix badges
- Improved mobile card layout to better display category information
- Updated desktop table view to include category prefix badges for consistency

## [2.97.7] - 2026-08-15

### Added
- Implemented card-based layout for vehicle listings on mobile devices in judge/show page
- Added responsive padding and spacing for better mobile viewing experience
- Enhanced CSS media queries for mobile-first responsive design

### Fixed
- Eliminated horizontal scrolling on the judge/show page for mobile devices
- Fixed table layout issues that caused content to overflow on small screens
- Improved breadcrumb display for long show names on mobile devices

### Changed
- Redesigned vehicle listing table to use cards on mobile and tables on desktop
- Adjusted button sizes and spacing for better touch interaction on mobile
- Enhanced typography scaling for better readability on small screens

## [2.97.6] - 2026-08-11

### Fixed
- Improved vehicle image display in judge view by showing primary vehicle image instead of generic car icon
- Enhanced fallback mechanism to retrieve primary image from vehicles table when no image gallery is available
- Added robust image path handling to ensure images are loaded from the correct location
- Implemented multiple fallback strategies to find the primary vehicle image
- Added direct database query fallback when model methods don't return images
- Improved mobile responsiveness of vehicle image display in judging interface

### Changed
- Replaced Year card with Display Number card in the judge view for better vehicle identification
- Enhanced Display Number visibility with prominent styling and visual indicators
- Moved Display Number to first position in vehicle specs for maximum visibility
- Added distinctive blue badge styling for Display Numbers across all views
- Enhanced Display Number card with subtle background color and left border accent
- Added interactive hover effects to make Display Numbers more engaging
- Added fallback display for vehicles without assigned display numbers
- Improved visual hierarchy to make Display Numbers easy to spot at a glance

## [2.97.5] - 2026-08-10

### Fixed
- Fixed progress bar not updating when loading a judging form with autosaved data
- Added proper tracking of autosaved scores and comments for progress calculation
- Enhanced form initialization to recognize pre-filled data from autosave
- Improved user experience when returning to a partially completed judging form

## [2.97.4] - 2026-08-09

### Fixed
- Fixed "Unknown column 'is_active' in 'where clause'" error in fix_duplicate_judges.sql
- Created a simplified SQL script (simple_fix_judges.sql) that avoids all potential issues
- Enhanced SQL scripts to check for column existence before using them
- Improved handling of temporary tables in database scripts
- Added more robust error handling for database operations
- Fixed issues with SQL scripts that might reference non-existent columns

## [2.97.3] - 2026-08-08

### Fixed
- Fixed "Duplicate entry for key 'judges.unique_judge'" error when saving scores
- Added checks for existing judge entries before attempting to create new ones
- Implemented logic to update judge IDs to match user IDs
- Created a new standalone SQL script (fix_duplicate_judges.sql) to fix duplicate judge entries
- Enhanced judge creation process to handle unique constraint violations
- Added fallback methods for judge creation when primary method fails
- Improved error handling for database constraint violations

## [2.97.2] - 2026-08-07

### Fixed
- Fixed persistent "Unknown column 'is_active' in 'where clause'" error with more robust column handling
- Added comprehensive checks before using columns that might not exist
- Created more resilient database update scripts that work with any database state
- Improved judge creation process to work even when is_active column doesn't exist yet
- Enhanced error handling for all database operations
- Added a new standalone SQL script (fix_judges_table_v2.sql) with step-by-step fixes
- Fixed issues with subqueries that might reference non-existent columns

## [2.97.1] - 2026-08-06

### Fixed
- Fixed foreign key constraint error when saving scores with judge IDs not in the judges table
- Improved column existence checking to prevent "Column already exists" errors
- Enhanced database update scripts to safely handle existing columns
- Fixed auto-save functionality to properly handle judge assignments
- Fixed "Unknown column 'is_active' in 'where clause'" error by ensuring the column exists
- Added proper checks for judges table structure before performing operations
- Fixed SQL syntax errors in database update scripts (removed IF/THEN blocks)
- Added standalone SQL script to fix judges table issues directly
- Improved error handling for database column operations

## [2.97.0] - 2026-08-05

### Added
- Implemented auto-save functionality for judge scoring to prevent data loss
- Added automatic restoration of scores if browser crashes during judging
- Added visual indicators for auto-save status and last save time
- Created database updates to support auto-save functionality

## [2.96.0] - 2026-07-31

### Fixed
- Fixed judging progress bar incorrectly showing 100% complete when judging hasn't started
- Changed default score from 1 to 0 to accurately reflect unjudged status
- Improved progress calculation to only count metrics that have been actively judged
- Added tracking for user modifications to distinguish between default values and user input
- Enhanced progress bar accuracy for better judging workflow visualization

## [2.95.0] - 2026-07-30

### Fixed
- Fixed "Undefined property: JudgeController::$db" error in JudgeController.php
- Improved database connection handling in JudgeController by initializing it in the constructor
- Fixed permission check for admins in vehicle check-in functionality
- Added admin role bypass for judge assignment checks in check-in and undo check-in methods
- Enhanced error handling for database operations in JudgeController

## [2.94.0] - 2026-07-28

### Fixed
- Fixed issue with Judge button not working on the judge/show page
- Added specific CSS and JavaScript fixes to ensure judge buttons are clickable
- Improved button styling for better mobile responsiveness
- Fixed "Undefined property: stdClass::$id" error in judge/show.php by using registration_id instead of id

## [2.93.0] - 2026-07-26

### Fixed
- Fixed issue with judge assignments not appearing in judge dashboard
- Updated JudgingModel to properly sync judge assignments between tables
- Ensured that admin-assigned judges are properly recorded in both judges and judge_assignments tables
- Eliminated the need to run fix_judge_assignments.php after assigning judges
- Improved vehicle assignment logic for judges to handle various assignment scenarios
- Enhanced error handling and logging in the judging system
- Added more informative messages when no vehicles are available to judge
- Fixed issues with category-specific judge assignments
- Fixed SQL parameter binding error in getJudgingAssignments method
- Resolved "Invalid parameter number" error when retrieving vehicles to judge

## [2.92.0] - 2026-07-25

### Fixed
- Fixed SQL syntax errors in JudgingModel when checking if tables exist
- Fixed undefined variable warning in header.php
- Improved error handling in database queries
- Enhanced DatabaseModel's tableExists method to use more reliable information_schema queries
- Fixed table existence checks in CustomFieldValuesModel, CustomFieldMapper, and CustomFieldRetriever
- Added better fallback mechanisms for database table existence checks
- Standardized database query methods across models for better compatibility
- Improved error handling for database operations with proper fallbacks

## [2.91.0] - 2026-07-24

### Fixed
- Fixed vehicle photos display in the admin judgeVehicle page
- Integrated with the built-in image viewer for consistent user experience
- Added proper gallery support for multiple vehicle images
- Fixed console errors related to image viewer initialization
- Improved compatibility with different image storage formats

## [2.90.0] - 2026-07-23

### Fixed
- Fixed QR code download functionality in user registration view by using direct file download instead of controller method
- Improved consistency of download button text between admin and user views
- Enhanced user experience with more reliable QR code downloading

## [2.89.0] - 2026-07-22

### Changed
- Updated "Print" button in the Check-in QR Code section of user registration view to "Print Registration"
- Changed the Print button link to use the same registration print page as the top button
- Fixed QR code download functionality in user registration view by correcting the URL path

## [2.88.0] - 2026-07-21

### Changed
- Renamed "Print QR Code" button to "Print Registration" in the Smart QR Code section of the registration view
- Updated the button to link directly to the registration print page for consistency with the top button

## [2.87.0] - 2026-07-20

### Fixed
- Fixed PHP warnings in printRegistration method when accessing custom field values
- Corrected array access in RegistrationController to properly handle custom field data structure
- Improved error handling for custom field values to prevent "Attempt to read property on array" warnings

## [2.86.0] - 2026-07-19

### Improved
- Enhanced registration edit form to return to the previous page after submission
- Added "Back" functionality to the Cancel button using JavaScript history
- Improved user experience by preserving navigation context

## [2.85.0] - 2026-07-18

### Fixed
- Fixed critical issue with registration status not updating in the database
- Corrected status dropdown values to match database enum values (pending, approved, rejected, cancelled)
- Corrected payment status dropdown values to match database enum values (unpaid, paid, refunded, free, waived)
- Drastically reduced excessive logging in the Database class to make error logs more readable
- Added targeted debug logging for registration updates to help troubleshoot issues

## [2.83.0] - 2026-07-16

### Changed
- Removed the ability to change Display number from admin/editRegistration page
- Display numbers are now managed by the system and cannot be manually edited

## [2.82.1] - 2026-07-15

### Fixed
- Fixed SQL error in registration updates caused by duplicate display_number parameter in the query

## [2.82.0] - 2026-07-14

### Added
- Enhanced QR code security by adding registration number as a token parameter
- Added intelligent category-based prefix system for display numbers (e.g., "CL-123" for Classics)
- Implemented smart algorithm for generating meaningful category prefixes
- Added special case handling for common categories (Classics, Muscle Cars, Trucks, etc.)
- Added robust handling for custom user-defined categories with automatic prefix generation
- Ensured all categories get meaningful 2-letter prefixes regardless of naming format
- Added display number field with category prefix selector to registration forms
- Created database update script to set display numbers for existing registrations
- Added security logging for invalid QR code token attempts

### Changed
- Modified QR code URLs to include registration number as a security token
- Enhanced QR code print templates with large, prominent display of category-based display numbers
- Added clear instructions for displaying vehicle numbers at events
- Improved display number handling in admin and coordinator interfaces
- Updated print styles to ensure display numbers are clearly visible when printed

## [2.81.2] - 2026-07-12

### Fixed
- Fixed check-in button redirecting to admin dashboard by adding view_registration method to AdminController
- Added URL-friendly alias for viewRegistration to ensure consistent routing

## [2.81.1] - 2026-07-11

### Fixed
- Fixed fatal error in check-in functionality by replacing non-existent logActivity method with error_log
- Corrected redirect URLs in AdminController to use view_registration instead of viewRegistration
- Improved error handling in check-in and undo check-in methods

## [2.81.0] - 2026-07-10

### Added
- Implemented comprehensive vehicle check-in functionality for admins, coordinators, and judges
- Added check-in and undo check-in methods to AdminController, CoordinatorController, and JudgeController
- Added check-in time tracking with timestamp recording
- Created database update script to add check_in_time column to registrations table
- Enhanced registration views with check-in status indicators and action buttons

### Fixed
- Fixed missing check-in functionality in the registration management system
- Ensured proper validation of user permissions before allowing check-in actions

## [2.80.0] - 2026-07-05

### Added
- Added downloadQrCode method to UserController for QR code downloads

### Fixed
- Fixed Check-in QR code not displaying in user registration view
- Corrected QR code image paths from /public/uploads/qrcodes/ to /uploads/qrcodes/
- Updated QR code paths in print_qr_code.php view

## [2.81.0] - 2026-07-06

### Changed
- **FORCED REGISTRATION CLOSURE**: Registration is now permanently set to "FULL" status
- All registration forms are completely blocked and hidden
- Registration buttons show "Registration Full - All Spots Taken"
- Added server-side blocks to prevent any form submissions
- Enhanced security to prevent direct form posting attempts

### Technical
- Hard-coded `$registrationsFull = true` to force closure regardless of actual count
- Added form submission blocking at server level
- Maintained all visual "Registration Full" indicators

## [2.80.0] - 2026-07-05

### Added
- Implemented registration limit of 20 entries maximum
- Added prominent "Registrations Full" notice when limit is reached
- Added registration count tracking and display
- Enhanced user feedback with clear status indicators

### Changed
- Registration form is completely hidden when limit is reached
- Hero section button changes to show "Registration Full" status
- Improved visual design of registration status messages

### Fixed
- Prevented new registrations when maximum capacity is reached
- Enhanced error handling for registration limit scenarios

## [2.79.0] - 2026-07-04

### Added
- Added "Back to Home" button on entries page for improved navigation
- Enhanced user experience with intuitive navigation controls

## [2.78.0] - 2026-07-03

### Added
- Added Facebook link to navigation menu on all pages
- Added social media integration with direct link to club's Facebook group

## [2.77.0] - 2026-07-02

### Added
- Added HTTP cache control headers to prevent page caching
- Implemented complete cache prevention for dynamic data

### Fixed
- Fixed issue with data not refreshing on page reload
- Ensured all dynamic content is always up-to-date
- Improved cache management for all pages

## [2.76.0] - 2026-07-01

### Added
- Added "Hide from Display" button to remove entries from public view
- Added automatic display confirmation when payment is confirmed
- Fixed cache busting for CSS and JavaScript files

### Changed
- Payment confirmation now automatically confirms entry for display
- Enhanced display status management with ability to hide confirmed entries
- Improved user feedback with more descriptive confirmation messages
- Updated admin interface to show hide/display options based on current status

## [2.75.0] - 2026-06-30

### Added
- Added ability to confirm entries for display without confirming payment
- Added separate display status tracking for entries
- Added "Confirm for Display" button in admin panel
- Added visual indicators for payment status in public entry list
- Added display approval count to public entry list

### Changed
- Modified entries page to only show display-approved entries
- Enhanced status indicators with different colors for payment status
- Improved admin interface with separate columns for payment and display status
- Updated stats display to show display-approved entries

## [2.74.0] - 2026-06-29

### Added
- Added entry editing functionality to the admin panel
- Added welcome messages to the front page: "Any Vehicle Type Welcome!" and "All Clubs Welcome To Join Us!"
- Enhanced delete confirmation with detailed entry information
- Added responsive edit form with mobile-first design

### Changed
- Increased visitor count display by adding 5 to the actual count
- Improved admin interface with edit buttons for each entry
- Enhanced form styling for better mobile experience

## [2.73.0] - 2026-06-28

### Added
- Added invisible unique visitor counter to the July 4th event page
- Added visitor count display in the admin dashboard
- Implemented unique visitor identification using IP address and user agent
- Created secure JSON storage for visitor data in the data directory
- Added responsive design for visitor count display in admin dashboard

### Changed
- Updated admin dashboard stats section to include visitor count
- Enhanced admin dashboard responsive layout for mobile devices
- Improved FileStorage class usage for visitor tracking

## [2.77.0] - 2026-06-27

### Fixed
- Fixed HTML entity encoding issues in field placeholders
- Removed &amp; and other HTML entities from displayed text
- Added proper HTML entity decoding for all field values
- Implemented special handling to prevent double-encoding of HTML entities
- Enhanced placeholder replacement to properly handle special characters
- Improved text rendering in templates for better readability
- Fixed issues with ampersands and other special characters in field values
- Ensured consistent text display across all template elements

## [2.76.0] - 2026-06-26

### Fixed
- Removed all border and outline styling from text and field elements in final output
- Added data attributes to template elements for better CSS targeting
- Created specific CSS classes for different element types (text-element, field-element, qrcode-element)
- Preserved QR code's distinctive red dashed border while removing borders from other elements
- Enhanced template CSS to ensure clean, border-free appearance for text and field elements
- Implemented selective border removal that preserves QR code styling
- Added !important rules to ensure border removal overrides any inline styles
- Improved overall visual appearance of printed templates

## [2.75.0] - 2026-06-25

### Fixed
- Removed dotted outline boxes from field elements in generated HTML
- Maintained field element borders in the designer for editing purposes only
- Improved the appearance of printed templates by eliminating unnecessary borders
- Enhanced the visual clarity of field elements in final output
- Preserved border styling for non-field elements like QR codes and images
- Implemented conditional border rendering based on element type
- Fixed issue where field elements had distracting borders in printed output
- Improved overall aesthetics of generated templates

## [2.74.0] - 2026-06-24

### Fixed
- Simplified QR code placeholder by removing text label for cleaner appearance
- Enhanced {{QR_CODE}} identifier with larger font size and more prominent styling
- Increased border thickness of QR code identifier for better visibility
- Added subtle shadow effect to QR code identifier for depth
- Maintained QR code visual pattern for easy recognition
- Kept distinctive red dashed border and yellow background for visibility
- Preserved data attributes for template system identification
- Improved overall aesthetics of QR code elements
- Optimized QR code placeholder for better template rendering

## [2.73.0] - 2026-06-23

### Fixed
- Improved QR code placeholder visibility in template designer
- Added distinctive styling for QR code elements with red dashed border
- Added yellow background to QR code placeholders for better visibility
- Implemented clear {{QR_CODE}} identifier in QR code elements
- Added QR code visual representation in the placeholder
- Added data attributes to QR code elements for easier identification
- Enhanced QR code elements with a label above the element
- Made QR code elements more distinct from other template elements
- Fixed issue where QR code placeholders were difficult to locate
- Improved template system's ability to identify and replace QR code placeholders
- Fixed case-sensitivity issues with QR code placeholders
- Added special handling for {{QR_CODE}} uppercase placeholder
- Enhanced QR code replacement to work with multiple placeholder formats

## [2.72.0] - 2026-06-22

### Fixed
- Fixed inconsistency between QR codes in admin view and test view
- Fixed QR code URL to consistently point to /qr/scan/registration/{id}
- Removed on-the-fly QR code generation to ensure consistency
- Updated QR test page to use stored QR codes when available
- Fixed registration printing issues where data wasn't displaying properly
- Fixed QR code display in printable registration view
- Resolved template rendering issues with registration data
- Fixed template system to properly use default templates from database
- Fixed placeholder replacement in printable templates
- Enhanced QR code handling in templates to use stored QR code files
- Fixed QR code display in registration printouts with direct file links
- Simplified QR code handling to use direct file paths instead of controller methods
- Added direct download link for QR codes in the fallback view
- Fixed undefined variable warnings in QR code display
- Added fallback image for missing QR codes
- Fixed duplicate variable definition causing PHP warnings

### Changed
- Standardized QR code generation to always use stored QR codes
- Improved QR code management interface to prevent confusion
- Updated QR code display to only show generated codes
- Enhanced template system to properly use templates from the database
- Improved fallback to dedicated view when template rendering fails
- Enhanced registration print view with mobile-first responsive design

## [2.71.0] - 2026-06-15

### Fixed
- Fixed QR code storage path to use /uploads/qrcodes/ instead of /public/uploads/qrcodes/
- Fixed QR code persistence by storing filenames in the database
- Fixed potential storage issues by implementing automatic QR code cleanup

### Added
- Added system to automatically clean up QR codes 1 month after show end date
- Added clear indicators for permanent vs. temporary QR codes
- Added improved QR code management in the admin interface

### Changed
- Improved QR code display with better visual indicators
- Enhanced QR code generation process for better reliability

## [2.71.0] - 2026-06-15

### Changed
- Changed registration number format from "CS-#####" to "RER-##########" (10 digits)
- Improved registration number generation to ensure sequential numbering

### Fixed
- Fixed issue with registration numbers sometimes appearing in scientific notation (e.g., "CS-1.844674407371E+1")
- Enhanced error handling in registration number generation

## [2.70.0] - 2026-05-31

### Added
- Added display number field to registration forms and database
- Added display_number column to registrations table
- Added ability for admins to assign and edit display numbers for vehicles

### Fixed
- Fixed CSRF token validation issues in forms
- Fixed form security with proper CSRF token handling
- Fixed "Failed to update registration" error when editing registrations

## [2.69.0] - 2026-01-21

### Added
- Added printable registration functionality for all users
- Implemented template-based registration printing with dynamic data
- Added print buttons to user and admin registration pages
- Created fallback template for when no custom template is available
- Added notification system to inform users about printing options
- Integrated QR codes into printable registration sheets
- Added QR code requirement for printing registration sheets
- Added automatic QR code generation option for admins
- Added clear messaging about QR code generation process
- Ensured print functionality is only available after QR code generation

### Fixed
- Fixed undefined property errors in registration printing
- Added proper null checks for all registration data fields
- Fixed deprecated strtotime() calls with null parameters
- Updated custom field handling to use available methods
- Improved error handling in the printRegistration method
- Added user-friendly message when QR code is not yet generated
- Enhanced template rendering to handle different placeholder formats
- Added proper handling of template fields like {{make}}, {{model}}, etc.
- Improved HTML escaping to properly display QR code images
- Added SQL script to ensure proper database structure
- Added comprehensive debugging to identify template rendering issues
- Fixed template data extraction from JSON format
- Added ability to build HTML from template elements
- Ensured existing templates are updated with proper HTML and CSS
- Added support for multiple field naming conventions
- Created dedicated view file for registration printing
- Implemented direct view rendering for consistent display

## [2.68.0] - 2026-01-20

### Fixed
- Fixed z-index issue with resize handles appearing on top of image selection modal
- Temporarily hide resize handles when image selection modal is open
- Ensured proper modal layering with appropriate z-index values
- Improved user experience when selecting images in template designer
- Fixed issue with modal controls becoming inactive

## [2.67.0] - 2026-01-19

### Fixed
- Unified text and field scaling to use the same algorithm for consistency
- Applied 90% of minimum ratio scaling to both text and field elements
- Removed different scaling approaches between element types
- Ensured consistent behavior across all element types
- Simplified scaling logic for better maintainability
- Improved visual consistency between text and field elements
- Enhanced user experience with predictable scaling behavior

## [2.66.0] - 2026-01-18

### Fixed
- Completely rewrote handle visibility enforcement after property changes
- Added direct recreation of resizable functionality when properties are changed
- Implemented MutationObserver to detect DOM changes and maintain handle visibility
- Added specific CSS positioning for each resize handle direction
- Enhanced CSS rules to ensure handles are always visible with !important flags
- Applied direct attribute styling to handles for maximum visibility
- Added initialization delay to ensure handles are visible on page load
- Fixed issue where handles disappear after editing element properties
- Improved handle positioning with explicit coordinates for each direction
- Added continuous monitoring of DOM changes to maintain handle visibility

## [2.65.0] - 2026-01-17

### Fixed
- Adjusted text and field scaling to be less aggressive and more balanced
- Reduced field element scaling to 90% of minimum ratio to prevent overflow
- Increased text element scaling to 150% of minimum ratio for faster growth
- Lowered maximum font size limits from 200px to 100px for better control
- Implemented different scaling algorithms for text vs field elements
- Improved text readability by ensuring it stays within container bounds
- Enhanced scaling behavior to be more predictable and visually appealing
- Fixed issue where field text would grow larger than its container

## [2.64.0] - 2026-01-16

### Fixed
- Fixed issue where resize handles disappear after applying property changes
- Added automatic handle visibility enforcement after property changes
- Ensured elements maintain proper styling after property updates
- Preserved element selection state after applying changes
- Added specific styling maintenance for field and text elements
- Improved consistency of element appearance across all operations
- Enhanced debugging with additional console logging for property changes

## [2.63.0] - 2026-01-15

### Fixed
- Fixed field elements to prevent text wrapping with white-space: nowrap
- Added overflow handling with hidden overflow and ellipsis for field elements
- Ensured field text always stays on a single line regardless of container size
- Applied consistent nowrap styling across all field element instances
- Enhanced field element resize behavior to maintain single-line text
- Added text-overflow: ellipsis to show indication when text is truncated
- Improved field element styling consistency across all operations

## [2.62.0] - 2026-01-14

### Fixed
- Fixed delete selected button functionality to properly remove selected elements
- Implemented aggressive text scaling to properly fill containers when resized
- Changed scaling algorithm to use maximum ratio instead of minimum for better text filling
- Added line-height adjustment to ensure text is vertically centered in containers
- Increased maximum font size limit from 72px to 200px for better visibility
- Added font weight enhancement for larger text elements
- Applied 20% additional scaling factor to make text more prominent
- Ensured text and field elements maintain proper alignment during resizing
- Improved vertical centering with flex display and alignment properties
- Fixed text justification and alignment for better visual appearance

## [2.61.0] - 2026-01-13

### Fixed
- Completely rewrote text and field element creation to ensure resize handles are always visible
- Implemented direct element creation with explicit jQuery UI resizable initialization
- Enhanced text and field scaling to properly fill containers when resized
- Made resize handles much more visible with bright red color and larger size
- Added aggressive handle styling with high z-index and box shadows
- Improved forceResizeHandlesVisible function to recreate resizable functionality when needed
- Implemented better text scaling based on both width and height changes
- Added comprehensive element styling enforcement for consistent appearance
- Enhanced debugging with detailed console logging for element creation and handle visibility
- Fixed font scaling to use proper ratios based on container dimensions

## [2.60.0] - 2026-01-12

### Fixed
- Fixed issue where field elements don't show resize handles until template is saved and reopened
- Added forceResizeHandlesVisible function to ensure handles are always visible
- Improved field element initialization with proper styling and attributes
- Enhanced resize handle visibility with better styling and forced display
- Added automatic handle visibility enforcement after adding new elements
- Implemented better debugging with console logging for element initialization
- Fixed element selection after adding new elements
- Ensured consistent styling for field elements across all operations

## [2.59.0] - 2026-01-11

### Fixed
- Fixed text scaling in template editor - text now scales with container size
- Fixed missing resize handles for field elements
- Improved text element display with flex centering
- Enhanced field element styling with consistent borders and padding
- Made resize handles more visible with improved styling
- Added automatic font size scaling based on element dimensions
- Ensured all elements maintain proper styling during resize operations
- Fixed CSS specificity issues with !important declarations where needed
- Improved element positioning with absolute positioning enforcement
- Enhanced template designer CSS with more robust styling

## [2.58.0] - 2026-01-10

### Fixed
- Fixed image resizing in template editor - images now resize properly with their containers
- Improved element resizing behavior for all element types
- Enhanced image handling to maintain proper dimensions and aspect ratios
- Added automatic image container sizing based on image dimensions
- Ensured consistent image styling with width/height 100% and object-fit contain
- Fixed issue where only the container box would resize but not the content
- Improved resize handle behavior for more intuitive element manipulation
- Enhanced template element styling for better visual representation

## [2.57.0] - 2026-01-09

### Fixed
- Fixed template editor not loading saved templates correctly
- Improved HTML entity handling in template editor
- Enhanced template HTML parsing to handle encoded HTML entities
- Fixed issue with jQuery UI resizable handles being saved in template HTML
- Added automatic HTML cleaning to remove UI artifacts from templates
- Implemented better error handling for template loading
- Added decoded HTML storage for more reliable template loading
- Fixed template editor to properly handle complex templates with multiple elements
- Enhanced debugging tools to identify and fix template loading issues
- Improved form submission to ensure clean HTML is saved

## [2.56.0] - 2026-01-08

### Fixed
- Fixed resize handles not appearing on newly added field elements
- Fixed template editor showing HTML code after saving
- Fixed media library image upload and selection issues
- Enhanced resize handle visibility for better usability
- Fixed template restoration when editing existing templates
- Improved template HTML parsing and saving to prevent code display
- Fixed template element positioning and styling preservation
- Added robust template serialization to ensure proper loading
- Fixed HTML string parsing for template restoration
- Added comprehensive debugging to track template loading issues
- Completely rewrote template parsing and serialization logic

## [2.55.0] - 2026-01-07

### Added
- Added media library integration for template designer
- Added font options for field elements
- Added media upload functionality

### Fixed
- Fixed media directory path issues
- Improved image selection modal
- Enhanced field element styling options

## [2.54.0] - 2026-01-06

### Added
- Added cache-busting for template designer pages
- Added refresh button to template designer interface
- Added no-cache headers to prevent browser caching

### Fixed
- Fixed issue with template designer not refreshing properly
- Improved page loading with cache control headers
- Enhanced user experience with manual refresh option

## [2.53.0] - 2026-01-05

### Fixed
- Fixed field properties showing partial HTML code in field label
- Fixed field name dropdown not affecting the canvas element
- Fixed field element content being parsed as HTML instead of text
- Improved field element creation and editing

## [2.52.0] - 2026-01-04

### Added
- Added element resizing by dragging corners/edges
- Added font selection for text elements
- Added image selection from media library
- Added aspect ratio maintenance for QR code elements
- Added text alignment options for text and field elements

### Enhanced
- Improved line element resizing
- Added font weight and text alignment controls for text elements
- Added image alt text editing
- Added maintain aspect ratio option for images

## [2.51.0] - 2026-01-03

### Added
- Added field selection dropdown to template designer
- Added field label editing capability
- Improved field element handling in the template designer

### Fixed
- Fixed issue where field elements could only use "registration_number" field
- Enhanced field element properties panel with more options
- Improved field element rendering with proper labels

## [2.50.0] - 2026-01-02

### Fixed
- Fixed jumbled JavaScript code appearing at the bottom of the template designer
- Properly separated JavaScript code into external files
- Fixed template designer showing raw JavaScript code instead of the designer interface
- Ensured proper loading of the simple-template-designer.js file
- Maintained mobile-first responsive design principles

## [2.49.0] - 2026-01-01

### Fixed
- Fixed visual template designer showing jumbled elements at the bottom of the page
- Added missing template_data field to the printable templates form
- Added template_data column to printable_templates table
- Improved error handling in template designer when template_data column doesn't exist
- Enhanced template element selection and property display
- Fixed template element rendering with proper default values
- Maintained mobile-first responsive design principles

## [2.48.0] - 2025-12-31

### Added
- Added development bypass for admin authentication in Developer Settings
- Added toggle in admin settings under development category to enable/disable the bypass

### Security
- Implemented proper warning messages for development features that could impact security

## [2.47.0] - 2025-12-30

### Fixed
- Fixed visual editor in printable templates not working properly
- Resolved JavaScript errors in simple-template-designer.js related to template string interpolation
- Fixed "The specified value cannot be parsed, or is out of range" errors in template designer
- Fixed "The specified value does not conform to the required format" errors for color inputs
- Improved form field initialization in the template designer
- Enhanced property panel to properly display and update element properties
- Ensured proper rendering of template elements with default values
- Maintained mobile-first responsive design principles

## [2.46.0] - 2025-12-27

### Changed
- Completely redesigned printable templates system to avoid using JSON
- Implemented direct HTML/CSS-based visual editor for printable templates
- Added live drag-and-drop interface for template design without JSON validation
- Fixed alignment issues when printing templates
- Improved template editor with better visual feedback
- Enhanced template designer with more intuitive controls
- Added HTML/CSS source editor for advanced users
- Maintained mobile-first responsive design principles

## [2.45.0] - 2025-12-20

### Fixed
- Fixed issue with QR code generation where clicking "Generate QR Code" button did nothing
- Improved AJAX request handling in QR code generator
- Enhanced error handling for entity loading in QR generator
- Added loading indicators for better user experience
- Maintained mobile-first responsive design principles

## [2.44.0] - 2025-12-19

### Fixed
- Fixed fatal error: "Call to undefined method ShowModel::getActiveShows()" in AdminController.php
- Modified manageQrCodes() method to use getShows() with filtering instead of getActiveShows()
- Added client-side filtering for active shows based on end date
- Improved error handling for show retrieval
- Maintained mobile-first responsive design principles

## [2.43.0] - 2025-12-18

### Fixed
- Fixed fatal error: "Cannot redeclare AdminController::downloadQrCode()" in AdminController.php
- Removed duplicate downloadQrCode method at line 6988
- Kept the more comprehensive implementation at line 4664
- Maintained mobile-first responsive design principles

## [2.42.0] - 2025-12-17

### Changed
- Removed Database Tools card from Developer Tools settings page to simplify the interface
- Maintained mobile-first responsive design principles

## [2.41.0] - 2025-12-16

### Fixed
- Fixed incorrect links in QR Code Settings page:
  - Updated "QR Code Settings" link to correctly point to /admin/qrCodeSettings
  - Updated "QR Code Analytics" link to correctly point to /admin/qrAnalytics
  - Updated "QR Code Generator" link to correctly point to /admin/qrGenerator
  - Updated "Print QR Codes" link to correctly point to /admin/qrcodes
- Maintained mobile-first responsive design principles

## [2.40.0] - 2025-12-15

### Changed
- Simplified admin settings interface by removing "General Settings", "Form Builder", "Printable Templates", and "Branding" cards
- Renamed "System Fields" card to "Forms & Fields" with updated description
- Updated description to "Manage System Forms, Fields, and Templates"

## [2.39.0] - 2025-12-14

### Changed
- Redesigned admin settings interface with a modern, card-based layout
- Removed sidebar navigation from settings pages for a cleaner, more intuitive interface
- Implemented mobile-first responsive design for all settings pages
- Organized settings into logical groups with visual cues and icons
- Enhanced visual hierarchy with consistent styling and improved typography
- Added hover effects and visual feedback for interactive elements
- Improved form layouts with better spacing and alignment
- Enhanced modals with more descriptive content and better visual design
- Added password visibility toggle for sensitive fields
- Improved maintenance section with larger, more accessible buttons

## [2.38.0] - 2025-12-07

### Added
- Added prominent "Manage Show Registrations" button for admins on show view page
- Enhanced admin and coordinator action buttons in show view with intuitive icons
- Improved UI consistency across admin and coordinator interfaces
- Better visual hierarchy for administrative actions

### Fixed
- Fixed deprecated warning: "Creation of dynamic property QrController::$db is deprecated" in QrController.php
- Properly declared $db property in QrController class
- Initialized $db in constructor to avoid dynamic property creation
- Removed redundant database instantiations throughout the QrController methods
- Improved code quality and compatibility with PHP 8.4.6

## [2.36.0] - 2025-11-23

### Fixed
- Fixed fatal error: "Call to undefined method AdminController::isAdmin()" in AdminController.php:4276
- Updated getEntities() method to use the correct auth->hasRole('admin') method for admin verification
- Improved consistency in admin access control methods across the application

## [2.35.0] - 2025-11-16

### Fixed
- Fixed fatal error: "Call to undefined method AdminController::isAdmin()" in AdminController.php
- Updated qrGenerator() method to use the correct auth->hasRole('admin') method for admin verification
- Improved consistency in admin access control methods across the application

## [2.34.0] - 2025-11-09

### Added
- Added unified QR code system that intelligently directs users based on their role
- Created new QrController to handle all QR code scanning with role-based redirection
- Implemented QR code analytics with tracking of scans by user role and entity type
- Created new print_judge_qr_codes.php page for printing judge QR codes
- Added "Print Judge QR Codes" button to admin and coordinator show pages
- Enhanced judging workflow with mobile-friendly QR code scanning
- Added qr_scans table to track QR code usage for analytics
- Created QR code test page for testing QR code scanning
- Added QR code generator utility for admins to create QR codes for any entity
- Enhanced registration details page with improved QR code display and testing options

### Changed
- Updated all QR code generation to use the new unified QR code system
- Modified print_qr_codes.php to use the new QR code endpoint
- Improved mobile-first responsive design for QR code printing page
- Enhanced user experience with intelligent redirection based on user role
- Simplified QR code printing and downloading process
- Added role-based information to QR code displays

## [2.33.0] - 2025-11-02

### Added
- Enhanced show view page to display user's registered vehicles
- Added vehicle registration cards with thumbnails on show pages
- Implemented "Register Another Vehicle" button when user has more vehicles to register
- Added method to get user's registered vehicles for a specific show
- Improved user experience by showing registration status directly on show pages
- Added direct links to registration details and QR codes for registered vehicles
- Made vehicle registration cards clickable for easy access to registration details

### Changed
- Modified ShowController to fetch and display user's registered vehicles
- Updated RegistrationModel with new getUserRegistrationsForShow method
- Improved mobile-first responsive design for vehicle registration cards
- Enhanced registration flow to better handle multiple vehicle registrations

## [2.32.0] - 2025-10-26

### Added
- Added new image_helper.php with functions for consistent thumbnail handling:
  - getThumbnailPath(): Gets thumbnail path if it exists
  - getImageUrl(): Gets full URL with thumbnail if available
  - imageTag(): Generates HTML img tag with thumbnail if available

### Changed
- Updated all card and small image displays to use thumbnails when available:
  - Vehicle cards in user/vehicles/index.php
  - Admin category judging vehicle thumbnails
  - Admin judging results vehicle thumbnails
  - Admin registration vehicle thumbnails
  - Admin view registration vehicle images
  - Admin view scores vehicle images
  - Admin judge vehicle images
  - Image editor vehicle and show thumbnails
  - Show banner images
  - Vehicle voting images
- Removed references to 'public' folder in vehicle and show image paths to match the server's directory structure:
  - Updated vehicle and show image paths in views to use '/uploads/' instead of '/public/uploads/'
  - Updated background image URL in judge_vehicle.php
  - Maintained original paths for QR code images

### Fixed
- Fixed issue where thumbnails weren't being deleted from the filesystem when deleting images
- Enhanced UserController::deleteImage() to properly delete thumbnail files
- Updated ImageEditorModel::deleteImage() in controllers directory to handle thumbnail deletion
- Improved thumbnail path detection for cases where the path isn't stored in the database
- Maintained mobile-first responsive design principles

## [2.31.0] - 2025-10-19

### Fixed
- Fixed issue where the primary image in the vehicle card on the left side of the image editor page wasn't displaying correctly
- Fixed incorrect image path in vehicle.php view (was using `/image_editor/vehicle/uploads/vehicles/` instead of `/uploads/vehicles/`)
- Simplified primary image display logic to use the correct BASE_URL path
- Improved VehicleModel::updateVehiclePrimaryImage() with better error handling and transaction support
- Updated ImageEditorController::setPrimary() method to ensure consistent primary image setting
- Added SQL update script to ensure all vehicles have their primary_image field correctly set
- Ensured proper synchronization between images table and vehicles table for primary images
- Maintained mobile-first responsive design principles

## [2.30.0] - 2025-10-12

### Fixed
- Fixed fatal error: "Too few arguments to function ImageEditorController::setPrimary()" in ImageEditorController.php
- Modified setPrimary() method to handle cases where not all required parameters are provided
- Modified setBanner() method to handle cases where not all required parameters are provided
- Added fallback to retrieve entity type and ID from the image when parameters are missing
- Fixed database error: "Unknown column 'primary_image' in 'field list'" in VehicleModel
- Enhanced VehicleModel::updateVehiclePrimaryImage() to check if primary_image column exists
- Added automatic creation of primary_image column in vehicles table if it doesn't exist
- Improved error handling for database operations in VehicleModel
- Maintained mobile-first responsive design principles

## [2.29.0] - 2025-10-05

### Added
- Enhanced Image Manager with user-specific image filtering
- Automatic image optimization based on settings in /admin/imageSettings
- Automatic thumbnail generation for all uploaded images
- Image resizing for large images based on configurable maximum dimensions
- Added user_id tracking for all uploaded images
- Added settings for maximum image dimensions and resizing options
- Improved image deletion to properly remove files from the filesystem

### Changed
- Updated Image Manager to only display images uploaded by the logged-in user (except for admins)
- Improved image upload process to use settings from /admin/imageSettings
- Enhanced image optimization to preserve transparency in PNG and GIF images

### Fixed
- Fixed issue where deleted images were not removed from the filesystem
- Improved error handling for image uploads and processing

## [2.28.3] - 2025-09-28

### Fixed
- Fixed "Upload Images" button on registration view page not redirecting to the correct upload page
- Fixed "Upload Images" button on vehicle images page (/user/vehicleImages/{id}) not working
- Fixed "Advanced Image Editor" button on vehicle images page pointing to non-existent route
- Added "Upload Images" and "Manage Images" buttons to vehicle information card
- Improved user experience by providing direct access to image management from registration details
- Ensured consistent image management links across the application
- Maintained mobile-first responsive design principles

## [2.28.2] - 2025-09-27

### Fixed
- Fixed .htaccess configuration to allow proper access to vehicle images
- Updated main .htaccess to explicitly exclude the uploads directory from URL rewriting
- Improved .htaccess in uploads/vehicles directory to properly allow image access
- Added .htaccess to uploads/vehicles/thumbnails directory to ensure thumbnail access
- Ensured compatibility with both Apache 2.2 and 2.4 using IfModule directives
- Maintained security by continuing to block PHP file access in uploads directories
- Preserved mobile-first responsive design principles

## [2.28.1] - 2025-09-26

### Fixed
- Corrected image paths to use the correct uploads directory (/uploads instead of /public/uploads)
- Fixed broken vehicle images by using the proper server directory structure
- Maintained compatibility with both old and new image storage systems
- Ensured proper display of images from both vehicle_images and images tables
- Maintained mobile-first responsive design principles

## [2.28.0] - 2025-09-25

### Fixed
- Fixed broken vehicle images in the vehicle images display page
- Enhanced image path handling to support multiple storage locations
- Added comprehensive path resolution for both old and new image storage systems
- Improved thumbnail handling with fallback to original images when thumbnails don't exist
- Fixed compatibility between admin image manager and user vehicle images
- Ensured proper display of images from both vehicle_images and images tables
- Maintained mobile-first responsive design principles

## [2.27.0] - 2025-09-24

### Fixed
- Fixed "Undefined property: stdClass::$filename" warnings in vehicle images display
- Updated images.php to handle both old and new image storage systems
- Added compatibility for both vehicle_images table (image_path) and images table (file_name)
- Added fallback to default image when neither filename nor file_name is available
- Improved error handling for image display
- Maintained mobile-first responsive design principles

## [2.26.0] - 2025-09-23

### Added
- Enhanced vehicle registration pages with filtering and sorting capabilities
- Added filter buttons (All Shows, Published, Draft) to admin and coordinator registerVehicle pages
- Added sortable columns for show name, location, dates, registration, coordinator, status, and entries
- Implemented clickable show names that navigate to the view show page
- Excluded completed and cancelled shows from the registration list by default
- Added mobile-first responsive design for filter buttons and table layout
- Improved user experience with visual indicators for sorting direction

### Changed
- Modified ShowModel to support filtering by status and excluding completed/cancelled shows
- Updated AdminController and CoordinatorController to handle filtering and sorting parameters
- Enhanced table layout with better spacing and visual hierarchy
- Improved accessibility with proper aria attributes and semantic HTML

## [2.25.0] - 2025-09-22

### Fixed
- Fixed fatal error: "Call to undefined method ShowModel::getCoordinatorShows()" in CoordinatorController.php
- Updated CoordinatorController::registerVehicle() to use the correct method ShowModel::getShowsByCoordinator()
- Ensured consistent method naming across the application
- Maintained mobile-first responsive design principles

## [2.24.0] - 2025-09-21

### Changed
- Improved UI of the registration management page with better button layout
- Enhanced mobile responsiveness of action buttons on the registrations page
- Replaced margin-based spacing with gap utility for more consistent button spacing
- Added proper vertical alignment between heading and action buttons
- Optimized button stacking behavior on small screens
- Improved overall visual appeal while maintaining the site's design language

## [2.23.0] - 2025-09-20

### Fixed
- Fixed "Undefined property: stdClass::$name" warning in registrations.php when no vehicles are registered
- Updated view to use the correct property name (category_name instead of name) to match database query results
- Improved consistency between database query results and view templates
- Maintained mobile-first responsive design principles

## [2.22.0] - 2025-09-19

### Fixed
- Fixed "Failed to open stream: No such file or directory" error in register_vehicle_for_user.php
- Updated header and footer includes to use correct path (/views/includes/ instead of /views/inc/)
- Fixed path issues in auth/forgot_password.php and auth/reset_password.php
- Ensured consistent header and footer inclusion across all views

## [2.21.0] - 2025-09-18

### Added
- Added ability for admins and coordinators to register vehicles after registration end date
- Implemented new admin interface for registering vehicles on behalf of users
- Implemented new coordinator interface for registering vehicles on behalf of users
- Added "Register Vehicle" button to admin and coordinator registration management pages
- Added "Register Vehicle for User" option to admin and coordinator dropdown menus
- Added show selection page for vehicle registration

### Changed
- Modified ShowModel::isRegistrationOpen method to support admin/coordinator override
- Updated RegistrationController to check for admin/coordinator roles

### Fixed
- Fixed issue where admins and coordinators couldn't register vehicles after the registration deadline

## [2.20.0] - 2025-09-16

### Fixed
- Fixed "Cannot access protected method ShowController::view()" fatal error
- Made ShowController::view() method public to allow proper routing
- Simplified ShowController implementation while maintaining APPROOT availability
- Ensured compatibility with App.php routing mechanism
- Maintained fail-safe APPROOT definition in view files
- Preserved mobile-first responsive design principles

## [2.19.99] - 2025-09-15

### Fixed
- Fixed "Undefined constant APPROOT" fatal error in show views
- Added APPROOT definition directly in show view files for fail-safe access
- Enhanced ShowController with robust APPROOT handling
- Implemented multiple fallback mechanisms for APPROOT definition
- Ensured consistent access to global variables across all views
- Maintained mobile-first responsive design principles

## [2.19.98] - 2025-09-14

### Fixed
- Fixed APPROOT access issue in show views
- Modified ShowController to ensure APPROOT is available in the global scope
- Ensured consistent access to global variables across all views
- Improved view loading mechanism in ShowController
- Maintained mobile-first responsive design principles

## [2.19.97] - 2025-09-13

### Fixed
- Fixed "Failed opening required 'admin_header.php'" error in fan_votes.php
- Removed references to non-existent admin_header.php and admin_sidebar.php files
- Updated admin and coordinator fan_votes.php files to use the existing admin_settings_sidebar.php
- Fixed layout issues in fan_votes.php and qr_codes.php pages
- Improved UI consistency with other admin pages
- Enhanced mobile-first responsive design principles

## [2.19.96] - 2025-09-12

### Added
- Enhanced fan favorite voting system with QR code support
- Added direct vehicle voting via QR code scanning on mobile devices
- Implemented dual authentication for voting: IP-based and Facebook login
- Created admin and coordinator monitoring pages for fan votes
- Added ability to export fan votes to CSV
- Added vote statistics dashboard with unique IP counts and Facebook vote tracking
- Enhanced JudgingModel with Facebook authentication support for fan votes
- Added SQL update script for fan_votes table to support Facebook authentication
- Maintained mobile-first responsive design principles

## [2.19.95] - 2025-09-11

### Fixed
- Fixed fatal error: "Cannot redeclare ShowModel::tableExists()" in ShowModel.php
- Removed duplicate tableExists() method declaration at the end of ShowModel class
- Maintained consistent database table existence checking functionality
- Ensured mobile-first responsive design principles

## [2.19.90] - 2025-09-10

### Fixed
- Fixed "Call to undefined method ShowModel::createAgeWeight()" fatal error
- Added missing age weights management methods to ShowModel:
  - getAgeWeights: Retrieves age weights for a show
  - createAgeWeight: Creates new age weights
  - updateAgeWeight: Updates existing age weights
  - deleteAgeWeight: Deletes age weights
- Added createAgeWeightsTable method to create the age_weights table if it doesn't exist
- Added tableExists method to check if a table exists in the database
- Enhanced error handling for all age weight operations
- Maintained mobile-first responsive design principles

## [2.19.89] - 2025-09-09

### Fixed
- Fixed "Undefined array key" warnings in age_weights view files
- Updated field names in age_weights views to match database structure (min_age/max_age instead of min_year/max_year)
- Removed non-existent name field from age_weights forms
- Improved age_weights interface with better descriptions and simplified table layout
- Enhanced error handling for age weight management
- Maintained mobile-first responsive design principles

## [2.19.88] - 2025-09-08

### Fixed
- Fixed "Unknown column 'is_active' in 'field list'" error in createMetric method
- Added dynamic column detection for judging_metrics table to handle different database structures
- Fixed "Undefined array key 'display_order'" warning in CoordinatorController
- Enhanced error handling for database operations in metric management
- Maintained mobile-first responsive design principles

## [2.19.87] - 2025-09-07

### Fixed
- Added missing createCategory method to ShowModel to fix fatal error in CoordinatorController
- Fixed CSRF token validation failure in metrics management forms
- Updated metrics add, edit, and delete forms to use csrfTokenField() instead of generateCsrfToken()
- Ensured proper CSRF token handling in all metrics management operations
- Maintained mobile-first responsive design principles

## [2.19.86] - 2025-09-06

### Fixed
- Fixed CSRF token validation failure in category management forms
- Updated category add, edit, and delete forms to use csrfTokenField() instead of generateCsrfToken()
- Ensured proper CSRF token handling in all category management operations
- Maintained mobile-first responsive design principles

## [2.23.5] - 2025-09-05

### Fixed
- Fixed "Delete All Categories" functionality in coordinator's editShow page
- Corrected table name from 'categories' to 'show_categories' in deleteAllCategories method
- Prevented SQL error when trying to delete categories
- Improved error handling for database operations

## [2.23.4] - 2025-09-04

### Fixed
- Fixed "Add Default Categories" button in coordinator's editShow page submitting the main form instead
- Restructured edit_show.php to move category management outside the main form
- Prevented form nesting issues that were causing incorrect form submissions
- Maintained mobile-first responsive design principles

## [2.23.3] - 2025-09-03

### Fixed
- Fixed case sensitivity issue in DefaultCategoryModel's rollback method call
- Corrected method name from rollback() to rollBack() to match Database class implementation
- Ensured database transactions work correctly when adding default categories to shows

## [2.23.2] - 2025-09-02

### Fixed
- Fixed "Add Default Categories" button not working in coordinator's editShow page
- Fixed syntax error in addDefaultCategoriesToShow method that prevented default categories from being added
- Removed duplicate code in addDefaultCategoriesToShow method
- Ensured proper flash message handling for better user feedback

## [2.23.1] - 2025-09-01

### Fixed
- Fixed "Add Default Categories" button redirecting to home page in coordinator interface
- Restructured addDefaultCategoriesToShow, addDefaultMetricsToShow, and addDefaultAgeWeightsToShow methods
- Improved error handling and validation in all "Add Default" methods
- Ensured consistent behavior across all default item addition methods
- Maintained mobile-first responsive design principles

## [2.23.0] - 2025-08-31

### Fixed
- Fixed non-working "Add Default" buttons in coordinator's edit show page
- Updated CoordinatorController to use DefaultCategoryModel, DefaultMetricModel, and DefaultAgeWeightModel
- Replaced hardcoded default categories, metrics, and age weights with model-based implementations
- Improved error handling for default item addition
- Added checks for existing categories, metrics, and age weights before attempting to add default ones
- Prevented duplicate items from being added when using "Add Default" buttons
- Added clear warning messages when items already exist
- Maintained mobile-first responsive design principles

## [2.22.0] - 2025-08-30

### Added
- Added user impersonation feature for administrators
- Administrators can now temporarily login as any user (judge, coordinator, or regular user)
- Added "Return to Admin" button that appears when impersonating a user
- Added database table to track impersonation sessions
- Added security measures to prevent unauthorized impersonation

## [2.21.0] - 2025-08-29

### Fixed
- Fixed fatal error: "Call to undefined method ShowModel::addMetric()" in CoordinatorController
- Updated addDefaultMetricsToShow method to use createMetric instead of non-existent addMetric method
- Added required parameters (max_score, display_order, is_active) to metric creation
- Enhanced error handling for metric creation process
- Maintained mobile-first responsive design principles

## [2.20.0] - 2025-08-28

### Fixed
- Fixed redirect issues with delete buttons and "Add Default" buttons in coordinator's edit show page
- Added missing controller methods for bulk operations (deleteAllCategories, deleteAllMetrics, deleteAllAgeWeights)
- Added missing controller methods for default items (addDefaultCategoriesToShow, addDefaultMetricsToShow, addDefaultAgeWeightsToShow)
- Improved flash messages for all operations
- Ensured all actions redirect back to the edit show page
- Enhanced error handling for all operations

## [2.19.99] - 2025-08-27

### Fixed
- Enhanced coordinator's edit show page to fully match admin functionality (except coordinator selection)
- Added table colors and styling to match admin interface
- Added buttons for inserting defaults and deleting items
- Added delete confirmation modals for categories, metrics, and age weights
- Added support for deleting all categories, metrics, or age weights at once
- Improved visual consistency with admin interface
- Enhanced JavaScript functionality for interactive elements
- Maintained mobile-first responsive design principles

## [2.19.98] - 2025-08-26

### Fixed
- Fixed PHP fatal error in coordinator's edit show page: "Call to undefined method CoordinatorController::generateCsrfToken()"
- Updated CSRF token handling to use the csrfTokenField() helper function
- Added FormFieldSynchronizer to ensure form fields match database columns
- Added fallback for when no form template is found
- Added validation for form fields to prevent errors with invalid templates
- Enhanced error handling throughout the form template processing
- Improved template creation and field synchronization

## [2.19.97] - 2025-08-25

### Fixed
- Fixed empty "Edit Show" page at /coordinator/editShow/4 for coordinators
- Enhanced coordinator's edit show page to match admin functionality (except coordinator selection)
- Added support for custom form fields, categories, metrics, and age weights in coordinator's edit show page
- Added JavaScript to handle the is_free checkbox toggling the registration_fee field
- Improved mobile-first responsive design for the edit show page
- Maintained consistent URL structure using URLROOT instead of BASE_URL

## [2.19.96] - 2025-08-24

### Fixed
- Fixed non-working "Update Status" and "Update Payment" buttons on /coordinator/view_registration/30 page
- Fixed persistent issue where payment status updates were not being saved to the database
- Fixed issue where only "refunded" payment status was being saved to the database
- Fixed "Unknown column 'payment_date' in 'field list'" error in payment updates
- Added missing update_registration and update_payment methods to CoordinatorController as URL-friendly aliases
- Fixed updatePayment method to properly handle form data and use the more flexible updateRegistration method
- Added payment_reference field handling in updateRegistration method
- Added column existence check before attempting to update payment_date
- Added direct SQL update as fallback when model-based updates fail
- Added verification steps to confirm updates are actually applied to the database
- Added detailed column information logging to diagnose database structure issues
- Added more aggressive SQL script to ensure payment_status column is properly defined as VARCHAR(50)
- Added SQL script to ensure payment_reference and payment_date columns exist with correct data types
- Added enhanced SQL execution logging for better troubleshooting
- Added detailed payment status tracking in logs
- Added success flash message for payment status updates
- Simplified payment update process by removing unnecessary validation
- Maintained consistent URL structure for coordinator actions
- Improved error handling for registration management operations
- Maintained mobile-first responsive design principles

## [2.19.95] - 2025-08-23

### Fixed
- Fixed non-working "view" button on /coordinator/registrations/4 page
- Added missing view_registration method to CoordinatorController as URL-friendly alias for viewRegistration
- Ensured "delete" button functionality works correctly in coordinator registration management
- Maintained consistent URL structure for coordinator actions
- Improved error handling for registration management operations
- Maintained mobile-first responsive design principles

## [2.19.94] - 2025-08-22

### Fixed
- Fixed "The requested view 'coordinator/judges/index' could not be found" error when clicking "manage" in Judge Assignments table
- Created missing coordinator/judges/index.php view file
- Fixed "Call to undefined function csrfField()" error in judge assignments view
- Fixed "Deprecated: Creation of dynamic property CoordinatorController::$db is deprecated" warning in PHP 8.4.6
- Fixed "SQLSTATE[HY093]: Invalid parameter number" error in judge assignment functionality
- Fixed non-functioning "Remove" button in judge assignments management
- Renamed removeJudge method to removeJudgeAssignment to match form action URL
- Improved SQL queries to properly handle NULL values in category_id
- Modified database queries to avoid binding parameters to NULL checks
- Added missing assignJudge method to ShowModel
- Added missing removeJudgeAssignment method to ShowModel
- Properly declared the $db property in CoordinatorController to prevent dynamic property creation
- Implemented proper CSRF token handling in the judge assignments interface
- Implemented proper judge assignment management interface with mobile-first responsive design
- Added breadcrumb navigation for better user experience
- Maintained consistent UI styling with other coordinator views

## [2.19.93] - 2025-08-21

### Fixed
- Fixed "Undefined array key 'category_id'" error in coordinator metrics edit view
- Fixed "Undefined array key 'categories'" error in coordinator metrics edit view
- Fixed "foreach() argument must be of type array|object, null given" error in metrics views
- Added category_id column to judging_metrics table
- Updated CoordinatorController to load categories for metric forms
- Enhanced ShowModel methods to handle category_id field
- Maintained mobile-first responsive design principles

## [2.19.92] - 2025-08-20

### Fixed
- Fixed "Call to undefined method ShowModel::getMetricById()" error in CoordinatorController
- Added missing getMetricById method to ShowModel
- Added missing createMetric method to ShowModel
- Added missing updateMetric method to ShowModel
- Updated ShowModel version number to reflect new methods
- Enhanced error handling in metric management methods
- Maintained mobile-first responsive design principles

## [2.19.91] - 2025-08-19

### Fixed
- Fixed "Undefined property: stdClass::$max_points" error in coordinator metrics view
- Updated form field names from max_points to max_score in add/edit metric forms
- Ensured consistency between database column names and view property names
- Added null coalescing operator for safer property access
- Maintained mobile-first responsive design principles

## [2.19.90] - 2025-08-19

### Fixed
- Fixed "Undefined property: stdClass::$category_name" error in registration counts display
- Added 'Uncategorized' label for registrations without a category name
- Enhanced property existence checks for registration data
- Maintained mobile-first responsive design principles

## [2.19.90] - 2025-08-18

### Fixed
- Fixed "Undefined property: stdClass::$event_date" error in coordinator show view
- Fixed "Undefined property: stdClass::$is_active" error in coordinator show view
- Fixed "Undefined property: stdClass::$category_name" error in coordinator show view
- Fixed "Undefined property: stdClass::$category_id" error in coordinator show view
- Fixed "Undefined property: stdClass::$has_judged" error in coordinator show view
- Fixed "Passing null to parameter #1 ($datetime) of type string is deprecated" warning in strtotime()
- Updated show.php to use start_date instead of event_date
- Updated show.php to use status instead of is_active
- Added proper property existence checks before accessing object properties
- Added fallback display for missing data
- Maintained mobile-first responsive design principles

## [2.19.89] - 2025-08-17

### Fixed
- Fixed fatal error "Call to undefined method ShowModel::getJudgeAssignments()" in CoordinatorController
- Added missing getJudgeAssignments method to ShowModel
- Implemented robust error handling for judge assignments retrieval
- Added table existence checks to prevent errors with incomplete database setup
- Enhanced ShowModel version number to reflect the latest changes
- Maintained mobile-first responsive design principles

## [2.19.88] - 2025-08-16

### Fixed
- Fixed "Undefined property: stdClass::$event_date" error in coordinator dashboard
- Fixed "Passing null to parameter #1 ($datetime) of type string is deprecated" warning in strtotime()
- Fixed "Undefined property: stdClass::$is_active" error in coordinator dashboard
- Updated dashboard.php to use start_date instead of event_date
- Added proper null checks before using date functions
- Added fallback display for missing dates
- Improved status display to use show status instead of is_active property
- Maintained mobile-first responsive design principles

## [2.19.87] - 2025-08-15

### Added
- Added delete button for QR code logo in QR code settings
- Implemented functionality to remove existing QR code logos
- Enhanced user experience with confirmation dialog before logo deletion
- Added comprehensive path detection for QR code logos
- Added detailed error logging for QR code logo operations

### Fixed
- Fixed issue with logos not appearing in generated QR codes
- Resolved logo file path detection problems
- Improved logo handling with multiple path fallbacks
- Enhanced image type detection for different logo formats

## [2.19.86] - 2025-08-14

### Added
- Added "Regenerate QR Code" button to the registration details page
- Implemented functionality to regenerate QR codes using the latest QR code settings
- Enhanced user experience by allowing QR code regeneration without leaving the registration details page
- Added integration with Google Chart API for reliable QR code generation
- Added fallback to QRServer API when Google Chart API is unavailable
- Added comprehensive error handling for all QR code generation methods

### Fixed
- Fixed "Undefined constant QR_ECLEVEL_L" error in QR code generation
- Completely replaced non-functional placeholder QR code library with reliable API-based solution
- Fixed issue where generated QR codes were not scannable by QR code readers
- Fixed QR code generation to properly apply all settings (size, margin, error correction)
- Resolved issue with logo placement on QR codes

### Enhanced
- Maintained mobile-first responsive design principles throughout the implementation
- Integrated the new button seamlessly with existing QR code actions
- Ensured regenerated QR codes use all settings from the QR Code Settings page
- Improved code structure for better reliability and error handling
- Removed unnecessary custom QR code generation code that produced invalid QR codes
- Added cascading fallback system for maximum reliability
- Enhanced logo support to work with API-generated QR codes

## [2.19.85] - 2025-08-13

### Fixed
- Fixed "Undefined variable $categories" error in registrations.php
- Added categories loading to AdminController and CoordinatorController registrations methods
- Added fallback handling in views to prevent errors when categories are not available
- Improved error handling in category dropdown display

## [2.19.84] - 2025-08-12

### Added
- Added ability for admins to view all registrations across all shows
- Added ability for coordinators to view registrations for shows they manage
- Added show dropdown filter to easily switch between shows
- Added owner column in all registrations view for better identification
- Added methods in RegistrationModel to support new functionality
- Added "Export to CSV" functionality for registration data
- Added print-friendly view with dedicated styling
- Added "New Registration" button for quick access to registration creation
- Added "Back to All Registrations" button when viewing filtered results
- Added ensureRequiredFields method to prevent undefined property errors

### Enhanced
- Enhanced AdminController to handle different view modes for registrations
- Improved user_registrations.php view to support multiple view modes
- Enhanced JavaScript search functionality to search across all fields
- Improved redirect handling after registration actions
- Added mobile-specific styling for better responsiveness
- Improved error handling in database queries with fallback mechanisms
- Enhanced UI with better context-specific messages and counts

### Fixed
- Fixed "Undefined property: stdClass::$start_date" errors in user_registrations.php
- Fixed "Passing null to parameter #1 ($datetime) of type string is deprecated" warnings in strtotime()
- Fixed "Undefined property: stdClass::$show_name" errors when viewing registrations
- Fixed "Undefined property: stdClass::$end_date" errors in date display
- Added proper null checks for all property accesses in templates
- Improved database queries to include all necessary fields
- Added fallback mechanisms for missing data in registration objects

## [2.19.83] - 2025-08-11

### Added
- Added missing 'print_qr_code.php' view for admin interface
- Added 'print_qr_code.php' view for user interface with proper styling
- Added printQrCode method to UserController to handle user-side QR code printing

### Fixed
- Fixed "The requested view 'admin/print_qr_code' could not be found" error
- Fixed undefined property errors in print_qr_code.php by using correct property names
- Enhanced AdminController's generateQrCode method to handle return URLs
- Improved navigation flow after QR code generation

## [2.19.82] - 2025-08-10

### Enhanced
- Enhanced QR code generation to use all settings from the admin interface
- Added support for QR code size, margin, and error correction level settings
- Improved QR code generation with phpqrcode library to support logos
- Enhanced QR code settings page with live preview and additional options
- Added tips for optimal QR code generation in the admin interface

## [2.19.81] - 2025-08-09

### Added
- Added printable_templates table for storing custom printable templates
- Added default vehicle registration template with QR code support
- Added scripts to create and manage printable templates
- Added QR code support to registration printouts
- Added web-based database update script for QR code support

### Fixed
- Fixed QR code storage in the database by updating the RegistrationModel
- Fixed updateRegistration method to handle both function signatures
- Fixed error handling in QR code generation process
- Added proper error logging for QR code operations

## [2.19.80] - 2025-08-08

### Fixed
- Fixed QR code generation functionality on the registration details page
- Fixed routing issue that prevented the generateQrCode method from being called
- Enhanced App.php routing to properly handle special methods like generateQrCode
- Improved QR code content to link directly to judging and fan voting pages
- Added phpqrcode library for better QR code generation
- Maintained mobile-first responsive design principles throughout the fix

### Added
- Added simplified phpqrcode library for QR code generation
- Added direct links to judging and fan voting pages in QR codes

## [2.19.79] - 2025-08-07

### Fixed
- Fixed "Undefined property: stdClass::$event_date" error in judge dashboard
- Fixed "Passing null to parameter #1 ($datetime) of type string is deprecated" warning in strtotime()
- Updated dashboard.php to use start_date instead of event_date
- Added proper null checks before using date functions
- Added fallback display for missing dates
- Maintained mobile-first responsive design principles

## [2.19.78] - 2025-08-06

### Fixed
- Fixed "You are not currently assigned to judge any shows" error on judge dashboard
- Added fix_judge_assignments.php script to repair judge assignments table
- Added SQL script to ensure judge_assignments table exists and has correct structure
- Made category_id column nullable in judge_assignments table
- Improved error handling in ShowModel::getShowsByJudge method

## [2.19.77] - 2025-08-05

### Fixed
- Fixed fatal error in flash() function by enhancing it to handle single-argument calls
- Modified flash() to work as both a setter and getter depending on argument count
- Improved session_helper.php documentation to clarify dual functionality
- Enhanced error handling in flash message system

## [2.19.76] - 2025-08-04

### Fixed
- Fixed fatal error in JudgeController by implementing missing getShowsByJudge method in ShowModel
- Added getJudgeCategories method to ShowModel to support judge dashboard functionality
- Added proper error handling and table existence checks for judge-related methods
- Updated version number in ShowModel to reflect changes

## [2.19.75] - 2025-08-03

### Added
- Added direct access to Judge and Coordinator dashboards for Admin users
- Added Judging and Coordination section to the Admin Tools dropdown menu
- Added dedicated section for judging and coordination in the admin settings sidebar

### Changed
- Modified header.php to include Judge and Coordinator dashboard links in the admin dropdown
- Updated admin_settings_sidebar.php to include a dedicated section for judging and coordination features
- Enhanced navigation structure to provide admins with unified access to all system features

## [2.19.74] - 2025-08-02

### Changed
- Centered pie charts vertically to align with metric tables
- Improved layout alignment for better visual presentation
- Enhanced responsive design for all screen sizes
- Optimized UI for better data comparison

## [2.19.73] - 2025-08-01

### Changed
- Restored color dots in metric table for better visual correlation with pie chart
- Removed only the legend from pie charts for a cleaner appearance
- Maintained visual consistency between chart colors and table indicators
- Enhanced the user interface for better data visualization

## [2.19.72] - 2025-07-31

### Changed
- Removed the legend from pie charts for a cleaner appearance
- Simplified the chart display while maintaining the visual data representation
- Improved overall visual consistency and readability
- Enhanced the user interface for better focus on important data
- Removed the legend from pie charts for a cleaner appearance
- Simplified the chart display while maintaining the visual data representation
- Improved overall visual consistency and readability
- Enhanced the user interface for better focus on important data

## [2.19.71] - 2025-07-30

### Changed
- Removed color dots from metric breakdown table for cleaner appearance
- Simplified the metric table display while maintaining the pie chart colors
- Improved overall visual consistency and readability
- Enhanced the user interface for better focus on important data

## [2.19.70] - 2025-07-29

### Fixed
- Fixed illegal continue statement error in JavaScript
- Restructured chart initialization code to use proper conditional blocks
- Improved error handling to avoid JavaScript syntax errors
- Enhanced code structure for better browser compatibility
- Optimized chart rendering for all browsers

## [2.19.69] - 2025-07-28

### Fixed
- Completely rewrote chart initialization JavaScript to fix syntax errors
- Simplified the code structure to eliminate unexpected token errors
- Improved error handling with more detailed console messages
- Enhanced chart rendering reliability across all browsers
- Optimized JavaScript for better performance

## [2.19.68] - 2025-07-27

### Fixed
- Fixed JavaScript error that was preventing pie charts from rendering
- Simplified chart initialization code for better reliability
- Improved error handling for chart rendering
- Fixed layout issues with judge score sections
- Enhanced mobile responsiveness for all screen sizes

## [2.19.67] - 2025-07-26

### Added
- Completely redesigned the score breakdown section with improved pie charts
- Added progress bars to visualize percentage scores for each metric
- Implemented a more mobile-friendly layout with better organization
- Enhanced the metric tables with additional percentage information
- Improved the visual hierarchy and information flow

## [2.19.66] - 2025-07-25

### Added
- Added pie charts to visualize score breakdown by metric for each judge
- Implemented side-by-side metric tables with color-coded indicators
- Enhanced score visualization with percentage calculations
- Added tooltips to show detailed score information on hover
- Improved the overall visual presentation of judging data

## [2.19.65] - 2025-07-24

### Fixed
- Reverted to a simpler, more reliable design for the viewScores page
- Fixed layout issues that were causing the page to break
- Improved the score summary section to be more readable
- Enhanced mobile responsiveness with a cleaner layout
- Removed Chart.js dependency to improve reliability

## [2.19.64] - 2025-07-23

### Added
- Redesigned the viewScores page with metric-based pie charts for each judge
- Implemented tabbed interface to switch between different judges' metric breakdowns
- Added color-coding for each metric in the pie charts
- Enhanced the score visualization to show percentage distribution by metric
- Improved mobile responsiveness with a more compact layout

## [2.19.63] - 2025-07-22

### Added
- Completely redesigned the viewScores page with a mobile-first responsive layout
- Added interactive pie chart visualization of judge scores using Chart.js
- Implemented color-coded judge scores for better visual distinction
- Added compact score summary cards at the top of the page
- Enhanced vehicle information display with improved layout

### Fixed
- Improved mobile responsiveness throughout the judging interface
- Fixed table layout issues on small screens
- Enhanced visual hierarchy for better information scanning

## [2.19.62] - 2025-07-21

### Fixed
- Completely rewrote the score retrieval logic to fix persistent display issues
- Eliminated complex JOIN queries that were failing to return results
- Implemented a more robust approach to fetch and combine score data
- Added fallback mechanisms to ensure scores are always displayed
- Enhanced error detection and recovery for score organization

## [2.19.61] - 2025-07-20

### Fixed
- Fixed critical issue with judge ID handling in the viewScores page
- Corrected database query to properly join user and judge information
- Added extensive debugging to track score retrieval and display
- Fixed issue where scores were not displayed due to judge ID mismatch
- Improved error handling and logging for score retrieval

## [2.19.60] - 2025-07-19

### Fixed
- Fixed issue where scores were not displayed on the viewScores page even when they existed
- Improved score display to show both final and draft scores with visual indicators
- Enhanced judging status display to show complete, draft, partial, or not judged status
- Added detailed logging for score retrieval and organization
- Added legend to explain draft scores on the viewScores page

## [2.19.59] - 2025-07-18

### Fixed
- Fixed issue where judging status was not correctly displayed on the category judging page
- Added proper detection of judging status for each registration and judge
- Improved judging status display to show complete, draft, partial, or not judged status
- Added detailed logging for judging status detection

## [2.19.58] - 2025-07-17

### Fixed
- Fixed issue where scores were not being saved correctly on the judging page
- Added automatic loading of the current judge's scores when visiting the judging page
- Improved judge selection to preselect the current user
- Added JavaScript to dynamically load scores when changing judges
- Enhanced error handling and debugging in score saving process
- Fixed redirect to stay on the judging page after saving scores

## [2.19.57] - 2025-07-16

### Fixed
- Fixed "Unknown column 'is_draft' in 'field list'" error when saving scores
- Added automatic creation of scores table with is_draft column if it doesn't exist
- Fixed SQL syntax errors in tableExists and columnExists methods
- Improved error handling in database operations

## [2.19.56] - 2025-07-15

### Enhanced
- Redesigned the vehicle judging page with a streamlined, tab-free layout
- Improved the flow of the page with vehicle details and judging status at the top
- Maintained mobile-friendly design with responsive layout for all screen sizes
- Kept interactive scoring with sliders and +/- buttons for easier input on mobile devices
- Enhanced visual presentation with better spacing, typography, and visual cues

## [2.19.55] - 2025-07-14

### Enhanced
- Completely redesigned the vehicle judging page with a mobile-first approach
- Added interactive scoring with sliders and +/- buttons for easier input on mobile devices
- Implemented tabbed interface for better organization of judging form, vehicle details, and status
- Improved visual presentation with better spacing, typography, and visual cues
- Added helpful instructions and visual feedback for judges

## [2.19.54] - 2025-07-13

### Fixed
- Removed non-existent admin_navbar.php include from judge_vehicle.php and view_scores.php views

## [2.19.53] - 2025-07-12

### Fixed
- Fixed include path errors in judge_vehicle.php and view_scores.php views by updating paths from '/views/inc/' to '/views/includes/'

## [2.19.52] - 2025-07-11

### Fixed
- Fixed "Deprecated: Creation of dynamic property AdminController::$categoryModel" warning by properly declaring the property

## [2.19.51] - 2025-07-10

### Added
- Added ShowCategoryModel.php to handle show category operations

### Fixed
- Fixed "Model file not found: ShowCategoryModel.php" error by creating the missing model file

## [2.19.50] - 2025-07-09

### Fixed
- Fixed undefined property error by initializing categoryModel in AdminController

## [2.19.49] - 2025-07-08

### Added
- Added judgeVehicle and viewScores methods to AdminController
- Added judge_vehicle.php and view_scores.php views for judging vehicles and viewing scores
- Added getScoresByRegistrationAndJudge and getScoresByRegistration methods to JudgingModel

### Fixed
- Fixed non-functional judge and scores buttons in category_judging.php view

## [2.19.48] - 2025-07-07

### Fixed
- Fixed undefined property warning in category_judging.php by using registration_number instead of display_number
- Fixed vehicle image display in category_judging.php by using primary_image instead of vehicle_image
- Updated table header in category_judging.php to match the displayed data

## [2.19.47] - 2025-07-06

### Added
- Added getRegistrationsByCategory method to RegistrationModel

### Fixed
- Fixed fatal error in categoryJudging method by implementing the missing getRegistrationsByCategory method

## [2.19.46] - 2025-07-05

### Added
- Added fix_judges_table.sql script with MySQL 8.0.41 compatible syntax
- Added default value update for existing rows in ensureColumnExists method

### Fixed
- Fixed SQL syntax error in ALTER TABLE statement by removing IF NOT EXISTS clause
- Fixed compatibility issues with MySQL 8.0.41 in SQL scripts
- Fixed column addition process to be compatible with older MySQL versions

## [2.19.45] - 2025-07-04

### Added
- Added update_judges_table.sql script for manual database updates
- Added explicit column check in updateShowJudges method

### Fixed
- Fixed persistent "Unknown column 'is_active'" error by ensuring column exists before using it
- Fixed table creation sequence to properly handle existing tables without required columns

## [2.19.44] - 2025-07-03

### Added
- Added ensureColumnExists method to JudgingModel for ensuring columns exist in tables

### Fixed
- Fixed SQL syntax error in tableExists method by using information_schema instead of SHOW TABLES
- Fixed missing is_active column in judges table by adding column check and creation
- Fixed error handling in database table creation and modification

## [2.19.43] - 2025-07-02

### Added
- Added getJudgeAssignedCategories method to JudgingModel for retrieving assigned categories for a judge

### Fixed
- Fixed error in assign_judges.php view by removing direct database access from the view
- Fixed judge category assignments by properly passing data from controller to view
- Fixed flash message handling in assign_judges.php view
- Fixed inconsistent flash message usage by using setFlashMessage directly

## [2.19.42] - 2025-07-01

### Added
- Added judge assignment functionality to the judging system
- Added category judging functionality for scoring vehicles
- Added updateShowJudges method to JudgingModel
- Added assignJudges and categoryJudging methods to AdminController
- Added assign_judges and category_judging views
- Added getUsersByRoles method to UserModel for retrieving users with specific roles

### Fixed
- Fixed "Assign Judges" button functionality on the /admin/judging/ page
- Fixed "Judge Vehicles" button functionality for each category
- Fixed deprecated dynamic property warning for $awardModel in AdminController
- Fixed initialization of AwardModel in the constructor
- Fixed fatal error in assignJudges method by implementing the missing getUsersByRoles method

## [2.19.41] - 2025-06-30

### Added
- Added award management functionality to the judging system
- Added AwardModel for handling award operations
- Added addAward method to AdminController
- Added add_award view for creating new awards
- Added update_awards_table.sql script to create the awards table
- Added support for different award types: category, special, and overall

### Fixed
- Fixed "add award" functionality on the /admin/judging/ page
- Fixed deprecated dynamic property warning for $awardModel in AdminController
- Fixed fatal error with flash() function in add_award.php view

## [2.19.40] - 2025-06-29

### Added
- Added missing judging method to AdminController to fix navigation from registrations page
- Added getJudgesByShowId, getJudgesByCategory, getJudgedVehicleCountByShow, and getJudgedVehicleCountByCategory methods to JudgingModel
- Added getRegistrationCountByShow and getRegistrationCountByCategory methods to RegistrationModel
- Added table existence checking and automatic table creation for judges and judge_categories tables
- Added update_judging_tables.sql script to create all necessary judging-related tables if they don't exist

### Fixed
- Fixed issue with judging link on admin/registrations page redirecting to home page instead of judging page
- Fixed navigation between show management pages by implementing proper judging controller method
- Fixed method name from getCategoriesByShowId to getShowCategories in AdminController
- Fixed fatal error by removing duplicate columnExists() method in JudgingModel

## [2.19.39] - 2025-06-28

### Added
- Added 'free' and 'waived' options to payment_status enum in database schema
- Added database update script (update_payment_status.sql) to modify existing databases
- Updated user dashboard to properly display 'free' and 'waived' payment statuses

### Fixed
- Fixed issue with payment status display in user dashboard for free registrations
- Fixed inconsistency between 'pending' and 'unpaid' payment statuses

## [2.19.38] - 2025-06-27

### Added
- Added dynamic form rendering system for registration forms
- Added form_helper.php with renderDynamicForm and renderFormField functions
- Added support for using custom templates in registration forms
- Added automatic year dropdown generation for year_of_manufacture field (1900 to present)
- Added getCategoryById method to ShowModel for category validation
- Added special handling for free shows in registration process

### Fixed
- Fixed issue with category_id field on registration form not properly displaying show categories
- Added registration counts to show object in RegistrationController to properly display category availability
- Fixed registration form to use templates created in form builder
- Fixed dynamic form rendering to properly handle system fields regardless of template configuration
- Added special handling for category_id, vehicle_id, and payment_method_id fields to ensure they always display correctly
- Added automatic detection and addition of missing required fields in templates
- Implemented fallback rendering for category dropdowns when template options are not properly configured
- Fixed CSRF token handling in dynamic form rendering to prevent form submission errors
- Updated registration form to use csrfTokenField() helper for consistent token generation
- Fixed foreign key constraint violation in registration creation by adding user_id field
- Added improved error handling for database operations in RegistrationModel
- Modified registration process to handle free shows correctly
- Updated user registrations view to display "Free" for free registrations
- Fixed undefined property warnings in admin registrations view
- Added proper handling for missing registration data in admin view

## [2.19.37] - 2025-06-26

### Added
- Added ability to add default categories, metrics, and age weights to shows from the edit show page
- Created new database tables for storing default categories, metrics, and age weights
- Added admin pages for managing default categories, metrics, and age weights
- Added buttons to quickly add default categories, metrics, and age weights to shows
- Added links to default categories, metrics, and age weights in the admin settings sidebar
- Added new Age Weight Metrics section to the show edit page

### Changed
- Updated admin controller to handle default categories, metrics, and age weights
- Enhanced show editing interface with new buttons for adding defaults
- Improved admin settings sidebar with better organization of default settings

## [2.19.36] - 2025-06-25

### Fixed
- Fixed issue with form submission for template types "event" and "show" by properly validating required fields based on entity type
- Added proper entity type mapping for form validation in FormDesignerController
- Fixed syntax error in SystemFieldManager.php by removing duplicate method declaration
- Fixed issue with event form templates showing "unknown" entity type by adding proper mapping
- Fixed issue with show templates using event fields instead of registration fields
- Fixed redirect issues after saving templates by rendering templates view directly

## [2.19.35] - 2025-06-24

### Fixed
- Fixed issue where padlock icons weren't displaying for all fields in vehicle templates
- Added automatic system field creation in FormDesignerController
- Enhanced debugging for system field detection
- Improved database checks to ensure vehicle system fields exist
- Added direct database verification in edit method to guarantee system fields are available
- Fixed issue where all system fields had incorrect entity_type values
- Added automatic entity_type correction in SystemFieldManager
- Created database scripts to fix entity_type values in system_fields table
- Added simplified SQL update script for better MySQL compatibility
- Updated SystemFieldManager with complete list of system fields from all entity types
- Added support for registration entity type in form designer
- Created comprehensive SQL scripts to ensure system_fields table is properly set up
- Fixed issue where saving system fields configuration would reset all entity types to "event"
- Fixed deprecated dynamic property warning in FormDesignerController for PHP 8.4 compatibility
- Fixed issue where system fields form wasn't updating for vehicle templates
- Enhanced entity type detection for system fields to ensure proper categorization
- Added visual indicators for entity types in the system fields management UI
- Fixed handling of fields that appear in multiple form types (plate, vehiclename)
- Added special handling for auto-populated reference fields in registration forms
- Improved template type detection for more accurate field categorization
- Enhanced debugging for system fields management
- Fixed issue with system fields form not saving changes when checkboxes are unchecked
- Fixed entity type handling for vehicle and registration fields
- Improved system field detection and management
- Added detailed debugging for system fields by entity type
- Added comprehensive debug information to help troubleshoot form submission issues
- Improved error handling and user feedback for system fields management

## [2.19.35] - 2025-06-24
- Added critical field protection to system fields management
- Critical system fields now cannot be unchecked or modified
- Improved visual indication of critical fields with lock icons
- Added permanent informational card explaining disabled checkboxes for critical fields
- Fixed issue with alert messages disappearing too quickly
- Enhanced database schema to track critical system fields

## [2.19.34] - 2025-06-23

### Fixed
- Fixed issue where padlock icons weren't displaying for vehicle type templates in form designer
- Improved system field detection for all template types
- Enhanced SystemFieldManager to ensure all default fields exist in the database
- Added better error logging for system field operations
- Fixed fallback mechanism for critical fields in vehicle templates
- Fixed syntax error in SystemFieldManager.php that was causing PHP parse errors
- Updated edit_final.php template to properly display padlock icons for system fields
- Ensured consistent styling between edit.php and edit_final.php templates

## [2.19.33] - 2025-06-22

### Added
- Implemented admin registration editing functionality
- Added comprehensive edit form for registration management
- Created new getAllVehicles method with schema detection
- Added ability for admins to change registration and payment status

### Fixed
- Fixed issue where "Edit Registration" menu option redirected to admin dashboard
- Improved database compatibility with dynamic schema detection
- Enhanced error handling for vehicle and registration operations

## [2.19.32] - 2025-06-21

### Fixed
- Fixed "Undefined array key 'category_id'" error when cancelling registrations
- Completely redesigned the registration update process to use dynamic query building
- Added support for partial updates with only the fields that need to be changed
- Improved error handling in registration update operations
- Enhanced logging for database operations

## [2.19.31] - 2025-06-20

### Fixed
- Fixed "Unknown column 'u.username' in 'field list'" error when viewing, editing, or deleting registrations
- Implemented advanced database schema detection to dynamically adapt queries to available columns
- Added multiple fallback mechanisms for registration data retrieval
- Enhanced error handling with graceful degradation for database operations

## [2.19.30] - 2025-06-19

### Fixed
- Fixed flickering and looping modals when clicking cancel/delete registration buttons
- Improved modal handling in user registrations page by using shared modals with dynamic content
- Enhanced JavaScript event handling for registration actions
- Optimized page performance by reducing the number of DOM elements

## [2.19.29] - 2025-06-18

### Fixed
- Fixed fatal error "Unknown column 'u.phone' in 'field list'" when viewing registration details
- Added dynamic SQL query building based on existing database columns
- Implemented robust error handling with fallback queries for registration data retrieval
- Added graceful handling for missing database columns

## [2.19.28] - 2025-06-18

### Fixed
- Fixed undefined property warnings in registration view page
- Fixed PHP 8.4.6 deprecation warnings related to null values in number_format()
- Enhanced registration data retrieval to include all necessary user and vehicle fields
- Added proper fallback values for missing registration data

## [2.19.27] - 2025-06-15

### Added
- Added "Registrations" button to user management page for viewing all registrations for a specific user
- Added dedicated user registrations page showing all registrations across all shows
- Added ability to filter user registrations by status and date (upcoming/past)
- Added ability to delete or cancel registrations directly from the user registrations page

## [2.19.26] - 2025-06-10

### Added
- Added ability for admins and coordinators to delete user show registrations
- Added delete buttons to registration management pages for both admin and coordinator roles
- Added confirmation modals to prevent accidental deletion of registrations
- Added registration_number column to registrations table if it doesn't exist
- Added max_entries column to show_categories table if it doesn't exist
- Added category statistics to registration management page
- Added registration summary statistics (total, paid, pending, revenue)

### Fixed
- Fixed PHP 8.4.6 deprecation warning by properly declaring registrationModel and vehicleModel properties in AdminController
- Fixed "Unknown column 'r.registration_number' in 'order clause'" error in RegistrationModel
- Fixed "Undefined variable $category_stats" warning in registrations.php view
- Fixed "Undefined variable $total_registrations" and related warnings in registrations.php
- Fixed deprecated warning about passing null to number_format() function
- Added fallback sorting for registrations when registration_number column doesn't exist
- Added fallback for max_entries column in category statistics
- Improved error handling in registration deletion process

## [2.19.25] - 2025-06-09

### Fixed
- Fixed fatal error in ShowController by adding missing isRegistrationOpen method to ShowModel
- Added proper error handling for date validation in isRegistrationOpen method

## [2.19.24] - 2025-06-08

### Fixed
- Fixed PHP 8.4.6 deprecation warning by properly declaring the systemFieldManager property in FormDesignerController
- Improved code compatibility with PHP 8.4.6 by preventing dynamic property creation

## [2.19.23] - 2025-06-07

### Fixed
- Fixed system fields protection in form designer to work with all template IDs
- Updated FormDesignerController to use SystemFieldManager for consistent system field identification
- Added proper logging for system field detection to aid in troubleshooting
- Ensured padlock icons appear for all system fields regardless of template type or ID
- Fixed form validation to only check for required system fields relevant to the current entity type
- Added confirmation dialog when saving forms with missing system fields instead of blocking save
- Fixed CSRF token validation to accept tokens from both headers and request body
- Improved error handling in form saving process with detailed logging
- Fixed PHP 8.4.6 deprecation warning by properly declaring the systemFieldManager property
- Added new getRequiredSystemFieldsArray method to SystemFieldManager to separate all system fields from required ones
- Updated JavaScript validation to only check for required system fields, not all system fields
- Implemented permanent fix for entity-specific field validation in form designer
- Fixed issue where vehicle fields were being validated in event forms
- Removed duplicate fetch call in form save function
- Added automatic database repair to fix incorrect entity_type values in system_fields table
- Enhanced SystemFieldManager to ensure proper entity type filtering
- Fixed edit_final.php to use required_system_fields instead of critical_fields for validation
- Updated FormDesignerController to pass required_system_fields to all form designer views
- Added client-side filtering of critical and required fields by entity type
- Fixed entity type detection in edit_final.php template
- Added debug information to form designer to aid in troubleshooting
- Implemented forced entity type detection based on template type
- Added client-side filtering of required fields in form submission handler
- Fixed entity type mapping to match database schema enum values
- Enhanced debug information to show actual template and entity types
- Fixed undefined variable errors in edit_final.php
- Added proper variable initialization to prevent PHP warnings
- Improved JavaScript entity type detection for more reliable field filtering
- Added fallback entity type detection based on template type
- Added template name-based entity type detection for better accuracy
- Added support for registration and user entity types
- Changed validation to warn instead of block when required fields are missing
- Added confirmation dialog for saving forms with missing recommended fields
- Enhanced debug information to show system fields and template name
- Added special handling for registration forms to bypass validation
- Added special handling for vehicle forms to bypass validation
- Simplified field validation logic for better compatibility

## [2.19.22] - 2025-06-06

### Fixed
- Fixed fatal error in UserController by adding missing getUserCompletedShows method to ShowModel
- Added proper error handling for awards and judging results tables in getUserCompletedShows
- Implemented table existence checks to prevent SQL errors when tables don't exist

## [2.19.21] - 2025-06-05

### Fixed
- Fixed issue with shows, registrations, and vehicles not using the default form templates assigned in /admin/defaultTemplates
- Modified EntityTemplateManager to correctly retrieve default templates from DefaultTemplateManager
- Updated AdminController to use EntityTemplateManager for template selection in editShow and addShow methods

## [2.19.20] - 2025-06-04

### Added
- Added entity selector dropdown to EntityTemplates page for easier entity selection
- Added AJAX functionality to load entities based on selected entity type
- Added "Revert to Default" button for easier template management

### Fixed
- Fixed fatal error in EntityTemplatesController by removing call to non-existent parent constructor
- Ensured proper inheritance from Controller base class
- Fixed SQL error in EntityTemplateManager by checking if columns exist before adding them
- Improved database column creation to avoid "Column already exists" errors

## [2.19.19] - 2025-06-04

### Added
- Added support for entity-specific templates
- Added template_id field to shows, vehicles, and registrations tables
- Implemented template override system for entity-specific templates
- Added database update script for adding template_id fields
- Enhanced template selection logic to check for entity-specific templates first

### Fixed
- Fixed "View does not exist" error when accessing /entityTemplates by extending the Controller base class
- Simplified EntityTemplatesController to use the parent class's view and model methods
- Removed redundant view loading code in EntityTemplatesController

## [2.19.18] - 2025-06-03

### Added
- Added Default Templates management system
- Created a dedicated admin interface for setting default templates
- Added support for entity-specific template types (event, vehicle, show)
- Implemented automatic database table creation for default templates
- Added DefaultTemplateManager model for handling template defaults
- Added menu links to access Default Templates in admin navigation

## [2.19.17] - 2025-06-02

### Added
- Restored missing dashboard method in AdminController
- Added settings method to AdminController
- Added getUserCount method to UserModel
- Added getShowCount method to ShowModel
- Added updateSettings method to SettingsModel

### Fixed
- Fixed ERR_TOO_MANY_REDIRECTS issue when accessing admin dashboard
- Fixed issue where clicking Admin Settings would redirect to dashboard
- Fixed duplicate APPROOT definition in AdminController constructor
- Fixed duplicate JudgingModel initialization in AdminController

## [2.19.16] - 2025-06-01

### Added
- Added new Default Templates management system
- Created a dedicated admin interface for setting default templates
- Added support for entity-specific template types (event, vehicle, show)
- Implemented automatic database table creation for default templates

### Fixed
- Fixed issue where custom templates weren't being used correctly
- Improved template selection logic to respect user preferences
- Enhanced template type handling for different entity types

## [2.19.15] - 2025-05-31

### Fixed
- Fixed issue where the system was creating duplicate templates instead of using existing ones
- Added template type priority system to properly find and use custom templates
- Enhanced AdminController to check for show-specific templates before using defaults
- Added support for 'event' type templates in the admin show editor
- Improved template selection logic to respect user-created templates

## [2.19.14] - 2025-05-30

### Fixed
- Fixed critical error in FormFieldSynchronizer when processing invalid templates
- Added robust null checking to prevent "Attempt to read property on false" errors
- Improved template validation in synchronizeTemplate method
- Added fallback for missing templates in synchronizeShowFields
- Enhanced error handling for template processing

## [2.19.13] - 2025-05-29

### Fixed
- Fixed critical issue where form designer was creating new templates instead of updating existing ones
- Added explicit template ID tracking to ensure templates are properly updated
- Enhanced form submission to include all necessary template data
- Improved error logging for template update operations
- Added safeguards to prevent duplicate template creation

## [2.19.12] - 2025-05-28

### Added
- Added integration with SystemFieldManager for proper system field identification
- Added SQL script to ensure system_fields table exists with default values
- Enhanced field cleanup process to use the system_fields database table

### Fixed
- Fixed issue with custom fields not being properly deleted when removed from templates
- Updated FormFieldManager to use SystemFieldManager for identifying system fields
- Fixed cleanupRemovedFieldData to properly identify system fields using SystemFieldManager
- Enhanced updateFieldMappings to use SystemFieldManager for system field detection
- Improved orphaned data cleanup to handle all custom fields correctly
- Fixed issue with template name being lost when editing templates in the form designer
- Added multiple safeguards to ensure template names are preserved during form submission
- Fixed form designer requiring system fields that aren't part of the current template type
- Implemented template-type-specific field requirements to prevent blocking template saves
- Added proper detection of template types to only enforce critical fields when necessary

## [2.19.11] - 2025-05-27

### Added
- Added enhanced FormFieldSynchronizer to ensure consistent field mappings
- Added dynamic field mapping system that works with all field types
- Created update_v2.19.11.sql script to fix database structure and ensure consistency
- Added improved error handling for all form field types
- Enhanced form submission processing to properly handle all field types
- Added support for new field types: signature, rating, and hidden fields
- Created debug_form_submission_helper.php for diagnosing and fixing form issues
- Added automatic cleanup of data and mappings when fields are removed from templates
- Enhanced FormDesignerController to ensure field mappings are updated when templates change

### Fixed
- Fixed issue with form fields not being properly saved to the database
- Fixed inconsistency between database_column and db_column values in field_mappings
- Fixed form field mappings to ensure proper data storage for custom fields
- Improved handling of empty fields in form submissions
- Enhanced field mapping synchronization during form template updates
- Fixed issue with field_mappings table missing required columns
- Fixed issue with data not being properly cleaned up when fields are removed from templates
- Added cleanup_field_data.php tool to fix orphaned mappings and data
- Fixed SQL syntax issues in field cleanup operations
- Added forceCleanupField method to ensure complete data removal
- Enhanced field removal detection in FormDesignerModel and FormDesignerController
- Fixed deprecated warnings related to passing null values to preg_match()
- Improved null value handling throughout the FormFieldManager class
- Added cleanupInvalidFieldMappings method to remove mappings with empty field IDs
- Fixed "Cleanup All Orphaned Mappings" functionality to handle empty field IDs
- Added cleanupCustomFieldColumn method to properly clean up custom_field_X columns
- Fixed isCustomField method to correctly identify custom field columns
- Enhanced cleanup_field_data.php to display and clean up custom field columns
- Fixed field removal in updateFieldMappings to use forceCleanupField for more reliable cleanup
- Added cleanupFieldById method for direct field cleanup
- Enhanced field cleanup to handle both field IDs and column names
- Updated FormDesignerController and FormDesignerModel to use cleanupFieldById for more reliable cleanup
- Improved field removal detection in the form_designer/edit/ workflow
- Fixed SQL parameter binding issue in mapFieldToColumn method using VALUES() syntax for duplicate key updates
- Improved emergency fallback mechanism to prevent field mapping conflicts
- Added missing cleanupFieldById method to properly remove field mappings when fields are deleted from templates
- Enhanced field mapping deletion to check both template_field_id and form_field_id
- Added detailed logging for field removal and cleanup operations
- Added forceDeleteFieldMappingById method for direct mapping deletion by ID
- Created diagnostic and cleanup scripts for field mappings
- Removed EmergencyFormFieldManager to simplify codebase and prevent conflicts
- Updated all code to use only FormFieldManager for consistent behavior
- Enhanced fix_field_mappings.php to detect and clean up orphaned mappings
- Added detailed logging to field cleanup methods for better debugging
- Fixed issue with field_mappings table detection in cleanup methods
- Fixed critical bug in updateFieldMappings method that was using deleteFieldMapping instead of cleanupFieldById
- Enhanced debug_field_mappings.php with direct testing of updateFieldMappings and cleanupFieldById methods
- Added detailed logging of database operations in field cleanup methods
- Improved security of debug tools with password protection and limited data exposure
- Added warnings and cleanup instructions to diagnostic scripts
- Enhanced system field detection to prevent accidental modification of critical fields
- Filtered out system fields from diagnostic displays for better security
- Improved debug_field_mappings.php with template selection and field dropdowns
- Added one-click template ID selection to simplify debugging
- Fixed PDO parameter binding issues in debug_field_mappings.php
- Added error handling for database queries in diagnostic tools
- Fixed critical bug in FormFieldManager preventing field mappings from being deleted
- Improved database query handling in cleanupFieldById and deleteFieldMapping methods
- Added force delete option to debug_field_mappings.php for stubborn field mappings
- Enhanced error logging and debugging for field mapping operations
- Fixed return value handling in cleanupFieldById to report success when mappings are deleted
- Added detailed logging for custom field detection and cleanup operations
- Improved error handling in cleanupRemovedFieldData to continue despite non-critical errors
- Added direct table existence check for custom_field_values when CustomFieldValuesModel is not available
- Enhanced updateFieldMappings method to ensure complete field cleanup when fields are removed from forms
- Added direct database operations in updateFieldMappings to guarantee mapping deletion
- Improved debug_field_mappings.php with clear distinction between system and custom fields and removed password requirement
- Added SystemFieldManager class for centralized management of system fields
- Created template-based admin interface for managing system fields
- Added database table for storing system field definitions
- Updated form designer to use SystemFieldManager for identifying protected fields
- Added System Fields menu item to admin settings sidebar
- Implemented checkbox-based system field designation by template
- Simplified system fields management by auto-detecting entity types from template types
- Added FormTemplateModel for handling form templates
- Added automatic table creation for system_fields and form_templates
- Fixed CSRF token validation in system fields management form
- Fixed form designer to properly identify and protect system fields
- Improved error handling with fallback to default system fields
- Improved SQL compatibility with MySQL 8.0.41

## [2.19.10] - 2025-05-26

### Added
- Added support for both form_field_id and template_field_id column names in field_mappings table
- Added support for both db_column and database_column naming in field_mappings table
- Added automatic column detection to determine which column names are available
- Created update_v2.19.10.sql script to fix field_mappings table structure
- Enhanced FormFieldManager to handle multiple column naming conventions

### Fixed
- Fixed "Unknown column 'form_field_id' in 'where clause'" error in debug_form_submission.php
- Fixed field mapping queries to work with different column naming conventions
- Updated debug_form_submission_fixed.php to handle different column names
- Improved error handling in field mapping operations
- Enhanced database queries to be more resilient to schema differences
- Fixed inconsistency between FormFieldManager.php and SQL scripts in column naming

## [2.19.9] - 2025-05-25

### Added
- Added automatic cleanup of orphaned custom field values
- Added cleanupOrphanedCustomFieldValues method to FormFieldManager
- Enhanced field_mapping_diagnostic.php to identify system fields
- Added special handling for system fields like listing_fee
- Created fix_listing_fee.php script to properly mark listing_fee as a system field

### Fixed
- Fixed SQL syntax error with prepared statements in LIKE clauses
- Fixed issue where listing_fee was incorrectly treated as a custom field
- Fixed cleanup process for system fields to prevent data loss
- Improved error handling in database operations with LIKE clauses
- Fixed orphaned custom field values remaining after field removal

## [2.19.8] - 2025-05-24

### Added
- Added automatic cleanup of removed custom fields data
- Added cleanupRemovedFieldData method to FormFieldManager to delete data for removed fields
- Enhanced deleteFieldMapping to automatically clean up associated data
- Improved form editor to properly manage the complete lifecycle of custom fields
- Added diagnostic tools for troubleshooting field mapping issues
- Added explicit detection of removed fields in FormDesignerModel

### Fixed
- Fixed issue where removed form fields would leave orphaned data in the database
- Fixed data persistence in custom_field_X columns after fields were removed from forms
- Fixed data persistence in custom_field_values table after fields were removed from forms
- Fixed issue where field mappings weren't properly deleted when fields were removed from forms
- Fixed error handling in FormFieldManager to properly handle null or missing values
- Fixed diagnostic tools to handle null values and prevent PHP warnings
- Improved error handling in field mapping operations to prevent PHP errors

## [2.19.7] - 2025-05-23

### Fixed
- Fixed SQL syntax error with BEGIN NOT ATOMIC statement for MySQL 8.0.41 compatibility
- Fixed duplicate method declaration in ShowModel.php (detectFieldType)
- Fixed deprecation warning about dynamic property creation in ShowModel.php
- Fixed missing database configuration in fix_field_mappings_v2.php
- Added missing property declaration for $formFieldManager in ShowModel class
- Added require_once for config.php in fix_field_mappings_v2.php
- Updated APP_VERSION to 2.19.7 in config.php
- Merged the two implementations of detectFieldType to include all field type detection logic
- Created new 2.19.7_fix_field_mappings.sql script with improved MySQL 8.0.41 compatibility
- Removed problematic foreign key constraint that was causing column type incompatibility errors
- Created new fix_field_mappings_v2.php script with better error handling
- Improved error handling in database operations
- Fixed path to Database.php in fix_field_mappings.php script
- Updated SQL script to check if indexes exist before adding them
- Added INSERT IGNORE for default mappings to prevent duplicate key errors

## [2.19.6] - 2024-11-26

### Added
- Added DynamicFormFieldManager class for fully dynamic field mapping without hardcoded values
- Added automatic field type detection based on field name patterns and value analysis
- Added database update script to create field_mappings table for storing dynamic mappings
- Added support for all HTML5 input types with proper sanitization
- Added automatic mapping discovery between form fields and database columns
- Made saveMapping method public to allow external scripts to create mappings
- Updated fix_field_mappings.php to use DynamicFormFieldManager for dynamic mapping

### Fixed
- Fixed issue with custom field mapping and updating not saving properly
- Enhanced CustomFieldRetriever to use DynamicFormFieldManager for better field mapping
- Removed all hardcoded field mappings in favor of dynamic discovery
- Improved ShowModel to use DynamicFormFieldManager for form data processing
- Enhanced AdminController to process form fields dynamically without hardcoded references
- Added detailed logging for custom field processing to aid in troubleshooting
- Fixed SQL errors in field_mappings table creation with improved error handling

## [2.19.5] - 2024-11-25

### Fixed
- Fixed "Call to undefined method ShowModel::getShowCategories()" error in AdminController
- Added getShowCategories and getJudgingMetrics methods to ShowModel
- Added automatic table creation for show_categories and judging_metrics tables
- Added methods for adding categories and judging metrics to shows
- Improved error handling for missing tables and methods

## [2.19.4] - 2024-11-25

### Fixed
- Fixed "Call to private method EmergencyFormFieldManager::detectFieldType()" error
- Replaced all calls to EmergencyFormFieldManager::detectFieldType with ShowModel::detectFieldType
- Updated version number and documentation

## [2.19.3] - 2024-11-25

### Fixed
- Fixed fatal error in ShowModel when calling private method detectFieldType from EmergencyFormFieldManager
- Added local implementation of detectFieldType method in ShowModel class
- Replaced calls to EmergencyFormFieldManager::detectFieldType with ShowModel::detectFieldType
- Improved field type detection with more comprehensive patterns

## [2.19.2] - 2024-11-25

### Fixed
- Fixed fatal error in ShowModel::updateShow method due to undefined $systemFields variable
- Fixed all occurrences of local variables vs. class properties in ShowModel
- Replaced all instances of $standardFields and $systemFields with $this->standardFields and $this->systemFields
- Comprehensive fix for all variable scope issues in ShowModel.php

## [2.19.1] - 2024-11-25

### Fixed
- Fixed PHP 8.4 deprecation warning about dynamic property creation in ShowModel
- Properly declared $emergencyFormFieldManager property in ShowModel class

## [2.19.0] - 2024-11-24

### Added
- Implemented unlimited custom fields support with new database structure
- Added CustomFieldValuesModel for managing custom field values
- Created migration script to move data from legacy custom_field_X columns to new table
- Added backward compatibility to ensure smooth transition

### Fixed
- Implemented comprehensive solution for all field types in the form builder
- Enhanced EmergencyFormFieldManager to detect and process all field types automatically
- Added support for richtext editors, file uploads, and all standard HTML5 input types
- Improved field type detection based on field name patterns
- Added specialized sanitization for each field type
- Enhanced form processing to handle empty fields correctly
- Removed hardcoded field references for better maintainability
- Improved bidirectional mapping for all custom fields
- Added detailed logging for all field types
- Fixed "Undefined variable $systemFields" error in ShowModel.php
- Updated ShowModel to use class properties instead of local variables
- Added proper initialization of EmergencyFormFieldManager in ShowModel constructor

## [2.18.8] - 2024-11-24

### Fixed
- Fixed issue with textarea field not updating when editing shows
- Added hidden marker field to ensure textarea fields are always included in form submissions
- Enhanced form processing to properly handle empty textarea fields
- Added special handling for field_1747938252386 to ensure it maps to custom_field_3
- Improved debug logging throughout the form processing pipeline
- Fixed bidirectional mapping between form fields and database columns
- Ensured consistent handling of textarea fields across the application
- Added proper HTML escaping for textarea content in forms

## [2.18.7] - 2024-11-24

### Fixed
- Fixed issue with textarea field (field_1747938252386) not saving to custom_field_3
- Enhanced HTML sanitization to preserve more content in textarea fields
- Added special handling for textarea fields to prevent data loss
- Added detailed logging to help diagnose form processing issues
- Created SQL script to ensure proper field mapping and column types

## [2.18.6] - 2024-11-23

### Fixed
- Fixed issue with textarea fields and other field types not saving properly
- Fixed database column type mismatch for custom_field_3 (changed from DATE to TEXT)
- Added comprehensive field type detection and handling for all form field types
- Implemented specialized sanitization for different field types (textarea, checkbox, radio, etc.)
- Enhanced HTML content handling in textarea fields with proper sanitization
- Added support for checkbox and radio button fields with proper boolean conversion
- Improved field type detection based on field name patterns
- Added database lookup for field types to ensure consistent handling
- Added fix_custom_field_types.php tool to check and fix column types

## [2.18.5] - 2024-11-22

### Fixed
- Created dynamic field mapping system with database storage
- Added field_mappings table to store form field to database column mappings
- Improved form field type detection for proper sanitization
- Enhanced sanitizeInput method to dynamically handle different field types
- Added special handling for HTML content in textarea fields
- Removed hardcoded field mappings in favor of dynamic discovery
- Added fallback mechanisms for field mapping discovery
- Ensured SQL compatibility with phpMyAdmin and MySQL 8.0.41
- Used standard SQL syntax without backticks for better compatibility
- Fixed SQL table creation to avoid column reference errors
- Improved database schema creation with separate ALTER TABLE statements

## [2.18.4] - 2024-11-22

### Fixed
- Fixed issue with custom fields not being displayed in the edit show form
- Added CustomFieldRetriever class to map database columns back to form fields
- Improved handling of custom fields in AdminController
- Enhanced custom field retrieval to display values in the edit form
- Added automatic mapping between custom_field_X columns and form fields
- Fixed issue with form fields not showing their saved values
- Added hardcoded mappings for known custom fields
- Added simple debug view for troubleshooting form field issues
- Added simpleDebug method to AdminController for easier debugging
- Added multiple sources for loading field mappings
- Added detailed logging for custom field mapping

## [2.18.3] - 2024-11-17

### Fixed
- Fixed critical issue where custom fields from form templates weren't being saved to the database
- Fixed issue with editShow and addShow not saving custom field data
- Added comprehensive database update script to ensure all form template fields have corresponding database columns
- Enhanced error handling in FormDesignerModel when adding fields to the database
- Improved ShowModel to better handle custom fields during create and update operations
- Added form_template_diagnostic.php tool to help diagnose and fix form template issues
- Added detailed logging for database operations to help troubleshoot issues
- Fixed SQL error #1054 - "Unknown column 'type' in 'field list'" in the database update script
- Created a more robust update script that works with different database structures
- Fixed error in updateShow method when 'type' column doesn't exist
- Added fallback mechanisms for handling missing database columns
- Enhanced error handling to prevent SQL errors from breaking the application
- Added graceful degradation when database structure differs from expected
- Created EmergencyFormFieldManager that works without relying on database tables
- Added add_custom_fields.php script to easily add custom field columns to the shows table

### Added
- Added FormFieldManager class to automatically handle all custom field operations
- Added automatic mapping of form fields to predefined custom_field_X columns
- Added intelligent field type detection and column selection based on field type
- Added dynamic field mapping that works without manual configuration
- Added automatic database column creation for custom fields
- Added support for handling field additions, updates, and deletions
- Added column_update_log table to track database column additions
- Added system_logs entries for database changes
- Added better error reporting for database operations
- Added support for handling missing columns gracefully
- Added field_mappings table to store mappings between template fields and database columns
- Added support for using predefined custom_field_X columns for custom form fields
- Added simple database update script to add field_type column to field_mappings table
- Added robust error recovery mechanisms for database operations

## [2.18.2] - 2024-11-16

### Added
- Added automatic database field creation when new fields are added to form templates
- Added system_logs table to track database changes
- Added intelligent SQL data type mapping based on form field types
- Added dynamic data saving and retrieval for custom form fields
- Added support for custom fields in show creation and editing

### Fixed
- Fixed issue where custom fields added through form designer weren't being saved to database
- Fixed issue where custom field values weren't being retrieved when editing shows
- Fixed bug in add_with_template.php where text input fields were incorrectly rendered as number fields
- Added support for email, tel, and url field types in edit_with_template.php

### Changed
- Enhanced form template designer to maintain database schema consistency
- Improved form field handling to ensure data persistence
- Updated ShowModel to dynamically handle all database columns

## [2.18.1] - 2024-11-16

### Added
- Added automatic disabling of the Registration Fee field when "This is a free show" is checked
- Added helpful tooltips to explain the relationship between the free show checkbox and registration fee

### Fixed
- Fixed section divider display to match the design requirements
- Added support for custom HTML content in form templates
- Improved handling of placeholder and help text in section dividers
- Fixed layout to properly respect template column widths and row organization
- Improved field grouping to match the add_with_template.php implementation
- Enhanced field value handling with better fallback options

### Changed
- Removed "Edit Registration Form" button from the top navigation bar
- Simplified the top navigation to focus on essential actions

## [2.18.0] - 2024-11-15

### Added
- Implemented automatic template system for admin/editShow page
- Added support for using the "Default Show Admin Form" template from form designer
- Enhanced form rendering with dynamic field types, widths, and help text

### Fixed
- Added robust error handling for form templates with missing field attributes
- Fixed "Undefined array key" warnings in template rendering
- Improved field rendering to handle incomplete or malformed template data
- Fixed compatibility issues between 'id' and 'name' field attributes
- Added automatic recovery for empty or invalid form templates
- Added debug information for administrators to troubleshoot template issues

## [2.17.0] - 2024-11-10

### Changed
- Updated the admin/editShow page to use the same template as admin/addShow for consistency
- Added "Next Steps" section to the edit show page with quick access to image management, form customization, and categories
- Improved user experience with consistent UI between add and edit show pages
- Enhanced the edit show page with better visual organization and information display
- Added auto-update functionality for end date when start date changes

## [2.16.0] - 2024-11-09

### Fixed
- Fixed "Undefined property: UserController::$db" error in vehicle image management
- Added $db property to UserController class and initialized it in the constructor
- Removed redundant database initialization in setPrimaryImage and deleteImage methods
- Improved database connection handling in UserController

## [2.15.0] - 2024-11-08

### Fixed
- Fixed "A scalar of type 'null' used as an array" error in user dashboard
- Added proper null checks for vehicles, registrations, and upcoming_shows variables
- Enhanced error handling in VehicleModel, RegistrationModel, and ShowModel
- Improved Database class to ensure resultSet always returns an array
- Added try-catch blocks to prevent database query errors from breaking the application

## [2.14.0] - 2024-11-07

### Fixed
- Fixed "Too many arguments to function view(). 3 provided, but 2 accepted" error in ImageEditorController
- Removed third parameter from view() method call and used the data array to pass AJAX flag

## [2.13.0] - 2024-11-06

### Fixed
- Fixed "Unreachable code detected" error in form_designer/preview.php by properly formatting HTML input tags
- Improved HTML structure in form preview to ensure all code is reachable
- Fixed input tag closing format to prevent PHP parsing issues

## [2.12.0] - 2024-11-05

### Fixed
- Fixed "Call to unknown function: 'validateCsrfToken'" error by replacing with the correct method $this->verifyCsrfToken()
- Updated CSRF token validation in FormDesignerController and Payments controller

## [2.11.0] - 2024-11-04

### Fixed
- Fixed "Call to unknown method: AdminFieldTypesController::setFlash()" error by updating method calls to use setFlashMessage() instead
- Standardized flash message method calls across controllers

## [2.10.0] - 2024-11-03

### Fixed
- Fixed "Cannot redeclare method ShowModel::updateShowFeaturedImage" error by removing duplicate method declaration
- Improved code structure in ShowModel.php

## [2.9.0] - 2024-11-02

### Added
- Added featured image support for shows in the admin show creation form
- Implemented custom image field in the admin show creation page
- Added image selector modal for selecting featured images
- Added database support for storing featured image IDs in the shows table
- Created database update script to add featured_image_id column to shows table
- Added getAllImages and getImagesByEntityType methods to ImageEditorModel
- Added updateShowFeaturedImage method to ShowModel

### Fixed
- Fixed error in ImageEditorController when calling undefined getAllImages() method
- Updated form_selector method to use getRecentImages() instead of getAllImages()
- Improved error handling in image selection process
- Fixed database update script to handle different settings table structures (setting_key vs name column)
- Created improved version of the database update script (add_featured_image_to_shows_v4.sql) that handles various column naming conventions
- Fixed issue with settings table update when column names vary (setting_value vs value)
- Fixed SQL syntax error in database update script by using a more compatible approach for MySQL

## [2.8.0] - 2024-11-01

### Fixed
- Fixed issue in form designer preview where separator field labels were incorrectly displayed
- Removed text labels from separator fields in preview mode to match design requirements
- Modified separator fields to only accept full width setting to prevent layout issues
- Added visual indication that separator fields are always full width in the form designer
- Applied the same separator field label fix to the admin show creation page
- Fixed checkbox fields in form preview to prevent duplicate labels by hiding the field label
- Ensured consistent rendering of separator fields across all parts of the application
- Removed unused test JavaScript files to clean up the codebase

### Removed
- Removed the preview feature from the form templates page
- Disabled the preview functionality in the FormDesignerController

## [2.7.0] - 2024-10-31

### Added
- Integrated form templates with the admin show creation page
- Added ability to use the "Default Show Admin Form" template when creating shows
- Created a new template-based show creation interface
- Added a link to edit the form template directly from the show creation page
- Added support for rendering all field types from templates
- Added admin page to manage form field types (enable/disable)
- Added links to the field type manager in admin settings and form builder
- Added detailed descriptions for each field type

### Fixed
- Fixed issue where the Default Show Admin Form template wasn't being used
- Improved form field rendering from templates
- Enhanced error handling for form templates
- Added proper validation for template-based forms
- Added confirmation when disabling commonly used field types

## [2.6.0] - 2024-10-31

### Added
- Added live form preview feature to the form designer
- Implemented toggle between design view and preview mode
- Added support for previewing all field types (text, select, checkbox, radio, etc.)
- Preview shows fields with proper Bootstrap styling and layout
- Added admin page to manage available field types
- Enhanced image upload field with custom image manager integration
- Added ability to disable specific field types (HTML, hidden, rating stars, range slider)
- Integrated image upload field with the existing image manager
- Added "Add Image" and "Manage Images" buttons to image upload fields
- Created a dedicated image selector for form fields
- Added support for storing and displaying selected images

### Fixed
- Fixed width selection in form designer not working properly
- Added debug logging to help troubleshoot form designer issues
- Removed unused functions to improve code clarity
- Ensured width changes are properly applied to form fields
- Fixed error handling for missing model dependencies
- Improved error handling for image selection and management

## [2.5.0] - 2024-10-30

### Fixed
- Fixed form designer to allow multiple fields in the same row based on width settings
- Implemented a simpler, more reliable layout system for form fields
- Added proper CSS support for field width classes (full, 1/2, 1/3, 1/4)
- Fixed issue where fields could only be placed one per row regardless of width setting
- Improved visual appearance of fields with consistent styling
- Enhanced mobile responsiveness for the form designer

## [2.4.0] - 2024-10-28

### Added
- Added form builder functionality for admin show creation/editing forms
- Created admin show form template with customizable fields
- Added visual form designer for admin show forms
- Implemented drag-and-drop interface for form field arrangement
- Added field property editor for customizing form fields
- Created form preview functionality for testing form designs
- Added admin-only access to form designer for show forms
- Added protection for critical system fields to prevent breaking functionality
- Added visual indicators for system fields that cannot be modified

### Changed
- Updated admin shows page with form designer button
- Enhanced form template management for better user experience
- Improved form field handling with better validation
- Restricted editing capabilities for system fields while allowing rearrangement

## [2.3.0] - 2024-10-25

### Added
- Completely redesigned the user registration details page with a modern, intuitive layout
- Added tabbed interface for better organization of registration information
- Implemented responsive design for improved mobile experience
- Added visual timeline for registration status tracking
- Enhanced vehicle information display with better formatting
- Improved payment information presentation
- Added dynamic tab handling with URL hash support for direct linking

### Fixed
- Fixed multiple undefined property errors in registration view page
- Updated property references to use null coalescing operator for better error handling
- Fixed date handling to use start_date instead of date property
- Fixed payment information display to use fee instead of registration_fee
- Fixed registration date display to use created_at instead of registration_date
- Added proper error handling for missing or null values throughout the view

## [2.2.0] - 2024-10-20

### Added
- Added registration edit functionality for users
- Added edit registration form view
- Added ability to change vehicle and category for existing registrations
- Added payment status handling for category changes with different fees

### Fixed
- Fixed undefined property errors in user dashboard
- Fixed incorrect property names in dashboard view (vehicle_year, vehicle_make, vehicle_model)
- Updated dashboard to use payment_status instead of status for registration status display
- Fixed routing issues with registration edit functionality
- Fixed undefined property show_date errors by using start_date from the shows table
- Updated all references to show_date in views and controllers to use start_date
- Added checked_in column to registrations table
- Fixed undefined property show_location and checked_in errors in registrations view
- Updated RegistrationModel to handle cases where checked_in column might not exist yet
- Simplified database update script to avoid permission issues
- Used try-catch approach to handle missing columns without requiring special permissions

## [2.1.0] - 2024-10-15

### Added
- Added advanced image viewer with zooming and moving capabilities
- Implemented AJAX popout for thumbnail images across the site
- Added image navigation controls for gallery viewing
- Added zoom controls with mouse wheel and button support
- Added image dragging capability for panning when zoomed in
- Enhanced user experience with smooth transitions and animations
- Improved mobile responsiveness for the image viewer
- Added test page for image viewer functionality
- Added banner_image column to shows table for banner images
- Added primary image display for shows and vehicles

### Changed
- Replaced lightbox library with custom image viewer solution
- Updated all thumbnail displays to use the new image viewer
- Improved thumbnail display with consistent styling
- Added support for setting show banner images
- Removed redundant "Set as Banner" option from image editor
- Improved dashboard stats layout and alignment

### Fixed
- Fixed image path handling to ensure images load correctly
- Added proper error handling for image loading failures
- Improved initialization of image viewer on page load
- Fixed CSS to ensure image viewer displays above all other elements
- Added BASE_URL JavaScript variable for consistent path handling
- Added missing updateShowBannerImage method to ShowModel
- Fixed dashboard stats CSS for better alignment and responsiveness
- Fixed icon sizing in dashboard stats
- Added specific 'compact-stat' class to dashboard stat boxes
- Created targeted CSS rules with !important flags for reliable styling
- Reduced padding to 12px and optimized for mobile displays
- Decreased font sizes for better space utilization
- Enhanced responsive media queries for small and medium screens
- Fixed CSS path in header to correctly load styles
- Added error checking for undefined properties in user dashboard
- Fixed duplicate footer inclusion in user dashboard

## [2.0.0] - 2024-10-01

### Added
- Added show images gallery to public show view
- Added getShowImages method to ImageEditorModel
- Enhanced show pages to display uploaded images
- Improved user experience with visual content for shows

### Fixed
- Fixed "Undefined property: stdClass::$is_published" error in image_editor/show.php
- Updated status display to use the correct property
- Fixed SQL error "#1054 - Unknown column 'type'" in database update scripts
- Updated column name from 'type' to 'log_type' in system_logs table queries

## [1.9.9] - 2024-09-30

### Changed
- Removed database tools section as it's no longer needed
- Removed DatabaseModel.php and related views
- Simplified admin settings interface by removing database tools option
- Improved code organization by removing unused functionality

### Fixed
- Fixed issue in admin dashboard where clicking on upcoming shows led to a non-existent page
- Updated links to properly direct to the show edit page

## [1.9.8] - 2024-09-28

### Fixed
- Fixed "Call to undefined method SettingsModel::setSetting()" error in AdminController
- Added setSetting method to SettingsModel as an alias for saveSetting
- Created database update script to ensure system_settings table exists
- Added default image settings for new installations
- Improved error handling in settings management
- Fixed database update scripts to work with both old and new settings table structures
- Enhanced SettingsModel to handle both settings and system_settings tables
- Fixed "#1054 - Unknown column 'setting_key' in 'where clause'" error in database updates
- Fixed "#1146 - Table 'system_logs' doesn't exist" error by creating the table before using it
- Fixed "#1054 - Unknown column 'group' in 'field list'" error by using a more robust approach
- Enhanced SettingsModel to automatically detect and use the correct column names
- Added multiple scripts for database table creation and fixes:
  - direct_create_system_logs.php for system_logs table
  - create_tables.php for all required tables
  - create_system_logs_table.php for a minimal system_logs table creation
  - fix_settings_table.php for fixing settings table column issues
  - fix_settings_table.sql for SQL-based settings table fixes
  - db_update_combined.sql for a combined SQL update

## [1.9.7] - 2024-09-28

### Fixed
- Created auth_helper.php file to fix "Call to unknown function: 'isLoggedIn'" error
- Added helper functions for authentication (isLoggedIn, isAdmin, hasRole, getCurrentUserId, getCurrentUserRole)
- Updated index.php to include the new auth_helper.php file
- Improved authentication system with better helper function organization

## [1.9.6] - 2024-09-25

### Fixed
- Created missing flash_message.php file in views/includes directory
- Fixed "Failed to open stream: No such file or directory" error in imageSettings.php
- Implemented comprehensive flash message display component
- Added support for different message types (success, error, warning, info)
- Enhanced error handling in admin settings pages
- Added missing flash() function to session_helper.php to fix "call to unknown function: 'flash'" warnings
- Ensured backward compatibility with existing code that uses the flash() function

## [1.9.5] - 2024-09-22

### Fixed
- Fixed image caching issues across all image editor pages (crop, resize, rotate, filter, text, draw, optimize)
- Fixed "draw on image" feature to properly display the entire image without cropping
- Fixed image deletion redirect to return to the previous page instead of the home page
- Fixed image upload redirect to return to the previous page instead of the home page
- Added cache-busting parameters to all image URLs to prevent displaying outdated images
- Improved canvas scaling in the drawing tool to match the image dimensions
- Enhanced image upload with loading indicator for better user feedback

### Improved
- Added loading indicator during image upload to provide better user feedback
- Enhanced image upload page with options to upload another image or return to browser
- Added success/error messages for image upload operations
- Improved navigation with dynamic "Back" button that returns to the previous page

## [1.9.4] - 2024-09-21

### Added
- Added Database Tools page in admin settings for database management
- Created direct_create_tables.php script for easy database setup
- Created verify_tables.php script to check database table status
- Created add_sample_registrations.php script to add sample registration data
- Added ability to create users and registrations tables from admin interface

### Fixed
- Fixed deprecated imagepolygon() and imagefilledpolygon() functions for PHP 8.4.6 compatibility
- Fixed "Users and registrations tables not found" error by providing tools to create required tables
- Improved error handling in database operations
- Fixed table existence checking in ShowModel

## [1.9.3] - 2024-09-07

### Added
- Added imageLibrary and imageSettings methods to AdminController
- Created imageSettings.php view for configuring image-related settings
- Added scripts to create test data for users and registrations tables
- Added support for batch image optimization in ImageEditorController

### Fixed
- Fixed form designer edit button issue by preventing form submission
- Fixed image editor upload functionality to work without parameters
- Updated all image editor methods to handle missing parameters gracefully
- Added proper error handling for image deletion and optimization
- Fixed redirect issues in FormDesignerController

## [1.9.2] - 2024-09-06

### Fixed
- Fixed SQL error in payment settings page by updating column references in PaymentModel
- Updated PaymentModel to use correct column names (setting_key, setting_value) instead of (name, value)
- Improved consistency in admin settings sidebar across all system settings pages
- Added payment methods link to the admin settings sidebar for easier navigation
- Fixed application version display in general settings page

## [1.9.1] - 2024-09-05

### Added
- Completed payment method integration with CashApp and Venmo:
  - Added payments/cashapp.php for CashApp payment processing
  - Added payments/venmo.php for Venmo payment processing
- Created comprehensive Payment controller to handle all payment methods
- Added Payment model for database interactions
- Enhanced payment workflow with improved user experience
- Implemented manual payment verification for CashApp and Venmo

## [1.9.0] - 2024-09-04

### Fixed
- Created missing authentication views:
  - Added auth/forgot_password.php for password reset requests
  - Added auth/reset_password.php for setting new passwords
- Enhanced password recovery workflow with improved user interface
- Improved error handling in authentication processes
- Ensured all authentication-related views are consistent with the application design

## [1.8.0] - 2024-09-03

### Fixed
- Created missing payment views:
  - Added payments/paypal.php for PayPal payment processing
  - Added payments/paypal_show.php for PayPal show listing payments
  - Added admin/payments/index.php for admin payment management
- Enhanced payment processing with improved user interface
- Improved payment method selection and instructions
- Added comprehensive payment history view
- Ensured all payment-related views are consistent with the application design

## [1.7.9] - 2024-09-02

### Fixed
- Created all missing view files in the judge module:
  - Added judge/dashboard.php for judge dashboard
  - Added judge/show.php for viewing show details as a judge
  - Added judge/judge.php for judging vehicles
  - Added judge/view_scores.php for viewing judging scores
- Created all missing view files in the coordinator module:
  - Added coordinator/dashboard.php for coordinator dashboard
  - Added coordinator/show.php for viewing show details as a coordinator
  - Added coordinator/categories/index.php for managing show categories
  - Added coordinator/categories/add.php for adding new categories
  - Added coordinator/categories/edit.php for editing existing categories
- Improved navigation between related views
- Enhanced user experience with consistent UI across all modules

## [1.7.8] - 2024-09-01

### Fixed
- Created all missing view files in the form_designer module:
  - Added form_designer/create.php for creating new form templates
  - Added form_designer/delete.php for deleting form templates
  - Added form_designer/design_vehicle_form.php for designing vehicle registration forms
  - Added form_designer/preview.php for previewing form templates
- Enhanced form designer with live preview functionality
- Added sample data generation for form previews
- Implemented section headings in form templates
- Added comprehensive validation for form fields

## [1.7.7] - 2024-08-31

### Fixed
- Fixed "The requested view 'form_designer/edit' could not be found" error by creating the missing view file
- Added comprehensive form template editor with field management capabilities
- Implemented drag-and-drop functionality for form field reordering
- Added field type selection and configuration options for form templates

## [1.7.6] - 2024-08-30

### Fixed
- Fixed "Call to unknown method: AdminController::setFlashMessage()" error by adding setFlashMessage method to base Controller class
- Added proper integration between Controller class and session_helper.php functions
- Improved error handling for flash messages across the application
- Fixed inconsistent URLs in user dashboard for show details links
- Updated social media links in footer to use settings from database
- Fixed non-existent reports link in admin dashboard
- Improved navigation consistency across the application

## [1.7.5] - 2024-08-28

### Fixed
- Fixed "Class 'ShowModel' not found" error in form_designer/templates.php by adding proper model inclusion
- Fixed "Class 'VehicleModel' not found" error in form_designer/templates.php by adding proper model inclusion
- Improved model loading in views to ensure proper class inclusion
- Enhanced error handling for model instantiation in views

## [1.7.4] - 2024-08-25

### Added
- Added accessible navigation links to the Visual Form Builder
- Added accessible navigation links to the Image Editor
- Created Image Manager page in admin settings
- Implemented browse functionality for the Image Editor
- Added Form Builder link to admin dashboard quick actions
- Added Image Manager link to admin dashboard quick actions
- Enhanced admin settings sidebar with Form Builder and Image Manager links
- Integrated Image Editor with vehicle management
- Created dedicated vehicle image editor page
- Added ability to edit vehicle images using the new Image Editor
- Added "Use Image Editor" button to vehicle images page
- Updated vehicle add page with information about the Image Editor
- Integrated Image Editor with show management
- Created dedicated show image editor page
- Added ability to edit show images using the new Image Editor
- Added "Manage Images" button to show edit page
- Added Visual Form Builder integration for show registration forms
- Created dedicated show form editor with live preview
- Added "Edit Registration Form" button to show edit page
- Updated show add page with information about the Image Editor and Form Builder
- Updated documentation to include new features

### Fixed
- Fixed issue where Visual Form Builder and Image Editor were not accessible from the UI
- Improved navigation between admin pages and new features
- Enhanced user experience with better feature discoverability
- Improved vehicle image management with advanced editing capabilities
- Improved show management with integrated image editing and form building capabilities
- Fixed fatal error in AdminController by adding missing generateCsrfToken method
- Fixed duplicate setPrimary method in ImageEditorController by renaming the second one to setPrimaryById
- Fixed fatal error in ImageEditorController by adding missing generateCsrfToken method
- Fixed fatal error in FormDesignerController by adding missing generateCsrfToken method
- Fixed fatal error in UserController by adding missing generateCsrfToken method
- Fixed undefined property warning in vehicle images view by using created_at instead of uploaded_at
- Fixed fatal error in ImageEditorController by adding missing updateVehiclePrimaryImage method to VehicleModel
- Fixed z-index issue where vehicle images dropdown menu was appearing behind batch actions box

## [1.7.3] - 2024-08-22

### Fixed
- Fixed "Call to undefined method LayoutEditorController::generateCsrfToken()" error
- Added missing generateCsrfToken method to LayoutEditorController
- Improved error handling in layout editor functionality
- Fixed CSRF token generation in visual editor pages

## [1.7.2] - 2024-08-21

### Fixed
- Fixed "Table 'rowaneli62_events.layouts' doesn't exist" error when accessing header and footer editors
- Added automatic layouts table creation in LayoutEditorController
- Enhanced error handling in LayoutEditorController to handle missing database tables
- Created create_layouts_table.php script to manually create the layouts table if needed
- Added default header and footer content for new installations

## [1.7.1] - 2024-08-20

### Fixed
- Fixed issue where header and footer editor buttons in the branding page were not working
- Enhanced URL routing to properly handle controllers with underscores in their names (e.g., layout_editor)
- Improved controller name resolution in the routing system
- Updated App.php to convert underscored controller names to proper CamelCase format

## [1.7.0] - 2024-08-15

### Added
- Added visual footer editor with drag-and-drop interface
- Implemented comprehensive image editor with multiple tools:
  - Crop tool with aspect ratio controls
  - Resize tool with quality preservation
  - Rotate tool with preview functionality
  - Filter application with real-time preview
  - Text overlay tool with font customization
  - Drawing tool with multiple shapes and colors
  - Image optimization tool with quality controls
- Added form designer for show registration with drag-and-drop interface
- Implemented preview functionality for all visual editors
- Enhanced user experience with interactive design tools
- Added support for custom form fields in registration forms
- Improved image management across the application

## [1.6.9] - 2024-08-10

### Added
- Enhanced white-label capabilities with header and footer editor
- Added drag-and-drop interface for header and footer customization
- Implemented WYSIWYG editor for header and footer content
- Added comprehensive image editing functionality (crop, resize, filters, rotation, text overlay, drawing tools)
- Made image editing capabilities reusable across the entire site
- Added image management for shows
- Implemented drag-and-drop form designer for show registration
- Added WYSIWYG editor for form fields with live preview
- Implemented customizable fields for show registration forms
- Added TinyMCE integration for rich text editing
- Enhanced vehicle registration with drag-and-drop form designer

## [1.6.8] - 2024-08-01

### Fixed
- Fixed issue where clicking "View details" button on any show redirected to /home/<USER>
- Fixed ShowController::view method to properly handle show ID parameter
- Fixed method signature compatibility issue with parent Controller class
- Enhanced ShowController::view to handle both show IDs and view paths
- Added additional logging to better track show view requests
- Improved error handling in ShowController::view method
- Removed "Direct View" button from show listings as it's no longer needed for troubleshooting

## [1.6.7] - 2024-07-30

### Fixed
- Fixed routing issue where normal links and query string links redirected to /home/<USER>
- Improved URL parsing in App.php to handle edge cases and provide better fallback routes
- Enhanced ShowController with additional logging for better debugging
- Fixed "users table not found" error in show_debug.php by adding automatic table creation
- Added functionality to create missing users table with default admin user
- Improved error handling and debugging in core application files
- Enhanced database connection error handling and reporting
- Fixed syntax error in show_debug.php that caused a parse error
- Added asset file handling to prevent routing errors for JS, CSS, and image files
- Updated .htaccess to exclude asset files from URL rewriting

## [1.6.6] - 2024-07-28

### Fixed
- Fixed issue where clicking "View Details" on a car show redirected to /home/<USER>
- Enhanced ShowController with better error handling and debugging
- Improved ShowModel's getShowById method with validation and error logging
- Fixed isRegistrationOpen method to properly handle date validation
- Added diagnostic scripts (show_diagnostic.php and show_debug.php) to troubleshoot show viewing issues
- Added special handling to allow admins and coordinators to view unpublished shows
- Completely rewrote App.php routing with robust error handling and debugging
- Enhanced URL parsing to handle edge cases and provide better error messages
- Added DEBUG_MODE constant to enable detailed error logging
- Added database table existence checking to prevent errors with missing tables
- Modified ShowModel to work even when users or registrations tables don't exist
- Added db_fix.php script to automatically create missing tables and sample data
- Updated show/view.php to handle NULL coordinator names
- Created show_direct.php for direct show viewing without MVC framework
- Added simplified ShowController_new.php that doesn't rely on other models
- Added direct view links to show listings for easier troubleshooting

## [1.6.5] - 2024-07-27

### Fixed
- Improved default payment methods creation process
- Enhanced overall error handling and reporting in database scripts

## [1.6.4] - 2024-07-25

### Added
- Added missing RegistrationController to handle show registrations
- Created registration/register.php view for the registration form
- Created registration/cancel.php view for cancelling registrations
- Fixed issue where users couldn't register for shows due to missing controller

### Fixed
- Fixed "Unable to register for a show" issue by implementing the missing registration functionality
- Ensured proper validation of registration data
- Added support for vehicle selection, category selection, and payment method selection during registration

## [1.6.3] - 2024-07-23

### Added
- Added is_active, requires_judging, and display_order columns to show_categories table
- Enhanced category management with activation toggle and display order settings
- Added update script (update_1.6.3.php) to update database schema

### Fixed
- Fixed "Undefined property: stdClass::$is_active" warning in edit_category.php
- Updated ShowModel and AdminController to handle new category properties
- Improved category editing interface with proper default values

## [1.6.2] - 2024-07-22

### Fixed
- Fixed non-functioning edit and delete buttons for categories and metrics on admin/editShow page
- Added missing modal dialogs for delete confirmations
- Implemented missing controller methods for editing and deleting categories and metrics
- Added proper redirects back to the edit show page after operations

## [1.6.1] - 2024-07-21

### Fixed
- Fixed judging metrics not showing on admin/editShow page
- Fixed fatal error caused by calling undefined method getCategories() in ShowModel
- Updated AdminController to use the correct method getShowCategories()
- Improved show editing interface to properly display categories and metrics

## [1.6.0] - 2024-07-20

### Added
- Implemented branding settings throughout the application
- Added site logo display in the header navigation
- Added favicon support from branding settings
- Applied custom colors from branding settings to the site theme
- Added custom CSS support from branding settings
- Implemented white-labeling with custom footer text

### Fixed
- Fixed 403 Forbidden error when uploading images on the branding page
- Updated .htaccess files in uploads directories to properly allow image access
- Improved directory permissions handling for uploads
- Enhanced error handling for file uploads

## [1.5.0] - 2024-07-15

### Added
- Fan favorite voting toggle feature for shows
- Added fan_voting_enabled column to shows table
- Show administrators can now enable or disable fan favorite voting for each show
- Fan favorite voting status is displayed on show details page
- Updated admin and coordinator interfaces to include fan voting toggle option

### Fixed
- Fixed site logo upload functionality on branding page
- Fixed directory structure for file uploads
- Added missing registrationModel in ShowController

## [1.4.0] - 2024-07-10

### Fixed
- Fixed "Error: Method not found: index" when clicking on `/admin/addCategory/1` or `/admin/addMetric/1`
- Fixed routing issue in App.php that was causing incorrect method detection
- Updated URL parsing to correctly handle controller methods and parameters
- Fixed redirect loop issue by properly reindexing URL parameters array after unsetting elements
- Added array_values() to reindex arrays after unsetting URL segments to prevent redirect loops
- Fixed "Undefined property: stdClass::$date" in judging_metrics.php and age_weights_shows.php
- Fixed "Deprecated: strtotime(): Passing null to parameter #1" warnings in multiple files
- Fixed "Undefined variable" warnings in user/registrations/index.php
- Fixed "Unknown column 'r.user_id'" SQL error in getUserCompletedShows method
- Corrected SQL queries to use v.owner_id instead of non-existent r.user_id column
- Added missing variables for registration statistics in UserController
- Added getUserCompletedShows method to ShowModel to display user awards
- Updated date handling to properly check for null values before using strtotime()
- Improved error handling for method not found scenarios

## [1.3.0] - 2023-12-20

### Added
- Created missing admin views for show categories management
- Added show_categories.php view for managing show categories
- Added add_category.php view for adding new categories
- Added edit_category.php view for editing existing categories
- Created missing admin views for registration management
- Added registrations.php view for managing show registrations
- Added view_registration.php view for viewing registration details
- Created missing admin views for judging management
- Added judging.php view for managing show judging and awards
- Added add_age_weight.php view for adding age weights
- Added edit_age_weight.php view for editing age weights
- Created missing user views for registration management
- Added user/registrations/index.php view for users to manage their registrations
- Added user/registrations/view.php view for users to view registration details
- Added user/vehicles/images.php view for users to manage vehicle images

### Fixed
- Fixed "Error: Method not found: index" by adding missing index methods to controllers
- Added index method to UserController that redirects to dashboard
- Added index method to JudgeController that redirects to dashboard
- Added index method to CoordinatorController that redirects to dashboard
- Added index method to AuthController that redirects to login
- Added index method to AdminController that redirects to dashboard
- Fixed "Error: Method not found: index" when clicking on Add Category or Add Metric from edit show page
- Added addCategory method to AdminController to handle adding categories
- Added addMetric method to AdminController that redirects to addJudgingMetric

### Fixed
- Fixed undefined property: stdClass::$date in age_weights.php on line 47
- Fixed deprecated strtotime() with null parameter in age_weights.php
- Enhanced date handling in show details display to properly check for null values
- Fixed fatal error: Access level to AdminController::sanitizeInput() must be protected (as in class Controller) or weaker

## [1.2.9] - 2023-12-15

### Added
- Created missing show management views in admin panel
- Added 'admin/shows/index.php' view for listing all shows
- Added 'admin/shows/add.php' view for adding new shows
- Added 'admin/shows/edit.php' view for editing existing shows
- Implemented complete user interface for show management
- Added update script (update_1.2.9.php) to fix database and code issues

### Fixed
- Fixed "The requested view 'admin/shows/add' could not be found" error
- Added missing show management interface components
- Fixed deprecated FILTER_SANITIZE_STRING usage in AdminController
- Added missing columns to shows table (registration_fee, is_free, listing_fee, listing_paid)
- Fixed undefined property $show->date in age_weights.php view
- Fixed deprecated strtotime() with null parameter in age_weights.php

## [1.2.8] - 2023-11-30

### Added
- Created admin settings view
- Added system information display in settings
- Implemented maintenance options in admin settings
- Added database backup functionality

### Fixed
- Fixed "The requested view 'admin/settings' could not be found" error

## [1.2.7] - 2023-11-25

### Added
- Created missing user dashboard view
- Added user profile view
- Created vehicle management views (index, add, edit)
- Implemented complete user interface for vehicle management

### Fixed
- Fixed "The requested view 'user/dashboard' could not be found" error
- Added missing user interface components for regular users

## [1.2.6] - 2023-11-20

### Fixed
- Fixed user creation issue in admin panel
- Aligned password validation requirements between Auth class and AdminController
- Added diagnostic test script for user creation
- Enhanced error logging for troubleshooting user creation issues

## [1.2.5] - 2023-11-15

### Fixed
- Added missing view files for user management in admin panel
- Created missing 'admin/users/add.php' view file
- Created missing 'admin/users/edit.php' view file
- Fixed error when trying to add or edit users

## [1.2.4] - 2023-11-01

### Fixed
- Added missing 'profile_image' column to users table
- Fixed error when accessing admin dashboard related to profile_image column
- Updated database schema to ensure all required columns are present

## [1.2.3] - 2023-10-15

### Fixed
- Enhanced CSRF token validation for improved security
- Added CSRF token support for AJAX requests
- Unified CSRF implementation across the application
- Added meta tag for CSRF token in header
- Updated JavaScript to automatically include CSRF tokens in AJAX requests
- Fixed potential security issues with form submissions
- Fixed helper file loading in index.php
- Fixed method signature compatibility in ShowController
- Added error handling for missing 'last_login' column in users table
- Added SQL script to add 'last_login' column to users table

## [1.2.2] - 2023-10-01

### Fixed
- Fixed potential security issues in database connection handling
- Improved error handling in core application files
- Enhanced path handling for better cross-platform compatibility
- Updated installer and updater to support the latest version
- Fixed issues with CSRF token validation in forms
- Improved database backup functionality in the updater
- Enhanced security for file uploads and user authentication

## [1.2.1] - 2023-09-15

### Fixed
- Fixed HTTP 500 error on the main page by implementing robust path handling
- Added fallback mechanisms for determining application root directory
- Made file path handling more flexible to work in different server environments
- Fixed path issues in App.php when loading controller files
- Fixed path issues in Controller.php when loading models and views
- Added missing view files (auth/register.php) and improved error handling
- Implemented robust include mechanisms in view files to handle different server environments
- Improved application path handling for more reliable file inclusion
- Fixed installer to create a more complete config.php file with all necessary settings
- Updated config.php to ensure compatibility with the application structure

## [1.2.0] - 2023-09-01

### Fixed
- Fixed installer issue where the application would incorrectly detect as already installed
- Fixed installer not proceeding past step 3 when using the force parameter
- Improved error handling and debugging in the installation process
- Enhanced installer to properly maintain the force parameter between steps

### Changed
- Updated installation process to better handle existing installations
- Improved admin user creation during installation

## [1.1.0] - 2023-08-15

### Added
- Enhanced payment processing system with multiple payment options
- Support for PayPal, CashApp, and Venmo payment methods
- Free event option for shows with no registration fee
- Show listing fee functionality based on user role permissions
- Admin payment management interface
- Payment approval workflow for manual payment methods
- Payment settings configuration in admin dashboard

### Changed
- Improved registration process with clearer payment options
- Enhanced show creation workflow to include payment settings
- Updated database schema to support new payment features

### Fixed
- Registration fee calculation issues
- Payment status tracking and reporting
- User permission checks for payment-related actions

## [1.0.0] - 2023-07-15

### Added
- Initial release of the Events and Shows Management System
- User authentication system with role-based access control
- Facebook login integration
- Vehicle management with image uploads
- Show management with categories and judging criteria
- Registration system with payment tracking
- Judging system with scoring and results calculation
- Public pages for shows, vehicles, and results
- Fan voting system for public choice awards
- Admin dashboard for system management
- Coordinator dashboard for show management
- Judge dashboard for scoring vehicles
- User dashboard for managing vehicles and registrations

### Security
- CSRF protection for all forms
- Password hashing using modern algorithms
- Input sanitization and validation
- Role-based access control for all pages

## [0.9.0] - 2023-06-30

### Added
- Beta release for testing
- All core functionality implemented
- Basic styling and responsive design

### Changed
- Improved performance of vehicle listing pages
- Enhanced security for authentication system

### Fixed
- Registration date validation issues
- Image upload error handling

## [0.8.0] - 2023-06-15

### Added
- Alpha release for internal testing
- Basic functionality for all main features
- Preliminary database schema

### Known Issues
- Performance issues with large vehicle listings
- Mobile responsiveness needs improvement
- Some validation errors in registration process