<?php
/**
 * Debug Push Notifications
 * 
 * This script helps diagnose push notification issues
 */

// Initialize the system
require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Push Notification Debug</h1>\n";

try {
    $db = new Database();
    
    echo "<h2>1. VAPID Configuration</h2>\n";
    
    if (defined('VAPID_PUBLIC_KEY') && !empty(VAPID_PUBLIC_KEY)) {
        echo "<p style='color: green;'>✓ VAPID Public Key: " . substr(VAPID_PUBLIC_KEY, 0, 20) . "...</p>\n";
    } else {
        echo "<p style='color: red;'>❌ VAPID Public Key not configured</p>\n";
    }
    
    if (defined('VAPID_PRIVATE_KEY') && !empty(VAPID_PRIVATE_KEY)) {
        echo "<p style='color: green;'>✓ VAPID Private Key: " . substr(VAPID_PRIVATE_KEY, 0, 20) . "...</p>\n";
    } else {
        echo "<p style='color: red;'>❌ VAPID Private Key not configured</p>\n";
    }
    
    echo "<h2>2. Database Tables</h2>\n";
    
    // Check push notification tables
    $tables = [
        'user_push_notifications' => 'Push notifications storage',
        'user_toast_notifications' => 'Toast notifications storage',
        'notification_queue' => 'Email/SMS queue',
        'user_notification_preferences' => 'User preferences'
    ];
    
    foreach ($tables as $table => $description) {
        $db->query("SHOW TABLES LIKE '$table'");
        $result = $db->single();
        
        if ($result) {
            echo "<p style='color: green;'>✓ $table - $description</p>\n";
            
            // Get count
            $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $db->single();
            echo "<p style='margin-left: 20px;'>Records: " . $count->count . "</p>\n";
        } else {
            echo "<p style='color: red;'>❌ $table missing - $description</p>\n";
        }
    }
    
    echo "<h2>3. User Notification Preferences</h2>\n";
    
    $db->query("SELECT 
                    COUNT(*) as total_users,
                    SUM(push_notifications) as push_enabled,
                    SUM(email_notifications) as email_enabled,
                    SUM(toast_notifications) as toast_enabled
                FROM user_notification_preferences");
    $prefs = $db->single();
    
    if ($prefs) {
        echo "<ul>\n";
        echo "<li>Total users with preferences: " . $prefs->total_users . "</li>\n";
        echo "<li>Push notifications enabled: " . $prefs->push_enabled . "</li>\n";
        echo "<li>Email notifications enabled: " . $prefs->email_enabled . "</li>\n";
        echo "<li>Toast notifications enabled: " . $prefs->toast_enabled . "</li>\n";
        echo "</ul>\n";
    }
    
    echo "<h2>4. Recent Push Notifications</h2>\n";
    
    $db->query("SELECT 
                    upn.*,
                    u.name as user_name
                FROM user_push_notifications upn
                LEFT JOIN users u ON upn.user_id = u.id
                ORDER BY upn.created_at DESC
                LIMIT 5");
    $recent = $db->resultSet();
    
    if ($recent && count($recent) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>User</th><th>Title</th><th>Message</th><th>Created</th><th>Read</th></tr>\n";
        
        foreach ($recent as $notification) {
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($notification->user_name ?? 'Unknown') . "</td>\n";
            echo "<td>" . htmlspecialchars($notification->title) . "</td>\n";
            echo "<td>" . htmlspecialchars(substr($notification->message, 0, 50)) . "...</td>\n";
            echo "<td>" . $notification->created_at . "</td>\n";
            echo "<td>" . ($notification->is_read ? 'Yes' : 'No') . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    } else {
        echo "<p style='color: orange;'>⚠ No recent push notifications found</p>\n";
    }
    
    echo "<h2>5. Service Worker Check</h2>\n";
    
    $swFiles = [
        'firebase-messaging-sw.js' => 'Firebase messaging service worker',
        'sw.js' => 'Main service worker',
        'public/js/notifications.js' => 'Notification JavaScript'
    ];
    
    foreach ($swFiles as $file => $description) {
        if (file_exists(__DIR__ . '/' . $file)) {
            echo "<p style='color: green;'>✓ $file - $description</p>\n";
        } else {
            echo "<p style='color: red;'>❌ $file missing - $description</p>\n";
        }
    }
    
    echo "<h2>6. PWA Controller Check</h2>\n";
    
    if (file_exists(__DIR__ . '/controllers/PWAController.php')) {
        echo "<p style='color: green;'>✓ PWAController.php exists</p>\n";
        
        // Check if VAPID endpoint works
        echo "<p><a href='/pwa/getVapidKey' target='_blank'>Test VAPID Key Endpoint</a></p>\n";
    } else {
        echo "<p style='color: red;'>❌ PWAController.php missing</p>\n";
    }
    
    echo "<h2>Recommendations</h2>\n";
    
    if (!defined('VAPID_PUBLIC_KEY') || empty(VAPID_PUBLIC_KEY)) {
        echo "<p style='color: red;'>🔧 Generate VAPID keys and add to config.php</p>\n";
    }
    
    echo "<ul>\n";
    echo "<li>Check browser console for JavaScript errors</li>\n";
    echo "<li>Verify service worker registration</li>\n";
    echo "<li>Test push subscription in browser dev tools</li>\n";
    echo "<li>Check if notifications are blocked in browser settings</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Debug failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>\n";
?>