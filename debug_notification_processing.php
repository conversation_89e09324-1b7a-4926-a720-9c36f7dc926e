<?php
/**
 * Debug Notification Processing
 * 
 * Deep dive into why pending emails aren't being processed
 */

require_once 'config/config.php';
require_once 'core/Database.php';

// Load required models
require_once APPROOT . '/models/NotificationModel.php';
require_once APPROOT . '/models/NotificationService.php';
require_once APPROOT . '/models/EmailService.php';
require_once APPROOT . '/models/SettingsModel.php';

echo "<h1>🔍 Debug Notification Processing</h1>";

try {
    $db = new Database();
    
    echo "<h2>📊 Detailed Pending Email Analysis</h2>";
    
    // Get detailed info about pending emails
    $db->query("SELECT * FROM notification_queue WHERE notification_type = 'email' AND status = 'pending' ORDER BY created_at DESC");
    $pendingEmails = $db->resultSet();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📋 Pending Email Details:</h3>";
    
    if (empty($pendingEmails)) {
        echo "<p style='color: green;'>✅ No pending emails found</p>";
    } else {
        echo "<p><strong>Found " . count($pendingEmails) . " pending email(s)</strong></p>";
        
        echo "<table border='1' cellpadding='5' style='width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>User ID</th><th>Email</th><th>Subject</th><th>Scheduled For</th><th>Created</th><th>Attempts</th>";
        echo "</tr>";
        
        foreach ($pendingEmails as $email) {
            // Get user email
            $db->query("SELECT email, name FROM users WHERE id = ?");
            $db->bind(1, $email->user_id);
            $db->execute();
            $user = $db->single();
            $userEmail = $user->email ?? 'Unknown';
            $userName = $user->name ?? 'Unknown';
            
            echo "<tr>";
            echo "<td>{$email->id}</td>";
            echo "<td>{$email->user_id}</td>";
            echo "<td>" . htmlspecialchars($userEmail) . "</td>";
            echo "<td>" . htmlspecialchars(substr($email->subject, 0, 50)) . "...</td>";
            echo "<td>{$email->scheduled_for}</td>";
            echo "<td>{$email->created_at}</td>";
            echo "<td>{$email->attempts}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
    echo "<h2>🔧 NotificationService Analysis</h2>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>📱 Step-by-Step Processing Test:</h3>";
    
    if (!empty($pendingEmails)) {
        $testEmail = $pendingEmails[0]; // Test the first pending email
        
        echo "<h4>🎯 Testing Email ID: {$testEmail->id}</h4>";
        
        // Test 1: Check if NotificationService can be created
        echo "<p><strong>Step 1: Creating NotificationService...</strong></p>";
        try {
            $notificationService = new NotificationService();
            echo "<p style='color: green;'>✅ NotificationService created successfully</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to create NotificationService: " . htmlspecialchars($e->getMessage()) . "</p>";
            throw $e;
        }
        
        // Test 2: Check if it can get pending notifications
        echo "<p><strong>Step 2: Getting pending notifications...</strong></p>";
        try {
            // We need to access the NotificationModel to test getPendingNotifications
            $notificationModel = new NotificationModel();
            $pendingNotifications = $notificationModel->getPendingNotifications(5);
            echo "<p style='color: green;'>✅ Found " . count($pendingNotifications) . " pending notifications via NotificationModel</p>";
            
            if (!empty($pendingNotifications)) {
                echo "<table border='1' cellpadding='5'>";
                echo "<tr style='background: #f0f0f0;'><th>ID</th><th>Type</th><th>User Email</th><th>Subject</th></tr>";
                foreach ($pendingNotifications as $notification) {
                    echo "<tr>";
                    echo "<td>{$notification->id}</td>";
                    echo "<td>{$notification->notification_type}</td>";
                    echo "<td>" . htmlspecialchars($notification->email ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars(substr($notification->subject ?? '', 0, 30)) . "...</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to get pending notifications: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Test 3: Check global notification settings
        echo "<p><strong>Step 3: Checking global notification settings...</strong></p>";
        try {
            $globalSettings = $notificationModel->getNotificationSettings();
            echo "<table border='1' cellpadding='5'>";
            echo "<tr style='background: #f0f0f0;'><th>Setting</th><th>Value</th></tr>";
            foreach ($globalSettings as $key => $value) {
                $displayValue = is_bool($value) ? ($value ? 'true' : 'false') : $value;
                echo "<tr><td>{$key}</td><td>{$displayValue}</td></tr>";
            }
            echo "</table>";
            
            if (!$globalSettings['email_enabled']) {
                echo "<p style='color: red;'>❌ <strong>ISSUE FOUND:</strong> Email notifications are disabled globally!</p>";
            } else {
                echo "<p style='color: green;'>✅ Email notifications are enabled globally</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to get notification settings: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Test 4: Check user notification preferences
        echo "<p><strong>Step 4: Checking user notification preferences...</strong></p>";
        try {
            $db->query("SELECT * FROM user_notification_preferences WHERE user_id = ?");
            $db->bind(1, $testEmail->user_id);
            $db->execute();
            $userPrefs = $db->single();
            
            if ($userPrefs) {
                echo "<table border='1' cellpadding='5'>";
                echo "<tr style='background: #f0f0f0;'><th>Setting</th><th>Value</th></tr>";
                echo "<tr><td>Email Notifications</td><td>" . ($userPrefs->email_notifications ? 'Enabled' : 'Disabled') . "</td></tr>";
                echo "<tr><td>Push Notifications</td><td>" . ($userPrefs->push_notifications ? 'Enabled' : 'Disabled') . "</td></tr>";
                echo "<tr><td>Toast Notifications</td><td>" . ($userPrefs->toast_notifications ? 'Enabled' : 'Disabled') . "</td></tr>";
                echo "<tr><td>SMS Notifications</td><td>" . ($userPrefs->sms_notifications ? 'Enabled' : 'Disabled') . "</td></tr>";
                echo "</table>";
                
                if (!$userPrefs->email_notifications) {
                    echo "<p style='color: red;'>❌ <strong>ISSUE FOUND:</strong> User has email notifications disabled!</p>";
                } else {
                    echo "<p style='color: green;'>✅ User has email notifications enabled</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No user notification preferences found (will use defaults)</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to get user preferences: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Test 5: Check EmailService configuration
        echo "<p><strong>Step 5: Checking EmailService configuration...</strong></p>";
        try {
            $emailService = new EmailService();
            $isConfigured = $emailService->isConfigured();
            
            if ($isConfigured) {
                echo "<p style='color: green;'>✅ EmailService is properly configured</p>";
            } else {
                echo "<p style='color: red;'>❌ <strong>ISSUE FOUND:</strong> EmailService is not configured!</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to check EmailService: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Test 6: Manual processing attempt
        echo "<p><strong>Step 6: Manual processing attempt...</strong></p>";
        echo "<form method='post'>";
        echo "<button type='submit' name='manual_process' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Process This Email Manually</button>";
        echo "</form>";
        
        if (isset($_POST['manual_process'])) {
            echo "<h4>🔄 Manual Processing Results:</h4>";
            try {
                // Try to process just this one email manually
                $results = $notificationService->processPendingNotifications(1);
                
                echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Results:</strong><br>";
                echo "Processed: {$results['processed']}<br>";
                echo "Sent: {$results['sent']}<br>";
                echo "Failed: {$results['failed']}<br>";
                
                if (!empty($results['errors'])) {
                    echo "<strong>Errors:</strong><br>";
                    foreach ($results['errors'] as $error) {
                        echo "• " . htmlspecialchars($error) . "<br>";
                    }
                }
                echo "</div>";
                
                // Check if the email is still pending
                $db->query("SELECT status FROM notification_queue WHERE id = ?");
                $db->bind(1, $testEmail->id);
                $db->execute();
                $updatedEmail = $db->single();
                
                if ($updatedEmail) {
                    echo "<p><strong>Email status after processing:</strong> {$updatedEmail->status}</p>";
                } else {
                    echo "<p style='color: red;'>❌ Could not find email after processing</p>";
                }
                
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px;'>";
                echo "<strong>Processing Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "<br><strong>Stack Trace:</strong><br>";
                echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
                echo "</div>";
            }
        }
        
    } else {
        echo "<p>No pending emails to test</p>";
    }
    echo "</div>";
    
    echo "<h2>🔍 Possible Issues</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🎯 Common Reasons for 0 Processed:</h3>";
    echo "<ol>";
    echo "<li><strong>Global email notifications disabled</strong> - Check notification_settings table</li>";
    echo "<li><strong>User email notifications disabled</strong> - Check user_notification_preferences table</li>";
    echo "<li><strong>EmailService not configured</strong> - Check SMTP settings</li>";
    echo "<li><strong>Scheduled time in future</strong> - Check scheduled_for column</li>";
    echo "<li><strong>Missing user email address</strong> - Check users table</li>";
    echo "<li><strong>Database query issues</strong> - Check JOIN conditions</li>";
    echo "<li><strong>Exception thrown during processing</strong> - Check error logs</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ Debug Error</h2>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><em>Notification processing debug completed at: " . date('Y-m-d H:i:s') . "</em></p>";
?>
